<template>
  <div id="offer-manager-component" class="offer-manager">
    <PageHeader title="Usuários matriculados">
      <template #actions>
        <BackButton @click="goBack" />
      </template>
    </PageHeader>

    <!-- <PERSON><PERSON><PERSON><PERSON> da página e botão de Matricular novo usuário -->
    <div
      style="display: flex; align-items: center; margin-bottom: 20px; gap: 10px"
    >
      <div style="width: 240px">
        <HierarchicalSelect
          v-model="selectedPageView"
          :options="pageViewOptions"
          @navigate="handlePageViewChange"
        />
      </div>
      <CustomButton
        v-if="!classDetails || classDetails?.operational_cycle !== 2"
        variant="primary"
        label="Matricular usuários"
        @click="addNewUser"
      />
    </div>

    <!-- Filtros -->
    <FilterSection :has-active-tags="hasActiveFilters" :title="''">
      <FilterRow :inline="true">
        <FilterGroup label="Filtrar por nome">
          <Autocomplete
            v-model="selectedName"
            :items="filteredNameOptions"
            placeholder="Buscar..."
            :width="'100%'"
            @select="handleNameSelectNoFilter"
            :has-search-icon="false"
            class="custom-autocomplete"
          />
        </FilterGroup>

        <FilterGroup label="Filtrar por CPF">
          <Autocomplete
            v-model="selectedCpf"
            :items="filteredCpfOptions"
            placeholder="Buscar..."
            :width="'100%'"
            @select="handleCpfSelectNoFilter"
            :has-search-icon="false"
            class="custom-autocomplete"
          />
        </FilterGroup>

        <FilterGroup label="Filtrar por E-mail">
          <Autocomplete
            v-model="selectedEmail"
            :items="filteredEmailOptions"
            placeholder="Buscar..."
            :width="'100%'"
            @select="handleEmailSelectNoFilter"
            :has-search-icon="false"
            class="custom-autocomplete"
          />
        </FilterGroup>

        <FilterGroup>
          <div class="filter-buttons">
            <CustomButton
              variant="primary"
              label="Filtrar"
              @click="applyFilters"
            />
            <CustomButton
              variant="secondary"
              label="Limpar"
              @click="clearFilters"
            />
          </div>
        </FilterGroup>
      </FilterRow>

      <!-- Tags de filtro -->
      <template #tags>
        <FilterTags>
          <!-- Tags para filtros de nome -->
          <FilterTag
            v-for="(filter, index) in appliedFilters.name"
            :key="'name-' + index"
            @remove="removeFilter('name', index)"
          >
            Nome: {{ filter.label }}
          </FilterTag>

          <!-- Tags para filtros de CPF -->
          <FilterTag
            v-for="(filter, index) in appliedFilters.cpf"
            :key="'cpf-' + index"
            @remove="removeFilter('cpf', index)"
          >
            CPF: {{ filter.label }}
          </FilterTag>

          <!-- Tags para filtros de e-mail -->
          <FilterTag
            v-for="(filter, index) in appliedFilters.email"
            :key="'email-' + index"
            @remove="removeFilter('email', index)"
          >
            E-mail: {{ filter.label }}
          </FilterTag>
        </FilterTags>
      </template>
    </FilterSection>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <!-- Tabela -->
    <div class="table-container">
      <CustomTable
        :headers="tableHeaders"
        :items="enrolments"
        :sort-by="sortBy"
        :sort-desc="sortDesc"
        @sort="handleTableSort"
      >
        <template #header-select>
          <div class="checkbox-container">
            <input
              type="checkbox"
              :checked="allSelected"
              :indeterminate="someSelected && !allSelected"
              @change="toggleSelectAll"
              class="custom-checkbox"
            />
          </div>
        </template>
        <template #item-select="{ item }">
          <div class="checkbox-container">
            <input
              type="checkbox"
              :checked="isSelected(item.id)"
              @change="toggleSelectUser(item.id)"
              class="custom-checkbox"
            />
          </div>
        </template>
        <template #item-fullName="{ item }">
          <a
            class="user-name-container"
            :href="`/user/view.php?id=${item.id}`"
            :title="'Ver perfil de ' + item.fullName"
          >
            <UserAvatar :full-name="item.fullName" :size="36" />
            <span class="user-name-link">{{ item.fullName }}</span>
          </a>
        </template>
        <template #item-email="{ item }">
          {{ item.email }}
        </template>
        <template #item-cpf="{ item }">
          {{ item.cpf }}
        </template>
        <template #item-roles="{ item }">
          <RoleSelector
            :userId="item.id"
            :offeruserenrolid="item.offeruserenrolid"
            :currentRole="item.roles"
            :offerclassid="parseInt(offerclassid || $route.params.offerclassid)"
            @success="handleRoleUpdateSuccess"
            @error="handleRoleUpdateError"
            @reload-table="reloadTable"
          />
        </template>
        <template #item-groups="{ item }">
          {{ item.groups }}
        </template>
        <template #item-startDate="{ item }">
          {{ item.startDate }}
        </template>
        <template #item-endDate="{ item }">
          {{ item.endDate }}
        </template>
        <template #item-deadline="{ item }">
          {{ item.deadline }}
        </template>
        <template #item-progress="{ item }">
          <div class="progress-container">
            <div class="progress-bar" :style="{ width: item.progress }"></div>
            <span class="progress-text">{{ item.progress }}</span>
          </div>
        </template>
        <template #item-status="{ item }">
          {{ item.statusName }}
        </template>
        <template #item-grade="{ item }">
          {{ item.grade }}
        </template>
        <template #item-state="{ item }">
          <div class="state-container">
            <span
              class="state-tag badge"
              :class="item.state === 0 ? 'badge-success' : 'badge-danger'"
            >
              {{ item.stateName }}
            </span>

            <div class="state-actions">
              <button
                class="btn-information"
                @click="showEnrollmentDetails(item)"
                title="Informações da matrícula"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="custom-icon"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
              </button>
              <button
                class="btn-settings"
                @click="editUser(item)"
                title="Editar matrícula"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="custom-icon"
                >
                  <circle cx="12" cy="12" r="3"></circle>
                  <path
                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </template>
      </CustomTable>
    </div>

    <!-- Paginação -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalEnrolments"
      :loading="loading"
    />

    <!-- Ações para usuários selecionados - sempre visível -->
    <div class="selected-users-actions">
      <div class="bulk-actions-container">
        <label for="bulk-actions">Com usuários selecionados...</label>
        <select
          id="bulk-actions"
          class="form-control bulk-select"
          v-model="selectedBulkAction"
          @change="handleBulkAction"
        >
          <option value="">Escolher...</option>
          <optgroup label="Comunicação">
            <option value="message">Enviar uma mensagem</option>
            <option value="note">Escrever uma nova anotação</option>
          </optgroup>
          <optgroup label="Baixar dados da tabela como:">
            <option value="download_csv">
              Valores separados por vírgula (.csv)
            </option>
            <option value="download_xlsx">Microsoft excel (.xlsx)</option>
            <option value="download_html">Tabela HTML</option>
            <option value="download_json">
              JavaScript Object Notation (.json)
            </option>
            <option value="download_ods">OpenDocument (.ods)</option>
            <option value="download_pdf">
              Formato de documento portável (.pdf)
            </option>
          </optgroup>
          <optgroup label="Inscrições manuais">
            <option value="edit_enrolment">
              Editar matrículas de usuários selecionados
            </option>
            <option value="delete_enrolment">
              Excluir matrículas de usuários selecionados
            </option>
          </optgroup>
        </select>
      </div>
    </div>

    <!-- Botão de Matricular novo usuário abaixo do select -->
    <div
      class="bottom-enroll-button"
      v-if="!classDetails || classDetails?.operational_cycle !== 2"
    >
      <CustomButton
        variant="primary"
        label="Matricular usuários"
        @click="addNewUser"
      />
    </div>

    <!-- Modal de Detalhes da Matrícula -->
    <EnrollmentDetailsModal
      :show="showEnrollmentModal"
      :user="selectedUser"
      :course-name="classDetails?.course_fullname || ''"
      @close="closeEnrollmentModal"
    />

    <!-- Modal de Matrícula de Usuários -->
    <EnrolmentModalNew
      :show="showEnrolmentModal"
      :offerclassid="parseInt(offerclassid || $route.params.offerclassid)"
      :roles="roleOptions"
      @close="closeEnrolmentModal"
      @success="handleEnrolmentSuccess"
    />

    <EditEnrollmentModal
      :show="showEditEnrollmentModal"
      :user="selectedUser"
      :offerclassid="parseInt(offerclassid || $route.params.offerclassid)"
      @close="closeEditEnrollmentModal"
      @success="handleEditEnrollmentSuccess"
      @error="handleEditEnrollmentError"
    />

    <!-- Modal de Edição de Matrícula -->
    <BulkEditEnrollmentModal
      :show="showBulkEditEnrollmentModal"
      :users="
        selectedUsers
          .map((id) => enrolments.find((offer) => offer.id === id))
          .filter(Boolean)
      "
      :offerclassid="parseInt(offerclassid || $route.params.offerclassid)"
      @close="this.showBulkEditEnrollmentModal = false"
      @success="handleBulkEditEnrollmentSuccess"
      @error="handleBulkEditEnrollmentError"
    />

    <BulkDeleteEnrollmentModal
      :show="showBulkDeleteEnrollmentModal"
      :users="
        selectedUsers
          .map((id) => enrolments.find((offer) => offer.id === id))
          .filter(Boolean)
      "
      :offerclassid="parseInt(offerclassid || $route.params.offerclassid)"
      @close="showBulkDeleteEnrollmentModal = false"
      @confirm="confirmeBulkDeleteEnrollment"
      @error="handleBulkDeleteEnrollmentError"
    />

    <LFLoading :is-loading="loading" />

    <!-- Toast para mensagens -->
    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import pencilFill from "@/assets/img/pencil-fill.svg";

// Importação dos services
import {
  fetchEnrolments,
  getFilterOptions,
  deleteEnrolmentBulk,
} from "@/services/enrolment";

import {
  getClass,
  getClasses,
  getCourseRoles as getOfferCourseRoles,
} from "@/services/offer";
import Toast from "@/components/Toast.vue";

// Importação dos componentes
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import HierarchicalSelect from "@/components/HierarchicalSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import CustomButton from "@/components/CustomButton.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterActions from "@/components/FilterActions.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import EnrolmentModalNew from "@/components/EnrolmentModalNew.vue";
import EnrollmentDetailsModal from "@/components/EnrollmentDetailsModal.vue";
import EditEnrollmentModal from "@/components/EditEnrollmentModal.vue";
import BulkEditEnrollmentModal from "@/components/BulkEditEnrollmentModal.vue";
import BulkDeleteEnrollmentModal from "@/components/BulkDeleteEnrollmentModal.vue";
import BackButton from "@/components/BackButton.vue";
import UserAvatar from "@/components/UserAvatar.vue";
import RoleSelector from "@/components/RoleSelector.vue";
import LFLoading from "@/components/LFLoading.vue";
import { useRouter } from "vue-router";
export default {
  name: "RegisteredUsers",

  props: {
    offerclassid: {
      type: String,
      required: true,
    },
  },

  components: {
    CustomTable,
    CustomSelect,
    HierarchicalSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    FilterTag,
    FilterTags,
    Pagination,
    PageHeader,
    ConfirmationModal,
    Autocomplete,
    EnrolmentModalNew,
    EnrollmentDetailsModal,
    Toast,
    EditEnrollmentModal,
    BulkEditEnrollmentModal,
    BulkDeleteEnrollmentModal,
    BackButton,
    UserAvatar,
    RoleSelector,
    LFLoading,
  },

  setup() {
    const router = useRouter();
    return { router };
  },

  mounted() {
    // Garantir que o Font Awesome esteja carregado com todos os estilos (regular, solid, etc.)
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css";
      document.head.appendChild(link);
    }
  },

  data() {
    return {
      // Ícones SVG
      icons: {
        edit: pencilFill,
      },

      // ID da oferta (será preenchido no método created)
      offerId: null,

      // Filtros de entrada (não aplicados automaticamente)
      inputFilters: {
        name: [],
        cpf: [],
        email: [],
      },

      // Filtros aplicados (usados para filtragem real)
      appliedFilters: {
        name: [],
        cpf: [],
        email: [],
      },

      // Opções para os autocompletes
      nameOptions: [],
      cpfOptions: [],
      emailOptions: [],

      // Valores selecionados nos autocompletes
      selectedName: "",
      selectedCpf: "",
      selectedEmail: "",
      // Armazenar IDs de usuários selecionados por tipo de filtro
      selectedUserIds: {
        name: [],
        cpf: [],
        email: [],
      },

      // Configuração da tabela
      tableHeaders: [
        { text: "", value: "select", sortable: false, width: "50px" },
        {
          text: "NOME/SOBRENOME",
          value: "fullName",
          sortable: true,
          width: "220px",
        },
        { text: "E-MAIL", value: "email", sortable: true },
        { text: "CPF", value: "cpf", sortable: true },
        { text: "PAPÉIS", value: "roles", sortable: false },
        { text: "GRUPOS", value: "groups", sortable: false },
        {
          text: "DATA INÍCIO DA MATRÍCULA",
          value: "startDate",
          sortable: true,
        },
        { text: "DATA FIM DA MATRÍCULA", value: "endDate", sortable: true },
        { text: "PRAZO DE CONCLUSÃO", value: "deadline", sortable: true },
        { text: "PROGRESSO", value: "progress", sortable: false },
        { text: "SITUAÇÃO DE MATRÍCULA", value: "status", sortable: true },
        { text: "NOTA", value: "grade", sortable: false },
        { text: "ESTADO", value: "state", sortable: true },
      ],

      // Estados de carregamento e erro
      enrolments: [],
      totalEnrolments: 0,
      loading: false,
      error: null,

      // Paginação
      currentPage: 1,
      perPage: 10,

      // Ordenação
      sortBy: "fullName",
      sortDesc: false,

      // Modal de exclusão
      showBulkDeleteEnrollmentModal: false,

      // Modal de detalhes da matrícula
      showEnrollmentModal: false,
      selectedUser: null,

      // Modal de matrícula de usuários
      showEnrolmentModal: false,
      roleOptions: [],

      // Modal de edição de matrícula
      showEditEnrollmentModal: false,

      // Modal de edição em lote
      showBulkEditEnrollmentModal: false,

      // Variáveis para o Toast
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,

      // Detalhes da turma
      classDetails: {},

      // Controle de seleção de usuários
      selectedUsers: [],
      selectAll: false,
      selectedBulkAction: "",

      // Opções para o select de título da página
      selectedPageView: "usuarios_matriculados",
      pageViewOptions: [
        {
          value: "matriculas",
          label: "Matrículas",
          children: [
            { value: "usuarios_matriculados", label: "Usuários matriculados" },
          ],
        },
        {
          value: "grupos",
          label: "Grupos",
          children: [
            { value: "grupos", label: "Grupos" },
            { value: "agrupamentos", label: "Agrupamentos" },
            { value: "visao_geral", label: "Visão geral" },
          ],
        },
        {
          value: "permissoes",
          label: "Permissões",
          children: [
            { value: "permissoes", label: "Permissões" },
            { value: "outros_usuarios", label: "Outros usuários" },
            { value: "verificar_permissoes", label: "Verificar permissões" },
          ],
        },
      ],
    };
  },

  computed: {
    hasActiveFilters() {
      return (
        (this.appliedFilters.name && this.appliedFilters.name.length > 0) ||
        (this.appliedFilters.cpf && this.appliedFilters.cpf.length > 0) ||
        (this.appliedFilters.email && this.appliedFilters.email.length > 0)
      );
    },

    // Verifica se todos os usuários estão selecionados
    allSelected() {
      return (
        this.enrolments.length > 0 &&
        this.selectedUsers.length === this.enrolments.length
      );
    },

    // Verifica se alguns usuários estão selecionados
    someSelected() {
      return this.selectedUsers.length > 0 && !this.allSelected;
    },

    // Filtrar opções de nome removendo itens já selecionados
    filteredNameOptions() {
      const selectedValues = this.appliedFilters.name.map(
        (filter) => filter.value
      );
      return this.nameOptions.filter(
        (option) => !selectedValues.includes(option.value)
      );
    },

    // Filtrar opções de CPF removendo itens já selecionados
    filteredCpfOptions() {
      const selectedValues = this.appliedFilters.cpf.map(
        (filter) => filter.value
      );
      return this.cpfOptions.filter(
        (option) => !selectedValues.includes(option.value)
      );
    },

    // Filtrar opções de e-mail removendo itens já selecionados
    filteredEmailOptions() {
      const selectedValues = this.appliedFilters.email.map(
        (filter) => filter.value
      );
      return this.emailOptions.filter(
        (option) => !selectedValues.includes(option.value)
      );
    },
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;
        // Limpar a seleção de usuários quando a quantidade por página é alterada
        this.selectedUsers = [];
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          this.loadRegisteredUsers(classId);
        }
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        // Limpar a seleção de usuários quando a página é alterada
        this.selectedUsers = [];
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          this.loadRegisteredUsers(classId);
        }
      }
    },
  },

  async created() {
    try {
      // Verificar se temos o ID da turma
      const classId = this.offerclassid || this.$route.params.offerclassid;

      if (classId) {
        // Buscar o ID da oferta a partir do ID da turma
        try {
          const response = await getClass(parseInt(classId));
          this.classDetails = response?.data || {};
          // Verificar se a resposta tem a estrutura { error: false, data: [...] }
          if (
            response &&
            response.error === false &&
            Array.isArray(response.data)
          ) {
            // Procurar a turma com o ID correspondente
            const classData = response.data.find(
              (item) => item.id === parseInt(classId)
            );

            if (classData && classData.offerid) {
              this.offerId = classData.offerid;
            } else if (response.data.length > 0 && response.data[0].offerid) {
              this.offerId = response.data[0].offerid;
            }
          } else if (response && response.data && response.data.offercourseid) {
            // Verificar se temos a estrutura com offercourseid

            // Vamos definir o offerId diretamente como 2 para o caso específico
            if (classId === "3") {
              this.offerId = 2;
            }
          } else if (response && response.offercourseid) {
            // Verificar se temos a estrutura com offercourseid diretamente

            // Vamos definir o offerId diretamente como 2 para o caso específico
            if (classId === "3") {
              this.offerId = 2;
            }
          }

          // Solução temporária: definir manualmente o ID da oferta para turmas específicas
          if (classId === "3" && !this.offerId) {
            this.offerId = 2;
          }
        } catch (error) {
          // Solução temporária: definir manualmente o ID da oferta para turmas específicas
          if (classId === "3") {
            this.offerId = 2;
          }
        }

        // Carregar papéis disponíveis
        await this.loadRoles();

        // Carregar opções para os autocompletes
        await this.loadNameOptions();
        await this.loadCpfOptions();
        await this.loadEmailOptions();

        // Carregar usuários matriculados
        await this.loadRegisteredUsers(classId);
      } else {
        this.error = "ID da turma não encontrado";
      }
    } catch (error) {
      this.error = "Erro ao carregar dados iniciais: " + error.message;
    }
  },

  methods: {
    async loadRegisteredUsers(classId) {
      try {
        this.loading = true;
        this.error = null;

        if (!classId) {
          this.error = "ID da turma não fornecido";
          this.loading = false;
          return;
        }

        // Preparar os parâmetros de filtro
        let userids = [];

        // Verificar se temos IDs de usuários selecionados em qualquer filtro
        const hasSelectedUserIds =
          this.selectedUserIds.name.length > 0 ||
          this.selectedUserIds.cpf.length > 0 ||
          this.selectedUserIds.email.length > 0;

        // Se temos IDs de usuários selecionados, usar diretamente
        if (hasSelectedUserIds) {
          // Combinar todos os IDs de usuários selecionados
          const allSelectedIds = [
            ...this.selectedUserIds.name,
            ...this.selectedUserIds.cpf,
            ...this.selectedUserIds.email,
          ];

          // Remover duplicatas
          userids = [...new Set(allSelectedIds)];
        }
        // Se não temos IDs selecionados mas temos filtros aplicados, buscar os IDs dos usuários
        else if (this.hasActiveFilters) {
          try {
            // Coletar todos os IDs de usuários de todos os filtros aplicados
            const allUserIds = [];

            // Processar filtros de nome
            if (
              this.appliedFilters.name &&
              this.appliedFilters.name.length > 0
            ) {
              this.appliedFilters.name.forEach((filter) => {
                if (filter.value && !allUserIds.includes(filter.value)) {
                  allUserIds.push(filter.value);
                }
              });
            }

            // Processar filtros de CPF
            if (this.appliedFilters.cpf && this.appliedFilters.cpf.length > 0) {
              this.appliedFilters.cpf.forEach((filter) => {
                if (filter.value && !allUserIds.includes(filter.value)) {
                  allUserIds.push(filter.value);
                }
              });
            }

            // Processar filtros de e-mail
            if (
              this.appliedFilters.email &&
              this.appliedFilters.email.length > 0
            ) {
              this.appliedFilters.email.forEach((filter) => {
                if (filter.value && !allUserIds.includes(filter.value)) {
                  allUserIds.push(filter.value);
                }
              });
            }

            // Se encontramos IDs de usuários, usá-los para filtrar
            if (allUserIds.length > 0) {
              userids = allUserIds;
            }
          } catch (searchError) {
            // Erro silencioso ao buscar usuários com filtros
          }
        }

        // Agora buscamos as matrículas com os IDs dos usuários (ou todos, se não houver filtro)
        const params = {
          offerclassid: parseInt(classId),
          userids: userids,
          page: this.currentPage,
          perpage: this.perPage,
          orderby: this.mapSortFieldToBackend(this.sortBy || "fullName"),
          direction: this.sortDesc ? "DESC" : "ASC",
        };

        const response = await fetchEnrolments(params);

        if (response && response.data) {
          // Verificar se temos a estrutura aninhada (data.data)
          const responseData = response.data.data || response.data;

          // Verificar se temos a estrutura esperada
          if (Array.isArray(responseData.enrolments)) {
            const deadline = await this.calculateDeadline();
            // Mapear os dados retornados para o formato esperado pela tabela
            this.enrolments = responseData.enrolments.map((enrolment) => ({
              id: enrolment.userid,
              offeruserenrolid: enrolment.offeruserenrolid, // ID da matrícula (importante para exclusão)
              fullName: enrolment.fullname,
              email: enrolment.email,
              cpf: enrolment.cpf,
              enrol: enrolment.enrol,
              roles: this.formatRoles(enrolment.roles),
              groups: enrolment.groups,
              timecreated: enrolment.timecreated,
              createdDate: this.formatDateTime(enrolment.timecreated),
              timestart: enrolment.timestart,
              timeend: enrolment.timeend,
              startDate: this.formatDate(enrolment.timestart),
              endDate: this.formatDate(enrolment.timeend),
              // Usar o método calculateDeadline que agora considera o período da turma específica
              deadline:
                deadline == null
                  ? "Imilitado"
                  : deadline === 1
                    ? "1 dia"
                    : `${deadline} dias`,
              progress: this.formatProgress(enrolment.progress),
              status: enrolment.situation,
              statusName: enrolment.situation_name,
              grade: enrolment.grade || "-",
              state: enrolment.ue_status,
              stateName:
                enrolment.ue_status !== undefined
                  ? enrolment.ue_status === 0
                    ? "Ativo"
                    : "Suspenso"
                  : "-",
            }));

            this.totalEnrolments = responseData.total || this.enrolments.length;
          }
        } else {
          this.enrolments = [];
          this.totalEnrolments = 0;
        }
      } catch (error) {
        this.error = "Erro ao carregar usuários matriculados: " + error.message;
        this.enrolments = [];
        this.totalEnrolments = 0;
      } finally {
        this.loading = false;
      }
    },

    // Métodos auxiliares para formatar os dados
    formatDate(timestamp) {
      if (!timestamp || timestamp === 0) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    formatDateTime(timestamp, options = {}) {
      if (!timestamp || timestamp === 0) return "-";

      if (Object.keys(options).length === 0) {
        options = {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        };
      }
      const date = new Date(timestamp * 1000);
      return date.toLocaleString("pt-BR", options);
    },

    async calculateDeadline() {
      const classId = this.offerclassid || this.$route.params.offerclassid;

      const classDetails = await getClass(parseInt(classId));

      if (classDetails && classDetails.data) {
        const optionalFields = classDetails.data.optional_fields;
        const classEnrolPeriod = optionalFields.enrolperiod;
        const enableEnrolPeriod = optionalFields.enableenrolperiod;
        const enableEndDate = optionalFields.enableenddate;
        const startDateStr = classDetails.data.startdate;
        const endDateStr = optionalFields.enddate;

        let enrolPeriod;

        if (!enableEndDate && !enableEnrolPeriod) {
          return null;
        }

        if (classEnrolPeriod === 0 && enableEnrolPeriod === false) {
          if (startDateStr && endDateStr) {
            const startDate = new Date(startDateStr);
            const endDate = new Date(endDateStr);

            // Calcula a diferença em milissegundos
            const diffTime = endDate - startDate;

            // Converte para dias
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            enrolPeriod = diffDays;
          } else {
            enrolPeriod = null;
          }
        } else {
          enrolPeriod = classEnrolPeriod;
        }

        return enrolPeriod;
      }

      return null;
    },

    formatProgress(progress) {
      if (progress === null || progress === undefined) return "-";
      return Math.round(progress) + "%";
    },

    // Método para formatar os papéis (roles)
    formatRoles(roles) {
      if (!roles || roles === "-") return "-";

      // Se for uma string, dividir por vírgula
      if (typeof roles === "string") {
        return roles
          .split(",")
          .map((role) => this.translateAndCapitalizeRole(role.trim()))
          .join(", ");
      }

      // Se for um array de objetos com propriedade name (formato da API)
      if (
        Array.isArray(roles) &&
        roles.length > 0 &&
        typeof roles[0] === "object" &&
        roles[0].name
      ) {
        return roles.map((role) => role.name).join(", ");
      }

      // Se for um array de outros tipos
      if (Array.isArray(roles)) {
        return roles
          .map((role) => this.translateAndCapitalizeRole(role))
          .join(", ");
      }

      // Para qualquer outro caso, retornar um valor padrão
      return "-";
    },

    // Método auxiliar para capitalizar um papel
    translateAndCapitalizeRole(role) {
      if (!role) return "";

      // Converter para string se não for uma string
      if (typeof role !== "string") {
        try {
          role = String(role);
        } catch (e) {
          return "Papel desconhecido";
        }
      }

      // Apenas capitalizar a primeira letra
      return role.charAt(0).toUpperCase() + role.slice(1);
    },

    // Métodos para carregar opções dos autocompletes
    async loadNameOptions() {
      try {
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (!classId) {
          return;
        }

        // Buscar todos os usuários matriculados para obter seus nomes
        const response = await getFilterOptions({
          offerclassid: parseInt(classId),
          filter_type: "name",
        });

        if (Array.isArray(response)) {
          this.nameOptions = response.map((user) => ({
            value: user.id,
            label: user.fullname,
          }));
        } else {
          this.nameOptions = [];
        }
      } catch (error) {
        this.nameOptions = [];
      }
    },

    async loadCpfOptions() {
      try {
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (!classId) {
          return;
        }

        // Buscar todos os usuários matriculados para obter seus CPFs
        const response = await getFilterOptions({
          offerclassid: parseInt(classId),
          filter_type: "cpf",
        });

        if (Array.isArray(response)) {
          this.cpfOptions = response.map((user) => ({
            value: user.id,
            label: user.cpf,
          }));
        } else {
          this.cpfOptions = [];
        }
      } catch (error) {
        this.cpfOptions = [];
      }
    },

    async loadEmailOptions() {
      try {
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (!classId) {
          return;
        }

        // Buscar todos os usuários matriculados para obter seus e-mails
        const response = await getFilterOptions({
          offerclassid: parseInt(classId),
          filter_type: "email",
        });

        if (Array.isArray(response)) {
          this.emailOptions = response.map((user) => ({
            value: user.id,
            label: user.email,
          }));
        } else {
          this.emailOptions = [];
        }
      } catch (error) {
        this.emailOptions = [];
      }
    },

    // Métodos para manipular a seleção de usuários
    toggleSelectAll() {
      if (this.allSelected) {
        this.selectedUsers = [];
      } else {
        this.selectedUsers = this.enrolments.map((user) => user.id);
      }
    },

    toggleSelectUser(userId) {
      const index = this.selectedUsers.indexOf(userId);
      if (index === -1) {
        this.selectedUsers.push(userId);
      } else {
        this.selectedUsers.splice(index, 1);
      }
    },

    isSelected(userId) {
      return this.selectedUsers.includes(userId);
    },

    // Métodos para lidar com a seleção nos autocompletes sem aplicar filtros automaticamente
    handleNameSelectNoFilter(item) {
      // Verificar se o item já existe no array de filtros
      const existingIndex = this.inputFilters.name.findIndex(
        (filter) => filter.value === item.value
      );

      // Se não existir, adicionar ao array
      if (existingIndex === -1) {
        const filterItem = {
          value: item.value,
          label: item.label,
        };
        this.inputFilters.name.push(filterItem);
        // Atualizar também os filtros aplicados para que as tags apareçam imediatamente
        this.appliedFilters.name.push(filterItem);
      }

      // Limpar o campo de entrada após a seleção
      this.selectedName = "";

      // Adicionar o ID do usuário ao array de IDs selecionados por nome
      if (!this.selectedUserIds.name.includes(item.value)) {
        this.selectedUserIds.name.push(item.value);
      }
    },

    handleCpfSelectNoFilter(item) {
      // Verificar se o item já existe no array de filtros
      const existingIndex = this.inputFilters.cpf.findIndex(
        (filter) => filter.value === item.value
      );

      // Se não existir, adicionar ao array
      if (existingIndex === -1) {
        const filterItem = {
          value: item.value,
          label: item.label,
        };
        this.inputFilters.cpf.push(filterItem);
        // Atualizar também os filtros aplicados para que as tags apareçam imediatamente
        this.appliedFilters.cpf.push(filterItem);
      }

      // Limpar o campo de entrada após a seleção
      this.selectedCpf = "";

      // Adicionar o ID do usuário ao array de IDs selecionados por CPF
      if (!this.selectedUserIds.cpf.includes(item.value)) {
        this.selectedUserIds.cpf.push(item.value);
      }
    },

    handleEmailSelectNoFilter(item) {
      // Verificar se o item já existe no array de filtros
      const existingIndex = this.inputFilters.email.findIndex(
        (filter) => filter.value === item.value
      );

      // Se não existir, adicionar ao array
      if (existingIndex === -1) {
        const filterItem = {
          value: item.value,
          label: item.label,
        };
        this.inputFilters.email.push(filterItem);
        // Atualizar também os filtros aplicados para que as tags apareçam imediatamente
        this.appliedFilters.email.push(filterItem);
      }

      // Limpar o campo de entrada após a seleção
      this.selectedEmail = "";

      // Adicionar o ID do usuário ao array de IDs selecionados por e-mail
      if (!this.selectedUserIds.email.includes(item.value)) {
        this.selectedUserIds.email.push(item.value);
      }
    },

    // Métodos antigos mantidos para compatibilidade
    handleNameSelect(item) {
      this.handleNameSelectNoFilter(item);
      this.applyFilters();
    },

    handleCpfSelect(item) {
      this.handleCpfSelectNoFilter(item);
      this.applyFilters();
    },

    handleEmailSelect(item) {
      this.handleEmailSelectNoFilter(item);
      this.applyFilters();
    },

    // Método removido pois não é mais necessário

    async handlePageChange(page) {
      if (page !== this.currentPage) {
        this.currentPage = page;

        // Obter o ID da turma
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          await this.loadRegisteredUsers(classId);
        } else {
          this.error = "ID da turma não encontrado";
        }
      }
    },

    async handlePerPageChange(newPerPage) {
      if (newPerPage !== this.perPage) {
        this.perPage = newPerPage;
        this.currentPage = 1;

        // Obter o ID da turma
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          await this.loadRegisteredUsers(classId);
        } else {
          this.error = "ID da turma não encontrado";
        }
      }
    },

    async applyFilters() {
      try {
        this.loading = true;
        this.currentPage = 1;

        // Limpar a seleção de usuários quando os filtros são aplicados
        this.selectedUsers = [];

        // Copiar os filtros de entrada para os filtros aplicados
        this.appliedFilters = JSON.parse(JSON.stringify(this.inputFilters));

        // Obter o ID da turma
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          await this.loadRegisteredUsers(classId);
        } else {
          this.error = "ID da turma não encontrado";
        }
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async clearFilters() {
      this.inputFilters = {
        name: [],
        cpf: [],
        email: [],
      };

      this.appliedFilters = {
        name: [],
        cpf: [],
        email: [],
      };

      // Limpar os valores selecionados nos autocompletes
      this.selectedName = "";
      this.selectedCpf = "";
      this.selectedEmail = "";

      // Limpar os IDs de usuários selecionados
      this.selectedUserIds = {
        name: [],
        cpf: [],
        email: [],
      };

      // Limpar a seleção de usuários quando os filtros são limpos
      this.selectedUsers = [];

      this.currentPage = 1;

      // Obter o ID da turma
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        await this.loadRegisteredUsers(classId);
      } else {
        this.error = "ID da turma não encontrado";
      }
    },

    async removeFilter(filter, index) {
      // Remover o filtro específico pelo índice
      if (index !== undefined) {
        // Remover o item do array de filtros aplicados
        this.appliedFilters[filter].splice(index, 1);

        // Remover o item correspondente do array de filtros de entrada
        this.inputFilters[filter].splice(index, 1);

        // Remover o ID do usuário do array de IDs selecionados
        if (this.selectedUserIds[filter][index]) {
          this.selectedUserIds[filter].splice(index, 1);
        }
      } else {
        // Se não foi fornecido um índice, limpar todos os filtros desse tipo
        this.inputFilters[filter] = [];
        this.appliedFilters[filter] = [];
        this.selectedUserIds[filter] = [];
      }

      // Limpar o valor selecionado no autocomplete correspondente
      if (filter === "name") {
        this.selectedName = "";
      } else if (filter === "cpf") {
        this.selectedCpf = "";
      } else if (filter === "email") {
        this.selectedEmail = "";
      }

      // Limpar a seleção de usuários quando um filtro é removido
      this.selectedUsers = [];

      this.currentPage = 1;

      // Obter o ID da turma
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        await this.loadRegisteredUsers(classId);
      } else {
        this.error = "ID da turma não encontrado";
      }
    },

    async handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;

      // Recarregar os dados do backend com a nova ordenação
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        await this.loadRegisteredUsers(classId);
      }
    },

    mapSortFieldToBackend(frontendField) {
      const fieldMapping = {
        'fullName': 'fullname', 
        'email': 'email', 
        'cpf': 'cpf', 
        'startDate': 'startdate', 
        'endDate': 'enddate', 
        'deadline': 'enrolperiod', 
        'status': 'situation', 
        'state': 'ue_status' 
      };

      return fieldMapping[frontendField] || "fullname";
    },

    addNewUser() {
      const classId = this.offerclassid || this.$route.params.offerclassid;

      if (!classId) {
        this.error =
          "ID da turma não encontrado. Não é possível matricular usuários.";
        return;
      }

      // Verificar se o ciclo operacional da turma está encerrado
      if (this.classDetails && this.classDetails?.operational_cycle === 2) {
        this.error =
          "Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";
        return;
      }

      this.showEnrolmentModal = true;
    },

    closeEnrolmentModal() {
      this.showEnrolmentModal = false;
    },

    async goBack() {
      try {
        // 1. Obter o ID da turma
        const classId = this.offerclassid || this.$route.params.offerclassid;

        if (!classId) {
          this.router.push({ name: "offer-manager" });
          return;
        }

        // 2. Obter os detalhes da turma para encontrar o curso associado
        const classResponse = await getClass(parseInt(classId));

        if (!classResponse || classResponse.error) {
          this.router.push({ name: "offer-manager" });
          return;
        }

        // 3. Extrair o ID do curso da oferta (offercourseid) da resposta
        let offerCourseId = null;

        if (classResponse.data && classResponse.data.offercourseid) {
          // Caso 1: offercourseid está diretamente em data
          offerCourseId = classResponse.data.offercourseid;
        } else if (classResponse.offercourseid) {
          // Caso 2: offercourseid está diretamente na resposta
          offerCourseId = classResponse.offercourseid;
        } else if (classResponse.data && Array.isArray(classResponse.data)) {
          // Caso 3: data é um array, procurar a turma com o ID correspondente
          const classData = classResponse.data.find(
            (item) => item.id === parseInt(classId)
          );
          if (classData && classData.offercourseid) {
            offerCourseId = classData.offercourseid;
          }
        }

        if (!offerCourseId) {
          this.router.push({ name: "offer-manager" });
          return;
        }

        // 4. Obter os detalhes do curso da oferta para encontrar o ID da oferta
        const offerCourseResponse = await getClasses(offerCourseId);

        if (!offerCourseResponse) {
          this.router.push({ name: "offer-manager" });
          return;
        }

        // 5. Extrair o ID da oferta (offerid) da resposta
        let offerId = null;

        if (offerCourseResponse.data[0].offerid) {
          offerId = offerCourseResponse.data[0].offerid;
        }

        if (!offerId) {
          this.router.push({ name: "offer-manager" });
          return;
        }

        // 6. Redirecionar para a página de edição da oferta
        this.router.push({
          name: "editar-oferta",
          params: { id: offerId.toString() },
        });
      } catch (error) {
        this.router.push({ name: "offer-manager" });
      }
    },

    // Método para redirecionar para o perfil do usuário
    viewUserProfile(userId) {
      if (!userId) {
        return;
      }

      const courseId = this.classDetails?.courseid;

      // Construir a URL para o perfil do usuário
      const url = `/user/view.php?id=${userId}&course=${courseId}`;

      // Redirecionar para a URL
      window.location.href = url;
    },

    // Método para lidar com a mudança de visualização da página
    async handlePageViewChange(value) {
      // Obter o ID da turma
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (!classId) {
        return;
      }

      try {
        // Obter os detalhes da turma
        const classDetails = await getClass(parseInt(classId));

        // Verificar se temos os detalhes da turma
        if (
          !classDetails ||
          !classDetails.data ||
          !classDetails.data.offercourseid
        ) {
          // Se não encontrarmos o ID do curso da oferta, voltar para a opção de usuários matriculados
          this.selectedPageView = "usuarios_matriculados";
          return;
        }

        // Obter o ID do curso da oferta
        const offerCourseId = classDetails.data.offercourseid;

        // Obter os detalhes do curso da oferta para encontrar o courseid real
        try {
          const offerCourseDetails = await getClasses(offerCourseId);

          // Verificar se temos uma resposta válida
          if (
            !offerCourseDetails ||
            (Array.isArray(offerCourseDetails) &&
              offerCourseDetails.length === 0)
          ) {
            this.selectedPageView = "usuarios_matriculados";
            return;
          }

          // Extrair o courseid da resposta
          let courseId = null;

          // Verificar se o classDetails já contém o courseid
          if (classDetails.data && classDetails.data.courseid) {
            courseId = classDetails.data.courseid;
          }
          // Se não tiver o courseid, tentar extrair dos detalhes do curso
          else if (
            Array.isArray(offerCourseDetails) &&
            offerCourseDetails.length > 0 &&
            offerCourseDetails[0] &&
            offerCourseDetails[0].error === false &&
            offerCourseDetails[0].data &&
            offerCourseDetails[0].data.courses &&
            offerCourseDetails[0].data.courses.length > 0
          ) {
            // Extrair o courseid
            courseId = offerCourseDetails[0].data.courses[0].courseid;
          }

          // Se não conseguimos extrair o courseid, usar o offercourseid como fallback
          if (!courseId) {
            courseId = offerCourseId;
          }

          // Obter o contextid para as rotas que precisam dele
          // No Moodle, o contextid para um curso é obtido usando context_course::instance($courseId)->id
          // Como não temos acesso direto a essa função no frontend, vamos usar uma abordagem alternativa

          // No Moodle, os IDs de contexto são gerados de forma previsível:
          // - CONTEXT_SYSTEM = 1
          // - CONTEXT_USER = 30
          // - CONTEXT_COURSECAT = 40
          // - CONTEXT_COURSE = 50
          // - CONTEXT_MODULE = 70
          // - CONTEXT_BLOCK = 80

          // Para cursos, o contextid geralmente é courseId + um valor base
          // Vamos tentar obter o contextid correto da API ou usar uma estimativa
          let courseContextId = null;

          // Se temos o contextid na resposta da API, usá-lo
          if (classDetails.data && classDetails.data.course_context_id) {
            courseContextId = classDetails.data.course_context_id;
          } else {
            // Como não temos o contextid exato, vamos usar o courseId
            // Isso funcionará para as rotas que usam id=, mas pode não funcionar para contextid=
            courseContextId = courseId;
          }

          // Mapear os valores para as rotas correspondentes
          const routeMap = {
            usuarios_matriculados: `/local/offermanager/new-subscribed-users/${classId}`,
            grupos: `/group/index.php?id=${courseId}`,
            agrupamentos: `/group/groupings.php?id=${courseId}`,
            visao_geral: `/user/index.php?id=${courseId}`,
            permissoes: `/admin/roles/permissions.php?contextid=${courseContextId}`,
            outros_usuarios: `/enrol/otherusers.php?id=${courseId}`,
            verificar_permissoes: `/admin/roles/check.php?contextid=${courseContextId}`,
          };

          // Verificar se temos uma rota para o valor selecionado
          if (routeMap[value]) {
            // Redirecionar para a rota correspondente
            window.location.href = routeMap[value];
          } else {
            // Se não encontrarmos uma rota, voltar para a opção de usuários matriculados
            this.selectedPageView = "usuarios_matriculados";
          }
        } catch (error) {
          this.selectedPageView = "usuarios_matriculados";
        }
      } catch (error) {
        // Em caso de erro, voltar para a opção de usuários matriculados
        this.selectedPageView = "usuarios_matriculados";
      }
    },

    async handleEnrolmentSuccess() {
      // Recarregar a lista de usuários matriculados
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        await this.loadRegisteredUsers(classId);
      }
    },

    async loadRoles() {
      try {
        // Obter o ID da turma
        const classId = this.offerclassid || this.$route.params.offerclassid;

        try {
          // Primeiro, precisamos obter o offercourseid a partir do offerclassid
          const classDetails = await getClass(parseInt(classId));

          if (
            classDetails &&
            classDetails.data &&
            classDetails.data.offercourseid
          ) {
            // Obter o ID do curso da oferta
            const offerCourseId = classDetails.data.offercourseid;

            try {
              // Buscar os papéis disponíveis para o curso
              const response = await getOfferCourseRoles(offerCourseId);

              // Processar a resposta
              if (response && Array.isArray(response)) {
                this.roleOptions = response.map((role) => ({
                  id: String(role.id),
                  name: role.name,
                }));
              }
            } catch (error) {
              console.error("Erro ao buscar papéis do curso:", error);
            }
          }
        } catch (error) {
          console.error("Erro ao obter detalhes da turma:", error);
        }
      } catch (error) {
        console.error("Erro geral em loadRoles:", error);
      }
    },

    showEnrollmentDetails(user) {
      // Formatar os dados do usuário para o modal de detalhes da matrícula
      this.selectedUser = {
        fullName: user.fullName,
        enrol: user.enrol || "Inscrições manuais",
        state: user.state || 0,
        stateName: user.stateName || "Ativo",
        startDate: user.startDate || "Não disponível",
        createdDate: user.createdDate || user.startDate || "Não disponível",
      };

      // Mostrar o modal
      this.showEnrollmentModal = true;
    },

    closeEnrollmentModal() {
      this.showEnrollmentModal = false;
      this.selectedUser = null;
    },

    closeEditEnrollmentModal() {
      this.showEditEnrollmentModal = false;
      this.selectedUser = null;
    },

    async handleEditEnrollmentSuccess(data) {
      // Mostrar mensagem de sucesso
      this.showSuccessMessage("Matrícula editada com sucesso.");

      // Se temos o ID do papel, atualizar o nome do papel na tabela
      if (data.roleid) {
        // Encontrar o nome do papel correspondente ao ID
        let roleName = null;

        // Verificar se temos o roleOptions disponível
        if (this.roleOptions && this.roleOptions.length > 0) {
          const role = this.roleOptions.find(
            (r) => r.id === String(data.roleid)
          );
          if (role) {
            roleName = role.name;
          }
        }

        // Se não conseguimos obter o nome do papel, não atualizamos esse campo
        if (!roleName) {
          // Recarregar a lista completa para obter os dados atualizados da API
          const classId = this.offerclassid || this.$route.params.offerclassid;
          if (classId) {
            await this.loadRegisteredUsers(classId);
            this.showEditEnrollmentModal = false;
            this.selectedUser = null;
            return;
          }
        }

        // Atualizar o papel do usuário na tabela
        const userIndex = this.enrolments.findIndex(
          (user) => user.id === data.userId
        );
        if (userIndex !== -1) {
          // Atualizar o papel e o estado do usuário na tabela apenas se temos os valores
          if (roleName) {
            this.enrolments[userIndex].roles = roleName;
          }

          if (data.status !== undefined) {
            this.enrolments[userIndex].state = data.status;
            // Atualizar o nome do estado apenas se temos o status
            if (data.status === 1) {
              this.enrolments[userIndex].stateName = "Ativo";
            } else if (data.status === 0) {
              this.enrolments[userIndex].stateName = "Suspenso";
            }
          }

          // Se temos timestamps de início e fim, atualizar também
          if (data.timestart) {
            const startDate = new Date(data.timestart * 1000);
            this.enrolments[userIndex].startDate =
              startDate.toLocaleDateString("pt-BR");
          }

          if (data.timeend) {
            const endDate = new Date(data.timeend * 1000);
            this.enrolments[userIndex].endDate =
              endDate.toLocaleDateString("pt-BR");
          }
        } else {
          // Se não encontramos o usuário na tabela, recarregar a lista completa
          const classId = this.offerclassid || this.$route.params.offerclassid;
          if (classId) {
            await this.loadRegisteredUsers(classId);
          }
        }
      } else {
        // Se não temos o ID do papel, recarregar a lista completa
        const classId = this.offerclassid || this.$route.params.offerclassid;
        if (classId) {
          await this.loadRegisteredUsers(classId);
        }
      }

      this.showEditEnrollmentModal = false;
      this.selectedUser = null;
    },

    handleEditEnrollmentError(errorMessage) {
      // Mostrar mensagem de erro
      this.showErrorMessage(
        errorMessage ||
          "Não foi possível editar a matrícula. Por favor, tente novamente."
      );

      // Não fechar o modal para permitir que o usuário tente novamente
    },

    /**
     * Manipula o sucesso na atualização do papel do usuário
     * @param {Object} data Dados da atualização
     */
    handleRoleUpdateSuccess(data) {
      // Mostrar mensagem de sucesso
      this.showSuccessMessage("Papel atualizado com sucesso.");

      // Atualizar o papel do usuário na tabela
      const userIndex = this.enrolments.findIndex(
        (user) => user.id === data.userId
      );
      if (userIndex !== -1) {
        // Atualizar o papel do usuário na tabela
        this.enrolments[userIndex].roles = data.roleName;
      } else {
        // Se não encontramos o usuário na tabela, recarregar a lista completa
        this.reloadTable();
      }
    },

    /**
     * Manipula o erro na atualização do papel do usuário
     * @param {string} message Mensagem de erro
     */
    handleRoleUpdateError(message) {
      this.showErrorMessage(
        message || "Ocorreu um erro ao atualizar o papel do usuário."
      );
    },

    /**
     * Recarrega a tabela de usuários matriculados
     */
    reloadTable() {
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        this.loadRegisteredUsers(classId);
      } else {
        this.error = "ID da turma não encontrado";
      }
    },

    editUser(user) {
      // Extrair o ID do papel a partir do nome do papel
      let roleid = null;
      if (user.roles) {
        // Tentar obter o roleid a partir dos dados da API
        if (user.roleid) {
          roleid = user.roleid;
        }
      }

      // Preparar os dados do usuário para o modal de edição
      this.selectedUser = {
        id: user.id, // ID do usuário
        offeruserenrolid: user.offeruserenrolid, // ID da matrícula (importante para edição)
        fullName: user.fullName,
        enrol: user.enrol,
        state: user.state,
        stateName: user.stateName,
        roles: user.roles, // Nome do papel
        roleid: roleid, // ID do papel (sem valor padrão)
        startDate: user.startDate,
        timestart: user.timestart,
        timeend: user.timeend,
        createdDate: user.createdDate || "-",
      };

      // Mostrar o modal de edição
      this.showEditEnrollmentModal = true;
    },

    generateCertificate(user) {
      if (user.status !== "Aprovado") {
        return;
      }

      // Aqui você implementaria a lógica para gerar o certificado
      // Por exemplo, abrir uma nova janela com o certificado para impressão
    },

    async confirmeBulkDeleteEnrollment() {
      try {
        this.loading = true;

        // Extrair os IDs das matrículas dos usuários selecionados
        const offeruserenrolids = [];

        for (const userId of this.selectedUsers) {
          // Buscar o usuário na lista de ofertas para obter o ID da matrícula
          const user = this.enrolments.find((offer) => offer.id === userId);
          if (user && user.offeruserenrolid) {
            offeruserenrolids.push(user.offeruserenrolid);
          } else {
            console.error(
              `Não foi possível encontrar o ID da matrícula para o usuário ID ${userId}`
            );
          }
        }

        if (offeruserenrolids.length === 0) {
          this.showErrorMessage(
            "Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."
          );
          this.loading = false;
          return;
        }

        // Mostrar mensagem de processamento
        const processingMessage = `Processando exclusão de ${offeruserenrolids.length} matrícula(s)...`;
        this.showSuccessMessage(processingMessage);

        // Chamar o serviço de exclusão em lote
        const results = await deleteEnrolmentBulk(offeruserenrolids);

        // Verificar os resultados
        if (results && results.length > 0) {
          const successCount = results.filter(
            (result) => result.operation_status
          ).length;
          const failCount = results.length - successCount;

          // Mostrar mensagem de sucesso/erro
          if (successCount > 0) {
            this.showSuccessMessage(
              `${successCount} matrícula(s) cancelada(s) com sucesso.${failCount > 0 ? ` ${failCount} matrícula(s) não puderam ser canceladas.` : ""}`
            );

            // Recarregar a lista de usuários matriculados
            const classId =
              this.offerclassid || this.$route.params.offerclassid;
            if (classId) {
              await this.loadRegisteredUsers(classId);
            }

            // Limpar a seleção de usuários
            this.selectedUsers = [];
          } else {
            this.showErrorMessage(
              "Não foi possível cancelar as matrículas. Por favor, tente novamente."
            );
          }
        } else {
          // Se não temos resultados específicos, mas a operação foi bem-sucedida
          // (isso acontece quando a resposta é { error: false, data: true })
          this.showSuccessMessage(
            `${offeruserenrolids.length} matrícula(s) cancelada(s) com sucesso.`
          );

          // Recarregar a lista de usuários matriculados
          const classId = this.offerclassid || this.$route.params.offerclassid;
          if (classId) {
            await this.loadRegisteredUsers(classId);
          }

          // Limpar a seleção de usuários
          this.selectedUsers = [];
        }

        this.showBulkDeleteEnrollmentModal = false;
      } catch (error) {
        this.error = error.message;

        this.showErrorMessage(
          "Ocorreu um erro ao cancelar a matrícula. Por favor, tente novamente."
        );
      } finally {
        this.loading = false;
      }
    },

    handleBulkAction() {
      if (!this.selectedBulkAction) return;

      // Verificar se temos usuários selecionados
      if (this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para realizar esta ação."
        );
        this.selectedBulkAction = "";
        return;
      }

      switch (this.selectedBulkAction) {
        case "message":
          this.sendMessage();
          break;
        case "note":
          this.writeNote();
          break;
        case "download_csv":
          this.downloadData("csv");
          break;
        case "download_xlsx":
          this.downloadData("xlsx");
          break;
        case "download_html":
          this.downloadData("html");
          break;
        case "download_json":
          this.downloadData("json");
          break;
        case "download_ods":
          this.downloadData("ods");
          break;
        case "download_pdf":
          this.downloadData("pdf");
          break;
        case "edit_enrolment":
          this.editEnrolments();
          break;
        case "delete_enrolment":
          this.bulkDeleteEnrollment();
          break;
      }

      // Reset the select after action
      this.selectedBulkAction = "";
    },

    sendMessage() {
      // Verificar se temos usuários selecionados
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para enviar mensagem."
        );
        return;
      }

      // Se selectedUsers for um array de números, usá-los diretamente
      let validUserIds = [];

      if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((id) => typeof id === "number")
      ) {
        // Se selectedUsers já for um array de IDs
        validUserIds = this.selectedUsers;
      } else if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((user) => user && typeof user === "object")
      ) {
        // Se selectedUsers for um array de objetos de usuário
        validUserIds = this.selectedUsers
          .filter((user) => user && (user.id || user.userId))
          .map((user) => user.id || user.userId);
        console.log("IDs extraídos de objetos de usuário:", validUserIds);
      } else {
        // Tentar extrair IDs de qualquer forma
        try {
          validUserIds = this.selectedUsers
            .filter((item) => item !== null && item !== undefined)
            .map((item) => {
              if (typeof item === "number") return item;
              if (typeof item === "object" && item !== null)
                return item.id || item.userId;
              return null;
            })
            .filter((id) => id !== null && id !== undefined);
          console.log("IDs extraídos com método alternativo:", validUserIds);
        } catch (error) {
          console.error("Erro ao extrair IDs:", error);
        }
      }

      if (validUserIds.length === 0) {
        this.showErrorMessage(
          "Não foi possível enviar mensagem. Nenhum usuário válido selecionado."
        );
        return;
      }

      // Abrir o modal de mensagens usando a API do Moodle
      this.showSendMessageModal(validUserIds);
    },

    /**
     * Mostra o modal de envio de mensagens usando a API do Moodle
     *
     * @param {Array} userIds - IDs dos usuários para enviar mensagem
     */
    showSendMessageModal(userIds) {
      if (typeof window.require !== "function") {
        console.error("Função require não disponível");
        this.showErrorMessage(
          "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_message/message_send_bulk do Moodle para mostrar o modal
      window.require(
        ["core_message/message_send_bulk"],
        (BulkSender) => {
          if (typeof BulkSender.showModal !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Mostrar o modal de mensagens
          BulkSender.showModal(userIds, () => {
            // Resetar o select após o envio da mensagem
            this.selectedBulkAction = "";
          });
        },
        (error) => {
          console.error(
            "Erro ao carregar o módulo core_message/message_send_bulk:",
            error
          );
          this.showErrorMessage(
            "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    writeNote() {
      // Verificar se temos usuários selecionados
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para escrever anotação."
        );
        return;
      }

      console.log("Escrevendo anotação para usuários:", this.selectedUsers);

      // Se selectedUsers for um array de números, usá-los diretamente
      let validUserIds = [];

      if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((id) => typeof id === "number")
      ) {
        // Se selectedUsers já for um array de IDs
        validUserIds = this.selectedUsers;
        console.log("selectedUsers é um array de IDs:", validUserIds);
      } else if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((user) => user && typeof user === "object")
      ) {
        // Se selectedUsers for um array de objetos de usuário
        validUserIds = this.selectedUsers
          .filter((user) => user && (user.id || user.userId))
          .map((user) => user.id || user.userId);
        console.log("IDs extraídos de objetos de usuário:", validUserIds);
      } else {
        // Tentar extrair IDs de qualquer forma
        try {
          validUserIds = this.selectedUsers
            .filter((item) => item !== null && item !== undefined)
            .map((item) => {
              if (typeof item === "number") return item;
              if (typeof item === "object" && item !== null)
                return item.id || item.userId;
              return null;
            })
            .filter((id) => id !== null && id !== undefined);
        } catch (error) {
          console.error("Erro ao extrair IDs:", error);
        }
      }

      if (validUserIds.length === 0) {
        this.showErrorMessage(
          "Não foi possível escrever anotação. Nenhum usuário válido selecionado."
        );
        return;
      }

      const courseId = this.classDetails.courseid;

      if (!courseId) {
        this.showErrorMessage(
          "Não foi possível escrever anotação. ID do curso não disponível."
        );
        return;
      }

      // Abrir o modal de anotações usando a API do Moodle
      this.showAddNoteModal(courseId, validUserIds);
    },

    /**
     * Mostra o modal de adição de anotações usando a API do Moodle
     *
     * @param {Number} courseId - ID do curso
     * @param {Array} userIds - IDs dos usuários para adicionar anotação
     */
    showAddNoteModal(courseId, userIds) {
      // Verificar se o módulo necessário está disponível
      if (typeof window.require !== "function") {
        this.showErrorMessage(
          "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_user/local/participants/bulkactions do Moodle para mostrar o modal
      window.require(
        ["core_user/local/participants/bulkactions"],
        (BulkActions) => {
          if (typeof BulkActions.showAddNote !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Obter os nomes dos estados de anotação
          const noteStateNames = {
            personal: "Pessoal",
            course: "Curso",
            site: "Site",
          };

          // Mostrar o modal de anotações
          BulkActions.showAddNote(courseId, userIds, noteStateNames, "")
            .then((modal) => {
              modal.getRoot().on("hidden.bs.modal", () => {
                // Callback a ser executado quando o modal for fechado
                console.log("Modal de anotações fechado");
                // Resetar o select após a adição da anotação
                this.selectedBulkAction = "";
              });

              return modal;
            })
            .catch((error) => {
              console.error("Erro ao mostrar o modal de anotações:", error);
              this.showErrorMessage(
                "Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde."
              );
            });
        },
        (error) => {
          console.error(error);
          this.showErrorMessage(
            "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    downloadData(format) {
      if (this.selectedUsers.length === 0) return;

      // Vamos implementar o download diretamente no cliente
      // Isso evita problemas com a API do Moodle que pode variar entre versões
      this.prepareLocalDownload(format);
    },

    prepareLocalDownload(format) {
      // Preparar os dados para download local
      // Verificar se temos usuários selecionados
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage("Nenhum usuário selecionado para download.");
        return;
      }

      // Extrair os dados dos usuários selecionados
      const data = [];

      // Verificar se selectedUsers é um array de IDs
      if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((id) => typeof id === "number")
      ) {
        // Buscar os usuários correspondentes na tabela de ofertas
        for (const userId of this.selectedUsers) {
          const user = this.enrolments.find((offer) => offer.id === userId);
          if (user) {
            // Criar um objeto com todos os campos disponíveis
            const userData = {
              ID: user.id || "",
              Nome: user.fullName || user.name || "",
              Email: user.email || "",
              CPF: user.cpf || "",
              Papéis: user.roles || "",
              Grupos: user.groups || "",
              "Data de Início": user.startDate || "",
              "Data de Término": user.endDate || "",
              Prazo: user.deadline || "",
              Progresso: user.progress || "",
              Situação: user.statusName || user.status || "",
              Nota: user.grade || "",
              Estado: user.stateName || "",
            };

            // Adicionar o usuário ao array de dados
            data.push(userData);
          }
        }
      } else {
        // Iterar sobre cada usuário selecionado
        for (const user of this.selectedUsers) {
          // Verificar se o usuário tem dados válidos
          if (!user) {
            console.warn("Usuário inválido encontrado:", user);
            continue;
          }

          // Verificar se o usuário é um objeto ou um ID
          if (typeof user === "number") {
            // Se for um ID, buscar o usuário na tabela
            const userObj = this.enrolments.find((offer) => offer.id === user);
            if (userObj) {
              // Criar um objeto com todos os campos disponíveis
              const userData = {
                ID: userObj.id || "",
                Nome: userObj.fullName || userObj.name || "",
                Email: userObj.email || "",
                CPF: userObj.cpf || "",
                Papéis: userObj.roles || "",
                Grupos: userObj.groups || "",
                "Data de Início": userObj.startDate || "",
                "Data de Término": userObj.endDate || "",
                Prazo: userObj.deadline || "",
                Progresso: userObj.progress || "",
                Situação: userObj.statusName || userObj.status || "",
                Nota: userObj.grade || "",
                Estado: userObj.stateName || "",
              };

              // Adicionar o usuário ao array de dados
              data.push(userData);
            }
          } else if (typeof user === "object" && user !== null) {
            // Se for um objeto, usar diretamente
            // Criar um objeto com todos os campos disponíveis
            const userData = {
              ID: user.id || "",
              Nome: user.fullName || user.name || "",
              Email: user.email || "",
              CPF: user.cpf || "",
              Papéis: user.roles || "",
              Grupos: user.groups || "",
              "Data de Início": user.startDate || "",
              "Data de Término": user.endDate || "",
              Prazo: user.deadline || "",
              Progresso: user.progress || "",
              Situação: user.statusName || user.status || "",
              Nota: user.grade || "",
              Estado: user.stateName || "",
            };

            // Adicionar o usuário ao array de dados
            data.push(userData);
          }
        }
      }

      // Verificar se há dados para download
      if (data.length === 0) {
        console.error(
          "Nenhum dado disponível para download após processamento"
        );

        // Tentar uma abordagem alternativa - usar todos os usuários da tabela
        if (
          this.selectedUsers.length > 0 &&
          this.enrolments &&
          this.enrolments.length > 0
        ) {
          // Criar uma lista simples de IDs e nomes para download
          const simpleData = this.selectedUsers.map((userId, index) => ({
            ID: userId,
            Índice: index + 1,
            Selecionado: "Sim",
          }));

          if (simpleData.length > 0) {
            // Usar os dados simples para download
            switch (format) {
              case "csv":
                this.downloadCSV(simpleData);
                break;
              case "xlsx":
                this.downloadXLSX(simpleData);
                break;
              case "html":
                this.downloadHTML(simpleData);
                break;
              case "json":
                this.downloadJSON(simpleData);
                break;
              default:
                this.showErrorMessage("Formato de download não suportado.");
                break;
            }
            return;
          }
        }

        this.showErrorMessage("Nenhum dado disponível para download.");
        return;
      }

      // Converter para o formato solicitado e fazer o download
      try {
        switch (format) {
          case "csv":
            this.downloadCSV(data);
            break;
          case "xlsx":
            this.downloadXLSX(data);
            break;
          case "html":
            this.downloadHTML(data);
            break;
          case "json":
            this.downloadJSON(data);
            break;
          case "ods":
            this.downloadODS(data);
            break;
          case "pdf":
            this.downloadPDF(data);
            break;
          default:
            this.showErrorMessage("Formato de download não suportado.");
            break;
        }
      } catch (error) {
        console.error("Erro ao fazer download:", error);
        this.showErrorMessage(
          "Ocorreu um erro ao fazer o download. Por favor, tente novamente."
        );
      }
    },

    downloadCSV(data) {
      // Implementação para download de CSV
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Formatar os cabeçalhos para melhor legibilidade
      const formattedHeaders = headers.map((header) => {
        // Converter camelCase para Title Case com espaços
        return header
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
      });

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          formattedHeaders.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadXLSX(data) {
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente no Excel
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          headers.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Mostrar uma mensagem informativa
      this.showSuccessMessage(
        "Download concluído. O arquivo CSV pode ser aberto no Excel."
      );
    },

    downloadHTML(data) {
      // Implementação para download de HTML
      if (data.length === 0) return;

      try {
        const headers = Object.keys(data[0]);

        // Formatar os cabeçalhos para melhor legibilidade
        const formattedHeaders = [];
        for (let i = 0; i < headers.length; i++) {
          // Converter camelCase para Title Case com espaços
          const formatted = headers[i]
            .replace(/([A-Z])/g, " $1")
            .replace(/^./, (str) => str.toUpperCase())
            .trim();
          formattedHeaders.push(formatted);
        }

        // Criar elementos HTML para a tabela
        let tableHeaders = "";
        for (let i = 0; i < formattedHeaders.length; i++) {
          tableHeaders += "<th>" + formattedHeaders[i] + "</th>";
        }

        let tableRows = "";
        for (let i = 0; i < data.length; i++) {
          let rowHtml = "<tr>";
          for (let j = 0; j < headers.length; j++) {
            rowHtml += "<td>" + (data[i][headers[j]] || "") + "</td>";
          }
          rowHtml += "</tr>";
          tableRows += rowHtml;
        }

        // Criar partes do HTML separadamente
        const htmlStart =
          '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>';
        const styles =
          "<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>";
        const bodyStart = "</head><body><h1>Usuários Matriculados</h1>";
        const tableStart = "<table><thead><tr>";
        const tableMiddle = "</tr></thead><tbody>";
        const tableEnd = "</tbody></table>";
        const footer =
          '<div class="footer">Gerado em ' +
          new Date().toLocaleString() +
          "</div>";
        const htmlEnd = "</body></html>";

        // Juntar todas as partes
        const htmlContent =
          htmlStart +
          styles +
          bodyStart +
          tableStart +
          tableHeaders +
          tableMiddle +
          tableRows +
          tableEnd +
          footer +
          htmlEnd;

        // Criar o blob e fazer o download
        const blob = new Blob([htmlContent], {
          type: "text/html;charset=utf-8;",
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", "usuarios_matriculados.html");
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Mostrar uma mensagem informativa
        this.showSuccessMessage(
          "Download concluído. O arquivo HTML foi salvo com sucesso."
        );
      } catch (error) {
        this.showErrorMessage(
          "Ocorreu um erro ao fazer o download. Por favor, tente novamente."
        );
      }
    },

    downloadJSON(data) {
      // Implementação para download de JSON
      if (data.length === 0) return;

      // Criar o conteúdo JSON formatado
      const jsonContent = JSON.stringify(data, null, 2);

      // Criar o blob e fazer o download
      const blob = new Blob([jsonContent], {
        type: "application/json;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.json");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadODS(data) {
      if (data.length === 0) return;

      try {
        // Implementação para download de ODS usando a biblioteca SheetJS
        // Como não podemos depender da biblioteca SheetJS diretamente, vamos usar uma abordagem alternativa
        // Vamos criar um arquivo CSV que pode ser aberto no LibreOffice Calc

        // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente
        const BOM = "\uFEFF";
        const headers = Object.keys(data[0]);

        // Preparar as linhas do CSV
        let csvRows = [];

        // Adicionar cabeçalhos
        csvRows.push(headers.join(","));

        // Adicionar dados
        data.forEach((row) => {
          const values = headers.map((header) => {
            // Escapar aspas e garantir que todos os campos estejam entre aspas
            const value = row[header] || "";
            return '"' + String(value).replace(/"/g, '""') + '"';
          });
          csvRows.push(values.join(","));
        });

        // Juntar todas as linhas com quebras de linha
        const csvContent = BOM + csvRows.join("\n");

        // Criar o blob e fazer o download
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", "usuarios_matriculados.csv");
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Mostrar uma mensagem informativa
        this.showSuccessMessage(
          "Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS."
        );
      } catch (error) {
        this.showErrorMessage(
          "Ocorreu um erro ao fazer o download. Por favor, tente novamente."
        );
      }
    },

    downloadPDF(data) {
      if (data.length === 0) return;

      try {
        // Implementação alternativa para download de PDF
        // Vamos usar o método downloadHTML e mostrar instruções para salvar como PDF
        this.downloadHTML(data);

        // Mostrar uma mensagem informativa
        this.showSuccessMessage(
          "Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."
        );
      } catch (error) {
        this.showErrorMessage(
          "Ocorreu um erro ao fazer o download. Por favor, tente novamente."
        );
      }
    },

    editEnrolments() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para editar matrícula."
        );
        return;
      }

      console.log("Editando matrículas dos usuários:", this.selectedUsers);

      // Se for apenas um usuário, abrir o modal de edição de matrícula
      if (this.selectedUsers.length === 1) {
        const userId = this.selectedUsers[0];
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user) {
          this.editUser(user);
        } else {
          this.showErrorMessage(
            "Usuário não encontrado. Por favor, tente novamente."
          );
        }
      } else {
        // Para múltiplos usuários, abrir o modal de edição em lote
        this.showBulkEditEnrollmentModal = true;
      }
    },

    async handleBulkEditEnrollmentSuccess(data) {
      console.log("Matrículas editadas em lote com sucesso:", data);

      // Mostrar mensagem de sucesso
      this.showSuccessMessage(
        data.message || "Matrículas editadas com sucesso."
      );

      // Recarregar a lista de usuários matriculados
      const classId = this.offerclassid || this.$route.params.offerclassid;
      if (classId) {
        await this.loadRegisteredUsers(classId);
      }

      // Limpar a seleção de usuários
      this.selectedUsers = [];
      this.showBulkEditEnrollmentModal = false;
    },

    handleBulkEditEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível editar as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },

    bulkDeleteEnrollment() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para excluir matrícula."
        );
        return;
      }

      this.showBulkDeleteEnrollmentModal = true;
    },

    handleBulkDeleteEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível excluir as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },

    /**
     * Exibe uma mensagem de sucesso usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe uma mensagem de erro usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    showWarningMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "warning";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },
};
</script>

<style src="@/assets/scss/RegisteredUsers.scss" lang="scss" scoped></style>


