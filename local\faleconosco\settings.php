<?php

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    $settingspage = new admin_settingpage(
        'local_faleconosco_settings',
        new lang_string('pluginname', 'local_faleconosco'),
        'moodle/site:config'
    );

    $ADMIN->add('localplugins', $settingspage);

    if ($ADMIN->fulltree) {
        $settingspage->add(new admin_setting_configcheckbox(
            "local_faleconosco/enabled",
            new lang_string('enable'),
            "",
            1
        ));
    }
}
