<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'local_tickets', language 'pt_br'
 *
 * @package    local_tickets
 * @category   string
 * @copyright  2025 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Fale Conosco';
$string['tickets:manage'] = 'Gerenciar chamados';
$string['subtitle_manager'] = 'Listagem de Chamados';
$string['subtitle_user'] = 'Meus Chamados';
$string['subtitle_edit'] = 'Edição de Chamado';

// Table Headers and Labels
$string['id'] = 'ID';
$string['summary'] = 'Resumo';
$string['description'] = 'Descrição';
$string['reporteduser'] = 'Aberto por';
$string['timecreated'] = 'Aberto em';
$string['timemodified'] = 'Atualizado em';
$string['priority'] = 'Prioridade';
$string['assigneduser'] = 'Designado para';
$string['status'] = 'Status';
$string['actions'] = 'Ações';
$string['ticket'] = 'Ticket';
$string['filter'] = 'Filtro';
$string['number_ticket'] = 'Número do Chamado';

// Buttons and Titles Modals
$string['create_ticket'] = 'Novo Ticket';
$string['save'] = 'Salvar';
$string['close'] = 'Fechar';
$string['filter_apply'] = 'Filtrar';
$string['clear'] = 'Limpar';
$string['back'] = 'Voltar';
$string['add_comment'] = 'Adicionar Comentário';
$string['interactions'] = 'Histórico de Interações';

// Helpers Text
$string['help_ticket_search'] = 'Pesquise pelo id ou resumo do chamado';

// Statuses
$string['status_1'] = 'Aberto';
$string['status_2'] = 'Em progresso';
$string['status_3'] = 'Resolvido';

// Priorities
$string['priority_1'] = 'Alta';
$string['priority_2'] = 'Média';
$string['priority_3'] = 'Baixa';

// Validations and Placeholders
$string['unassigned'] = 'Não atribuído';
$string['comment_placeholder'] = 'Digite seu comentário...';

// Errors
$string['ticketnotfound'] = 'Chamado não encontrado.';
$string['accessdenied'] = 'Você não tem permissão para ver este chamado.';

// Success
$string['ticketcreated'] = 'Chamado criado com sucesso.';
$string['ticketupdated'] = 'Chamado atualizado com sucesso.';
$string['interactioncreated'] = 'Interação criada com sucesso.';

//notifications and emails
$string['ticket:notification:creation_subject'] = 'Chamado #{$a->id}: Criado';
$string['ticket:notification:creation_message'] = '<p>Olá {$a->user},<br><br>
O chamado #{$a->id} foi criado! <br><br> 
Acompanhe sua solicitação em <a href="{$a->link}">meu chamado</a></p>';
$string['ticket:notification:creation_manager_subject'] = 'Há um novo chamado #{$a->id}';
$string['ticket:notification:creation_manager_message'] = '<p>Olá {$a->user},<br><br>
O chamado #{$a->id} foi criado pelo usuário {$a->reporteduser}! <br><br> 
Por favor, verifique o <a href="{$a->link}">chamado</a></p>';
$string['ticket:notification:creation_manager_subject'] = 'Há um novo chamado #{$a->id}';
$string['ticket:notification:creation_manager_message'] = '<p>Olá {$a->user},<br><br>
O chamado #{$a->id} foi criado pelo usuário {$a->reporteduser}! <br><br> 
Por favor, verifique o <a href="{$a->link}">chamado</a></p>';
$string['ticket:notification:update_subject'] = 'Existe atualização no chamado #{$a->id}';
$string['ticket:notification:update_message'] = '<p>Olá {$a->user},<br><br>
O chamado #{$a->id} tem nova atualização! <br><br> 
Por favor, verifique o <a href="{$a->link}">chamado</a></p>';

$string['messageprovider:ticket_created'] = 'Envia notificação quando o chamado for criado';
$string['messageprovider:ticket_updated'] = 'Envia notificação quando o chamado for atualizado';
