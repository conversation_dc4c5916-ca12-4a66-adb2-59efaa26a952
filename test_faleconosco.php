<?php

define('CLI_SCRIPT', true);
require_once('config.php');

// Testa se a função do plugin está funcionando
if (function_exists('local_faleconosco_add_menubar_icon')) {
    $result = local_faleconosco_add_menubar_icon();
    
    if ($result !== false) {
        echo "✅ Plugin local_faleconosco está funcionando!\n";
        echo "Nome: " . $result->name . "\n";
        echo "Ícone: " . $result->icon . "\n";
        echo "URL: " . $result->url . "\n";
        echo "Target: " . $result->target . "\n";
        echo "Order: " . $result->order . "\n";
    } else {
        echo "❌ Plugin local_faleconosco retornou false\n";
    }
} else {
    echo "❌ Função local_faleconosco_add_menubar_icon não encontrada\n";
}

// Verifica configurações
$enabled = get_config('local_faleconosco', 'enabled');
$tickets_enabled = get_config('local_tickets', 'enabled');

echo "\nConfigurações:\n";
echo "local_faleconosco enabled: " . ($enabled ? 'SIM' : 'NÃO') . "\n";
echo "local_tickets enabled: " . ($tickets_enabled ? 'SIM' : 'NÃO') . "\n";
