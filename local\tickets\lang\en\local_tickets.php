<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Position Manager
 *
 * @package    local_tickets
 * @category   string
 * @copyright  2025 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Contact Us';
$string['tickets:manage'] = 'Manage tickets';
$string['subtitle_manager'] = 'Tickets List';
$string['subtitle_user'] = 'My Tickets';
$string['subtitle_edit'] = 'Edition Ticket';

// Table Headers
$string['id'] = 'ID';
$string['summary'] = 'Summary';
$string['description'] = 'Description';
$string['reporteduser'] = 'Reported By';
$string['timecreated'] = 'Opened in';
$string['timemodified'] = 'Updated in';
$string['priority'] = 'Priority';
$string['assigneduser'] = 'Assigned To';
$string['status'] = 'Status';
$string['actions'] = 'Actions';
$string['ticket'] = 'Ticket';
$string['filter'] = 'Filter';
$string['number_ticket'] = 'Ticket Number';

// Buttons and Titles Modals
$string['create_ticket'] = 'New Ticket';
$string['save'] = 'Save';
$string['close'] = 'Close';
$string['filter_apply'] = 'Apply Filter';
$string['clear'] = 'Clear';
$string['back'] = 'Back';
$string['add_comment'] = 'Add Comment';
$string['interactions'] = 'Interaction History';

// Helpers Text
$string['help_ticket_search'] = 'Search by id or summary';

// Statuses
$string['status_1'] = 'Open';
$string['status_2'] = 'In Progress';
$string['status_3'] = 'Closed';

// Priorities
$string['priority_1'] = 'High';
$string['priority_2'] = 'Medium';
$string['priority_3'] = 'Low';

// Validations and Placeholders
$string['unassigned'] = 'Not assigned';
$string['comment_placeholder'] = 'Write a comment...';

// Errors
$string['ticketnotfound'] = 'Ticket not found.';
$string['accessdenied'] = 'You do not have permission to view this ticket.';

// Success
$string['ticketcreated'] = 'Ticket created successfully.';
$string['ticketupdated'] = 'Ticket updated successfully.';
$string['interactioncreated'] = 'Interaction created successfully.';

//notifications
$string['ticket:notification:creation_subject'] = 'Ticket #{$a->id}: Created';
$string['ticket:notification:creation_message'] = '<p>Hello {$a->user},<br><br>
The ticket #{$a->id} was created! <br><br> 
Follow up your request in <a href="{$a->link}">my ticket</a></p>';
$string['ticket:notification:creation_manager_subject'] = 'There is a new ticket #{$a->id}';
$string['ticket:notification:creation_manager_message'] = '<p>Hello {$a->user},<br><br>
The ticket #{$a->id} was created by {$a->reporteduser}! <br><br> 
Follow up your request in the <a href="{$a->link}">ticket</a></p>';
$string['ticket:notification:update_subject'] = 'There is an update on the ticket #{$a->id}';
$string['ticket:notification:update_message'] = '<p>Hello {$a->user},<br><br>
The ticket #{$a->id} has a new update! <br><br> 
Follow up your request in the <a href="{$a->link}">ticket</a></p>';

$string['messageprovider:ticket_created'] = 'Sends notification when a ticket is created';
$string['messageprovider:ticket_updated'] = 'Sends notification when a ticket is updated';
