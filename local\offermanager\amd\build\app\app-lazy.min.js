define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const rt={}.NODE_ENV!=="production"?Object.freeze({}):{},xo={}.NODE_ENV!=="production"?Object.freeze([]):[],Ot=()=>{},qg=()=>!1,sn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ni=e=>e.startsWith("onUpdate:"),pt=Object.assign,Ya=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},zg=Object.prototype.hasOwnProperty,Ze=(e,t)=>zg.call(e,t),ge=Array.isArray,Qr=e=>rn(e)==="[object Map]",So=e=>rn(e)==="[object Set]",Rc=e=>rn(e)==="[object Date]",Se=e=>typeof e=="function",ft=e=>typeof e=="string",As=e=>typeof e=="symbol",Xe=e=>e!==null&&typeof e=="object",Za=e=>(Xe(e)||Se(e))&&Se(e.then)&&Se(e.catch),Vc=Object.prototype.toString,rn=e=>Vc.call(e),Ja=e=>rn(e).slice(8,-1),Fc=e=>rn(e)==="[object Object]",Xa=e=>ft(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,on=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Wg=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),ii=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Gg=/-(\w)/g,Kt=ii(e=>e.replace(Gg,(t,s)=>s?s.toUpperCase():"")),Kg=/\B([A-Z])/g,Sr=ii(e=>e.replace(Kg,"-$1").toLowerCase()),Yr=ii(e=>e.charAt(0).toUpperCase()+e.slice(1)),Zr=ii(e=>e?`on${Yr(e)}`:""),Or=(e,t)=>!Object.is(e,t),Oo=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ai=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},li=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Qg=e=>{const t=ft(e)?Number(e):NaN;return isNaN(t)?e:t};let Uc;const nn=()=>Uc||(Uc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ls(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],o=ft(i)?Xg(i):ls(i);if(o)for(const a in o)t[a]=o[a]}return t}else if(ft(e)||Xe(e))return e}const Yg=/;(?![^(]*\))/g,Zg=/:([^]+)/,Jg=/\/\*[^]*?\*\//g;function Xg(e){const t={};return e.replace(Jg,"").split(Yg).forEach(s=>{if(s){const i=s.split(Zg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function pe(e){let t="";if(ft(e))t=e;else if(ge(e))for(let s=0;s<e.length;s++){const i=pe(e[s]);i&&(t+=i+" ")}else if(Xe(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const ev="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",tv="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",sv="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",rv=er(ev),ov=er(tv),nv=er(sv),iv=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Lc(e){return!!e||e===""}function av(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=an(e[i],t[i]);return s}function an(e,t){if(e===t)return!0;let s=Rc(e),i=Rc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=As(e),i=As(t),s||i)return e===t;if(s=ge(e),i=ge(t),s||i)return s&&i?av(e,t):!1;if(s=Xe(e),i=Xe(t),s||i){if(!s||!i)return!1;const o=Object.keys(e).length,a=Object.keys(t).length;if(o!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),f=t.hasOwnProperty(u);if(c&&!f||!c&&f||!an(e[u],t[u]))return!1}}return String(e)===String(t)}function el(e,t){return e.findIndex(s=>an(s,t))}const Bc=e=>!!(e&&e.__v_isRef===!0),G=e=>ft(e)?e:e==null?"":ge(e)||Xe(e)&&(e.toString===Vc||!Se(e.toString))?Bc(e)?G(e.value):JSON.stringify(e,$c,2):String(e),$c=(e,t)=>Bc(t)?$c(e,t.value):Qr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,o],a)=>(s[tl(i,a)+" =>"]=o,s),{})}:So(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>tl(s))}:As(t)?tl(t):Xe(t)&&!ge(t)&&!Fc(t)?String(t):t,tl=(e,t="")=>{var s;return As(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let us;class jc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=us,!t&&us&&(this.index=(us.scopes||(us.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=us;try{return us=this,t()}finally{us=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){us=this}off(){us=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function lv(e){return new jc(e)}function uv(){return us}let ot;const sl=new WeakSet;class Hc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,us&&us.active&&us.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,sl.has(this)&&(sl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||zc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Yc(this),Wc(this);const t=ot,s=Ts;ot=this,Ts=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&ot!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),Gc(this),ot=t,Ts=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)il(t);this.deps=this.depsTail=void 0,Yc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?sl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){nl(this)&&this.run()}get dirty(){return nl(this)}}let qc=0,ln,un;function zc(e,t=!1){if(e.flags|=8,t){e.next=un,un=e;return}e.next=ln,ln=e}function rl(){qc++}function ol(){if(--qc>0)return;if(un){let t=un;for(un=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;ln;){let t=ln;for(ln=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Wc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Gc(e){let t,s=e.depsTail,i=s;for(;i;){const o=i.prevDep;i.version===-1?(i===s&&(s=o),il(i),cv(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=o}e.deps=t,e.depsTail=s}function nl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===cn))return;e.globalVersion=cn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!nl(e)){e.flags&=-3;return}const s=ot,i=Ts;ot=e,Ts=!0;try{Wc(e);const o=e.fn(e._value);(t.version===0||Or(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{ot=s,Ts=i,Gc(e),e.flags&=-3}}function il(e,t=!1){const{dep:s,prevSub:i,nextSub:o}=e;if(i&&(i.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=o),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)il(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function cv(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ts=!0;const Qc=[];function tr(){Qc.push(Ts),Ts=!1}function sr(){const e=Qc.pop();Ts=e===void 0?!0:e}function Yc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ot;ot=void 0;try{t()}finally{ot=s}}}let cn=0;class dv{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class al{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!ot||!Ts||ot===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ot)s=this.activeLink=new dv(ot,this),ot.deps?(s.prevDep=ot.depsTail,ot.depsTail.nextDep=s,ot.depsTail=s):ot.deps=ot.depsTail=s,Zc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=ot.depsTail,s.nextDep=void 0,ot.depsTail.nextDep=s,ot.depsTail=s,ot.deps===s&&(ot.deps=i)}return{}.NODE_ENV!=="production"&&ot.onTrack&&ot.onTrack(pt({effect:ot},t)),s}trigger(t){this.version++,cn++,this.notify(t)}notify(t){rl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(pt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{ol()}}}function Zc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Zc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ll=new WeakMap,Jr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),ul=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),dn=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Nt(e,t,s){if(Ts&&ot){let i=ll.get(e);i||ll.set(e,i=new Map);let o=i.get(s);o||(i.set(s,o=new al),o.map=i,o.key=s),{}.NODE_ENV!=="production"?o.track({target:e,type:t,key:s}):o.track()}}function qs(e,t,s,i,o,a){const u=ll.get(e);if(!u){cn++;return}const c=f=>{f&&({}.NODE_ENV!=="production"?f.trigger({target:e,type:t,key:s,newValue:i,oldValue:o,oldTarget:a}):f.trigger())};if(rl(),t==="clear")u.forEach(c);else{const f=ge(e),m=f&&Xa(s);if(f&&s==="length"){const p=Number(i);u.forEach((v,w)=>{(w==="length"||w===dn||!As(w)&&w>=p)&&c(v)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(dn)),t){case"add":f?m&&c(u.get("length")):(c(u.get(Jr)),Qr(e)&&c(u.get(ul)));break;case"delete":f||(c(u.get(Jr)),Qr(e)&&c(u.get(ul)));break;case"set":Qr(e)&&c(u.get(Jr));break}}ol()}function No(e){const t=Ae(e);return t===e?t:(Nt(t,"iterate",dn),Qt(e)?t:t.map(Ht))}function ui(e){return Nt(e=Ae(e),"iterate",dn),e}const fv={__proto__:null,[Symbol.iterator](){return cl(this,Symbol.iterator,Ht)},concat(...e){return No(this).concat(...e.map(t=>ge(t)?No(t):t))},entries(){return cl(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return dl(this,"includes",e)},indexOf(...e){return dl(this,"indexOf",e)},join(e){return No(this).join(e)},lastIndexOf(...e){return dl(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return fn(this,"pop")},push(...e){return fn(this,"push",e)},reduce(e,...t){return Jc(this,"reduce",e,t)},reduceRight(e,...t){return Jc(this,"reduceRight",e,t)},shift(){return fn(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return fn(this,"splice",e)},toReversed(){return No(this).toReversed()},toSorted(e){return No(this).toSorted(e)},toSpliced(...e){return No(this).toSpliced(...e)},unshift(...e){return fn(this,"unshift",e)},values(){return cl(this,"values",Ht)}};function cl(e,t,s){const i=ui(e),o=i[t]();return i!==e&&!Qt(e)&&(o._next=o.next,o.next=()=>{const a=o._next();return a.value&&(a.value=s(a.value)),a}),o}const hv=Array.prototype;function rr(e,t,s,i,o,a){const u=ui(e),c=u!==e&&!Qt(e),f=u[t];if(f!==hv[t]){const v=f.apply(e,a);return c?Ht(v):v}let m=s;u!==e&&(c?m=function(v,w){return s.call(this,Ht(v),w,e)}:s.length>2&&(m=function(v,w){return s.call(this,v,w,e)}));const p=f.call(u,m,i);return c&&o?o(p):p}function Jc(e,t,s,i){const o=ui(e);let a=s;return o!==e&&(Qt(e)?s.length>3&&(a=function(u,c,f){return s.call(this,u,c,f,e)}):a=function(u,c,f){return s.call(this,u,Ht(c),f,e)}),o[t](a,...i)}function dl(e,t,s){const i=Ae(e);Nt(i,"iterate",dn);const o=i[t](...s);return(o===-1||o===!1)&&mi(s[0])?(s[0]=Ae(s[0]),i[t](...s)):o}function fn(e,t,s=[]){tr(),rl();const i=Ae(e)[t].apply(e,s);return ol(),sr(),i}const pv=er("__proto__,__v_isRef,__isVue"),Xc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(As));function mv(e){As(e)||(e=String(e));const t=Ae(this);return Nt(t,"has",e),t.hasOwnProperty(e)}class ed{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const o=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!o;if(s==="__v_isReadonly")return o;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(o?a?ad:id:a?nd:od).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=ge(t);if(!o){let f;if(u&&(f=fv[s]))return f;if(s==="hasOwnProperty")return mv}const c=Reflect.get(t,s,Dt(t)?t:i);return(As(s)?Xc.has(s):pv(s))||(o||Nt(t,"get",s),a)?c:Dt(c)?u&&Xa(s)?c:c.value:Xe(c)?o?ud(c):hi(c):c}}class td extends ed{constructor(t=!1){super(!1,t)}set(t,s,i,o){let a=t[s];if(!this._isShallow){const f=or(a);if(!Qt(i)&&!or(i)&&(a=Ae(a),i=Ae(i)),!ge(t)&&Dt(a)&&!Dt(i))return f?!1:(a.value=i,!0)}const u=ge(t)&&Xa(s)?Number(s)<t.length:Ze(t,s),c=Reflect.set(t,s,i,Dt(t)?t:o);return t===Ae(o)&&(u?Or(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ze(t,s),o=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,o),a}has(t,s){const i=Reflect.has(t,s);return(!As(s)||!Xc.has(s))&&Nt(t,"has",s),i}ownKeys(t){return Nt(t,"iterate",ge(t)?"length":Jr),Reflect.ownKeys(t)}}class sd extends ed{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const gv=new td,vv=new sd,_v=new td(!0),yv=new sd(!0),fl=e=>e,ci=e=>Reflect.getPrototypeOf(e);function bv(e,t,s){return function(...i){const o=this.__v_raw,a=Ae(o),u=Qr(a),c=e==="entries"||e===Symbol.iterator&&u,f=e==="keys"&&u,m=o[e](...i),p=s?fl:t?pl:Ht;return!t&&Nt(a,"iterate",f?ul:Jr),{next(){const{value:v,done:w}=m.next();return w?{value:v,done:w}:{value:c?[p(v[0]),p(v[1])]:p(v),done:w}},[Symbol.iterator](){return this}}}}function di(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Yr(e)} operation ${s}failed: target is readonly.`,Ae(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function wv(e,t){const s={get(o){const a=this.__v_raw,u=Ae(a),c=Ae(o);e||(Or(o,c)&&Nt(u,"get",o),Nt(u,"get",c));const{has:f}=ci(u),m=t?fl:e?pl:Ht;if(f.call(u,o))return m(a.get(o));if(f.call(u,c))return m(a.get(c));a!==u&&a.get(o)},get size(){const o=this.__v_raw;return!e&&Nt(Ae(o),"iterate",Jr),Reflect.get(o,"size",o)},has(o){const a=this.__v_raw,u=Ae(a),c=Ae(o);return e||(Or(o,c)&&Nt(u,"has",o),Nt(u,"has",c)),o===c?a.has(o):a.has(o)||a.has(c)},forEach(o,a){const u=this,c=u.__v_raw,f=Ae(c),m=t?fl:e?pl:Ht;return!e&&Nt(f,"iterate",Jr),c.forEach((p,v)=>o.call(a,m(p),m(v),u))}};return pt(s,e?{add:di("add"),set:di("set"),delete:di("delete"),clear:di("clear")}:{add(o){!t&&!Qt(o)&&!or(o)&&(o=Ae(o));const a=Ae(this);return ci(a).has.call(a,o)||(a.add(o),qs(a,"add",o,o)),this},set(o,a){!t&&!Qt(a)&&!or(a)&&(a=Ae(a));const u=Ae(this),{has:c,get:f}=ci(u);let m=c.call(u,o);m?{}.NODE_ENV!=="production"&&rd(u,c,o):(o=Ae(o),m=c.call(u,o));const p=f.call(u,o);return u.set(o,a),m?Or(a,p)&&qs(u,"set",o,a,p):qs(u,"add",o,a),this},delete(o){const a=Ae(this),{has:u,get:c}=ci(a);let f=u.call(a,o);f?{}.NODE_ENV!=="production"&&rd(a,u,o):(o=Ae(o),f=u.call(a,o));const m=c?c.call(a,o):void 0,p=a.delete(o);return f&&qs(a,"delete",o,void 0,m),p},clear(){const o=Ae(this),a=o.size!==0,u={}.NODE_ENV!=="production"?Qr(o)?new Map(o):new Set(o):void 0,c=o.clear();return a&&qs(o,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(o=>{s[o]=bv(o,e,t)}),s}function fi(e,t){const s=wv(e,t);return(i,o,a)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?i:Reflect.get(Ze(s,o)&&o in i?s:i,o,a)}const Ev={get:fi(!1,!1)},Cv={get:fi(!1,!0)},Dv={get:fi(!0,!1)},xv={get:fi(!0,!0)};function rd(e,t,s){const i=Ae(s);if(i!==s&&t.call(e,i)){const o=Ja(e);Hs(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const od=new WeakMap,nd=new WeakMap,id=new WeakMap,ad=new WeakMap;function Sv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ov(e){return e.__v_skip||!Object.isExtensible(e)?0:Sv(Ja(e))}function hi(e){return or(e)?e:pi(e,!1,gv,Ev,od)}function ld(e){return pi(e,!1,_v,Cv,nd)}function ud(e){return pi(e,!0,vv,Dv,id)}function zs(e){return pi(e,!0,yv,xv,ad)}function pi(e,t,s,i,o){if(!Xe(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=o.get(e);if(a)return a;const u=Ov(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return o.set(e,c),c}function Xr(e){return or(e)?Xr(e.__v_raw):!!(e&&e.__v_isReactive)}function or(e){return!!(e&&e.__v_isReadonly)}function Qt(e){return!!(e&&e.__v_isShallow)}function mi(e){return e?!!e.__v_raw:!1}function Ae(e){const t=e&&e.__v_raw;return t?Ae(t):e}function hl(e){return!Ze(e,"__v_skip")&&Object.isExtensible(e)&&ai(e,"__v_skip",!0),e}const Ht=e=>Xe(e)?hi(e):e,pl=e=>Xe(e)?ud(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function cd(e){return dd(e,!1)}function Nv(e){return dd(e,!0)}function dd(e,t){return Dt(e)?e:new Iv(e,t)}class Iv{constructor(t,s){this.dep=new al,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ae(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Qt(t)||or(t);t=i?t:Ae(t),Or(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Nr(e){return Dt(e)?e.value:e}const Av={get:(e,t,s)=>t==="__v_raw"?e:Nr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const o=e[t];return Dt(o)&&!Dt(s)?(o.value=s,!0):Reflect.set(e,t,s,i)}};function fd(e){return Xr(e)?e:new Proxy(e,Av)}class Tv{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new al(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=cn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&ot!==this)return zc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return Kc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function Mv(e,t,s=!1){let i,o;Se(e)?i=e:(i=e.get,o=e.set);const a=new Tv(i,o,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const gi={},vi=new WeakMap;let eo;function Pv(e,t=!1,s=eo){if(s){let i=vi.get(s);i||vi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function kv(e,t,s=rt){const{immediate:i,deep:o,once:a,scheduler:u,augmentJob:c,call:f}=s,m=Y=>{(s.onWarn||Hs)("Invalid watch source: ",Y,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Y=>o?Y:Qt(Y)||o===!1||o===0?nr(Y,1):nr(Y);let v,w,D,k,U=!1,te=!1;if(Dt(e)?(w=()=>e.value,U=Qt(e)):Xr(e)?(w=()=>p(e),U=!0):ge(e)?(te=!0,U=e.some(Y=>Xr(Y)||Qt(Y)),w=()=>e.map(Y=>{if(Dt(Y))return Y.value;if(Xr(Y))return p(Y);if(Se(Y))return f?f(Y,2):Y();({}).NODE_ENV!=="production"&&m(Y)})):Se(e)?t?w=f?()=>f(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Y=eo;eo=v;try{return f?f(e,3,[k]):e(k)}finally{eo=Y}}:(w=Ot,{}.NODE_ENV!=="production"&&m(e)),t&&o){const Y=w,he=o===!0?1/0:o;w=()=>nr(Y(),he)}const T=uv(),oe=()=>{v.stop(),T&&T.active&&Ya(T.effects,v)};if(a&&t){const Y=t;t=(...he)=>{Y(...he),oe()}}let Q=te?new Array(e.length).fill(gi):gi;const we=Y=>{if(!(!(v.flags&1)||!v.dirty&&!Y))if(t){const he=v.run();if(o||U||(te?he.some((be,Te)=>Or(be,Q[Te])):Or(he,Q))){D&&D();const be=eo;eo=v;try{const Te=[he,Q===gi?void 0:te&&Q[0]===gi?[]:Q,k];f?f(t,3,Te):t(...Te),Q=he}finally{eo=be}}}else v.run()};return c&&c(we),v=new Hc(w),v.scheduler=u?()=>u(we,!1):we,k=Y=>Pv(Y,!1,v),D=v.onStop=()=>{const Y=vi.get(v);if(Y){if(f)f(Y,4);else for(const he of Y)he();vi.delete(v)}},{}.NODE_ENV!=="production"&&(v.onTrack=s.onTrack,v.onTrigger=s.onTrigger),t?i?we(!0):Q=v.run():u?u(we.bind(null,!0),!0):v.run(),oe.pause=v.pause.bind(v),oe.resume=v.resume.bind(v),oe.stop=oe,oe}function nr(e,t=1/0,s){if(t<=0||!Xe(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))nr(e.value,t,s);else if(ge(e))for(let i=0;i<e.length;i++)nr(e[i],t,s);else if(So(e)||Qr(e))e.forEach(i=>{nr(i,t,s)});else if(Fc(e)){for(const i in e)nr(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&nr(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const to=[];function _i(e){to.push(e)}function yi(){to.pop()}let ml=!1;function K(e,...t){if(ml)return;ml=!0,tr();const s=to.length?to[to.length-1].component:null,i=s&&s.appContext.config.warnHandler,o=Rv();if(i)Io(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,o.map(({vnode:a})=>`at <${Li(s,a.type)}>`).join(`
`),o]);else{const a=[`[Vue warn]: ${e}`,...t];o.length&&a.push(`
`,...Vv(o)),console.warn(...a)}sr(),ml=!1}function Rv(){let e=to[to.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function Vv(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...Fv(s))}),t}function Fv({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,o=` at <${Li(e.component,e.type,i)}`,a=">"+s;return e.props?[o,...Uv(e.props),a]:[o+a]}function Uv(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...hd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function hd(e,t,s){return ft(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=hd(e,Ae(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):Se(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Ae(t),s?t:[`${e}=`,t])}function Lv(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?K(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&K(`${t} is NaN - the duration expression might be incorrect.`))}const gl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Io(e,t,s,i){try{return i?e(...i):e()}catch(o){hn(o,t,s)}}function Ms(e,t,s,i){if(Se(e)){const o=Io(e,t,s,i);return o&&Za(o)&&o.catch(a=>{hn(a,t,s)}),o}if(ge(e)){const o=[];for(let a=0;a<e.length;a++)o.push(Ms(e[a],t,s,i));return o}else({}).NODE_ENV!=="production"&&K(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function hn(e,t,s,i=!0){const o=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||rt;if(t){let c=t.parent;const f=t.proxy,m={}.NODE_ENV!=="production"?gl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let v=0;v<p.length;v++)if(p[v](e,f,m)===!1)return}c=c.parent}if(a){tr(),Io(a,null,10,[e,f,m]),sr();return}}Bv(e,s,o,i,u)}function Bv(e,t,s,i=!0,o=!1){if({}.NODE_ENV!=="production"){const a=gl[t];if(s&&_i(s),K(`Unhandled error${a?` during execution of ${a}`:""}`),s&&yi(),i)throw e;console.error(e)}else{if(o)throw e;console.error(e)}}const Yt=[];let Ws=-1;const Ao=[];let Ir=null,To=0;const pd=Promise.resolve();let bi=null;const $v=100;function vl(e){const t=bi||pd;return e?t.then(this?e.bind(this):e):t}function jv(e){let t=Ws+1,s=Yt.length;for(;t<s;){const i=t+s>>>1,o=Yt[i],a=pn(o);a<e||a===e&&o.flags&2?t=i+1:s=i}return t}function wi(e){if(!(e.flags&1)){const t=pn(e),s=Yt[Yt.length-1];!s||!(e.flags&2)&&t>=pn(s)?Yt.push(e):Yt.splice(jv(t),0,e),e.flags|=1,md()}}function md(){bi||(bi=pd.then(yd))}function gd(e){ge(e)?Ao.push(...e):Ir&&e.id===-1?Ir.splice(To+1,0,e):e.flags&1||(Ao.push(e),e.flags|=1),md()}function vd(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Yt.length;s++){const i=Yt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&_l(t,i))continue;Yt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function _d(e){if(Ao.length){const t=[...new Set(Ao)].sort((s,i)=>pn(s)-pn(i));if(Ao.length=0,Ir){Ir.push(...t);return}for(Ir=t,{}.NODE_ENV!=="production"&&(e=e||new Map),To=0;To<Ir.length;To++){const s=Ir[To];({}).NODE_ENV!=="production"&&_l(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Ir=null,To=0}}const pn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>_l(e,s):Ot;try{for(Ws=0;Ws<Yt.length;Ws++){const s=Yt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Io(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Yt.length;Ws++){const s=Yt[Ws];s&&(s.flags&=-2)}Ws=-1,Yt.length=0,_d(e),bi=null,(Yt.length||Ao.length)&&yd(e)}}function _l(e,t){const s=e.get(t)||0;if(s>$v){const i=t.i,o=i&&ql(i.type);return hn(`Maximum recursive updates exceeded${o?` in component <${o}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let Ps=!1;const Ei=new Map;({}).NODE_ENV!=="production"&&(nn().__VUE_HMR_RUNTIME__={createRecord:yl(bd),rerender:yl(zv),reload:yl(Wv)});const so=new Map;function Hv(e){const t=e.type.__hmrId;let s=so.get(t);s||(bd(t,e.type),s=so.get(t)),s.instances.add(e)}function qv(e){so.get(e.type.__hmrId).instances.delete(e)}function bd(e,t){return so.has(e)?!1:(so.set(e,{initialDef:Ci(t),instances:new Set}),!0)}function Ci(e){return Pf(e)?e.__vccOpts:e}function zv(e,t){const s=so.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ci(i.type).render=t),i.renderCache=[],Ps=!0,i.update(),Ps=!1}))}function Wv(e,t){const s=so.get(e);if(!s)return;t=Ci(t),wd(s.initialDef,t);const i=[...s.instances];for(let o=0;o<i.length;o++){const a=i[o],u=Ci(a.type);let c=Ei.get(u);c||(u!==s.initialDef&&wd(u,t),Ei.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?wi(()=>{Ps=!0,a.parent.update(),Ps=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}gd(()=>{Ei.clear()})}function wd(e,t){pt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function yl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,mn=[],bl=!1;function gn(e,...t){Gs?Gs.emit(e,...t):bl||mn.push({event:e,args:t})}function Ed(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,mn.forEach(({event:o,args:a})=>Gs.emit(o,...a)),mn=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{Ed(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,bl=!0,mn=[])},3e3)):(bl=!0,mn=[])}function Gv(e,t){gn("app:init",e,t,{Fragment:Fe,Text:En,Comment:wt,Static:Cn})}function Kv(e){gn("app:unmount",e)}const Qv=wl("component:added"),Cd=wl("component:updated"),Yv=wl("component:removed"),Zv=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&Yv(e)};/*! #__NO_SIDE_EFFECTS__ */function wl(e){return t=>{gn(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Jv=Dd("perf:start"),Xv=Dd("perf:end");function Dd(e){return(t,s,i)=>{gn(e,t.appContext.app,t.uid,t,s,i)}}function e_(e,t,s){gn("component:emit",e.appContext.app,e,t,s)}let xt=null,xd=null;function Di(e){const t=xt;return xt=e,xd=e&&e.type.__scopeId||null,t}function ye(e,t=xt,s){if(!t||e._n)return e;const i=(...o)=>{i._d&&Cf(-1);const a=Di(t);let u;try{u=e(...o)}finally{Di(a),i._d&&Cf(1)}return{}.NODE_ENV!=="production"&&Cd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Sd(e){Wg(e)&&K("Do not use built-in directive ids as custom directive id: "+e)}function bt(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&K("withDirectives can only be used inside render functions."),e;const s=Ui(xt),i=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[a,u,c,f=rt]=t[o];a&&(Se(a)&&(a={mounted:a,updated:a}),a.deep&&nr(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:f}))}return e}function ro(e,t,s,i){const o=e.dirs,a=t&&t.dirs;for(let u=0;u<o.length;u++){const c=o[u];a&&(c.oldValue=a[u].value);let f=c.dir[i];f&&(tr(),Ms(f,s,8,[e.el,c,e,t]),sr())}}const Od=Symbol("_vte"),Nd=e=>e.__isTeleport,oo=e=>e&&(e.disabled||e.disabled===""),Id=e=>e&&(e.defer||e.defer===""),Ad=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Td=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,El=(e,t)=>{const s=e&&e.to;if(ft(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!oo(e)&&K(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&K("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!oo(e)&&K(`Invalid Teleport target: ${s}`),s},Md={name:"Teleport",__isTeleport:!0,process(e,t,s,i,o,a,u,c,f,m){const{mc:p,pc:v,pbc:w,o:{insert:D,querySelector:k,createText:U,createComment:te}}=m,T=oo(t.props);let{shapeFlag:oe,children:Q,dynamicChildren:we}=t;if({}.NODE_ENV!=="production"&&Ps&&(f=!1,we=null),e==null){const Y=t.el={}.NODE_ENV!=="production"?te("teleport start"):U(""),he=t.anchor={}.NODE_ENV!=="production"?te("teleport end"):U("");D(Y,s,i),D(he,s,i);const be=(le,ie)=>{oe&16&&(o&&o.isCE&&(o.ce._teleportTarget=le),p(Q,le,ie,o,a,u,c,f))},Te=()=>{const le=t.target=El(t.props,k),ie=Pd(le,t,U,D);le?(u!=="svg"&&Ad(le)?u="svg":u!=="mathml"&&Td(le)&&(u="mathml"),T||(be(le,ie),Si(t,!1))):{}.NODE_ENV!=="production"&&!T&&K("Invalid Teleport target on mount:",le,`(${typeof le})`)};T&&(be(s,he),Si(t,!0)),Id(t.props)?Jt(()=>{Te(),t.el.__isMounted=!0},a):Te()}else{if(Id(t.props)&&!e.el.__isMounted){Jt(()=>{Md.process(e,t,s,i,o,a,u,c,f,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Y=t.anchor=e.anchor,he=t.target=e.target,be=t.targetAnchor=e.targetAnchor,Te=oo(e.props),le=Te?s:he,ie=Te?Y:be;if(u==="svg"||Ad(he)?u="svg":(u==="mathml"||Td(he))&&(u="mathml"),we?(w(e.dynamicChildren,we,le,o,a,u,c),wn(e,t,!0)):f||v(e,t,le,ie,o,a,u,c,!1),T)Te?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):xi(t,s,Y,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Pe=t.target=El(t.props,k);Pe?xi(t,Pe,null,m,0):{}.NODE_ENV!=="production"&&K("Invalid Teleport target on update:",he,`(${typeof he})`)}else Te&&xi(t,he,be,m,1);Si(t,T)}},remove(e,t,s,{um:i,o:{remove:o}},a){const{shapeFlag:u,children:c,anchor:f,targetStart:m,targetAnchor:p,target:v,props:w}=e;if(v&&(o(m),o(p)),a&&o(f),u&16){const D=a||!oo(w);for(let k=0;k<c.length;k++){const U=c[k];i(U,t,s,D,!!U.dynamicChildren)}}},move:xi,hydrate:t_};function xi(e,t,s,{o:{insert:i},m:o},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:f,children:m,props:p}=e,v=a===2;if(v&&i(u,t,s),(!v||oo(p))&&f&16)for(let w=0;w<m.length;w++)o(m[w],t,s,2);v&&i(c,t,s)}function t_(e,t,s,i,o,a,{o:{nextSibling:u,parentNode:c,querySelector:f,insert:m,createText:p}},v){const w=t.target=El(t.props,f);if(w){const D=oo(t.props),k=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=v(u(e),t,c(e),s,i,o,a),t.targetStart=k,t.targetAnchor=k&&u(k);else{t.anchor=u(e);let U=k;for(;U;){if(U&&U.nodeType===8){if(U.data==="teleport start anchor")t.targetStart=U;else if(U.data==="teleport anchor"){t.targetAnchor=U,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}U=u(U)}t.targetAnchor||Pd(w,t,p,m),v(k&&u(k),t,w,s,i,o,a)}Si(t,D)}return t.anchor&&u(t.anchor)}const s_=Md;function Si(e,t){const s=e.ctx;if(s&&s.ut){let i,o;for(t?(i=e.el,o=e.anchor):(i=e.targetStart,o=e.targetAnchor);i&&i!==o;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Pd(e,t,s,i){const o=t.targetStart=s(""),a=t.targetAnchor=s("");return o[Od]=a,e&&(i(o,e),i(a,e)),a}const Ar=Symbol("_leaveCb"),Oi=Symbol("_enterCb");function r_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Hd(()=>{e.isMounted=!0}),qd(()=>{e.isUnmounting=!0}),e}const bs=[Function,Array],kd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:bs,onEnter:bs,onAfterEnter:bs,onEnterCancelled:bs,onBeforeLeave:bs,onLeave:bs,onAfterLeave:bs,onLeaveCancelled:bs,onBeforeAppear:bs,onAppear:bs,onAfterAppear:bs,onAppearCancelled:bs},Rd=e=>{const t=e.subTree;return t.component?Rd(t.component):t},o_={name:"BaseTransition",props:kd,setup(e,{slots:t}){const s=Vi(),i=r_();return()=>{const o=t.default&&Ld(t.default(),!0);if(!o||!o.length)return;const a=Vd(o),u=Ae(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&K(`invalid <transition> mode: ${c}`),i.isLeaving)return Dl(a);const f=Ud(a);if(!f)return Dl(a);let m=Cl(f,u,i,s,v=>m=v);f.type!==wt&&vn(f,m);let p=s.subTree&&Ud(s.subTree);if(p&&p.type!==wt&&!lo(f,p)&&Rd(s).type!==wt){let v=Cl(p,u,i,s);if(vn(p,v),c==="out-in"&&f.type!==wt)return i.isLeaving=!0,v.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete v.afterLeave,p=void 0},Dl(a);c==="in-out"&&f.type!==wt?v.delayLeave=(w,D,k)=>{const U=Fd(i,p);U[String(p.key)]=p,w[Ar]=()=>{D(),w[Ar]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{k(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Vd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){K("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const n_=o_;function Fd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function Cl(e,t,s,i,o){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:f,onEnter:m,onAfterEnter:p,onEnterCancelled:v,onBeforeLeave:w,onLeave:D,onAfterLeave:k,onLeaveCancelled:U,onBeforeAppear:te,onAppear:T,onAfterAppear:oe,onAppearCancelled:Q}=t,we=String(e.key),Y=Fd(s,e),he=(le,ie)=>{le&&Ms(le,i,9,ie)},be=(le,ie)=>{const Pe=ie[1];he(le,ie),ge(le)?le.every(ae=>ae.length<=1)&&Pe():le.length<=1&&Pe()},Te={mode:u,persisted:c,beforeEnter(le){let ie=f;if(!s.isMounted)if(a)ie=te||f;else return;le[Ar]&&le[Ar](!0);const Pe=Y[we];Pe&&lo(e,Pe)&&Pe.el[Ar]&&Pe.el[Ar](),he(ie,[le])},enter(le){let ie=m,Pe=p,ae=v;if(!s.isMounted)if(a)ie=T||m,Pe=oe||p,ae=Q||v;else return;let We=!1;const J=le[Oi]=$e=>{We||(We=!0,$e?he(ae,[le]):he(Pe,[le]),Te.delayedLeave&&Te.delayedLeave(),le[Oi]=void 0)};ie?be(ie,[le,J]):J()},leave(le,ie){const Pe=String(e.key);if(le[Oi]&&le[Oi](!0),s.isUnmounting)return ie();he(w,[le]);let ae=!1;const We=le[Ar]=J=>{ae||(ae=!0,ie(),J?he(U,[le]):he(k,[le]),le[Ar]=void 0,Y[Pe]===e&&delete Y[Pe])};Y[Pe]=e,D?be(D,[le,We]):We()},clone(le){const ie=Cl(le,t,s,i,o);return o&&o(ie),ie}};return Te}function Dl(e){if(_n(e))return e=Ks(e),e.children=null,e}function Ud(e){if(!_n(e))return Nd(e.type)&&e.children?Vd(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&Se(s.default))return s.default()}}function vn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,vn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ld(e,t=!1,s){let i=[],o=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Fe?(u.patchFlag&128&&o++,i=i.concat(Ld(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(o>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Bd(e,t){return Se(e)?(()=>pt({name:e.name},t,{setup:e}))():e}function $d(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const i_=new WeakSet;function Ni(e,t,s,i,o=!1){if(ge(e)){e.forEach((k,U)=>Ni(k,t&&(ge(t)?t[U]:t),s,i,o));return}if(Mo(i)&&!o){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Ni(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ui(i.component):i.el,u=o?null:a,{i:c,r:f}=e;if({}.NODE_ENV!=="production"&&!c){K("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===rt?c.refs={}:c.refs,v=c.setupState,w=Ae(v),D=v===rt?()=>!1:k=>({}).NODE_ENV!=="production"&&(Ze(w,k)&&!Dt(w[k])&&K(`Template ref "${k}" used on a non-ref value. It will not work in the production build.`),i_.has(w[k]))?!1:Ze(w,k);if(m!=null&&m!==f&&(ft(m)?(p[m]=null,D(m)&&(v[m]=null)):Dt(m)&&(m.value=null)),Se(f))Io(f,c,12,[u,p]);else{const k=ft(f),U=Dt(f);if(k||U){const te=()=>{if(e.f){const T=k?D(f)?v[f]:p[f]:f.value;o?ge(T)&&Ya(T,a):ge(T)?T.includes(a)||T.push(a):k?(p[f]=[a],D(f)&&(v[f]=p[f])):(f.value=[a],e.k&&(p[e.k]=f.value))}else k?(p[f]=u,D(f)&&(v[f]=u)):U?(f.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&K("Invalid template ref type:",f,`(${typeof f})`)};u?(te.id=-1,Jt(te,s)):te()}else({}).NODE_ENV!=="production"&&K("Invalid template ref type:",f,`(${typeof f})`)}}nn().requestIdleCallback,nn().cancelIdleCallback;const Mo=e=>!!e.type.__asyncLoader,_n=e=>e.type.__isKeepAlive;function a_(e,t){jd(e,"a",t)}function l_(e,t){jd(e,"da",t)}function jd(e,t,s=It){const i=e.__wdc||(e.__wdc=()=>{let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Ii(t,i,s),s){let o=s.parent;for(;o&&o.parent;)_n(o.parent.vnode)&&u_(i,t,s,o),o=o.parent}}function u_(e,t,s,i){const o=Ii(t,e,i,!0);zd(()=>{Ya(i[t],o)},s)}function Ii(e,t,s=It,i=!1){if(s){const o=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=Sn(s),f=Ms(t,s,e,u);return c(),sr(),f});return i?o.unshift(a):o.push(a),a}else if({}.NODE_ENV!=="production"){const o=Zr(gl[e].replace(/ hook$/,""));K(`${o} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=It)=>{(!On||e==="sp")&&Ii(e,(...i)=>t(...i),s)},c_=ir("bm"),Hd=ir("m"),d_=ir("bu"),f_=ir("u"),qd=ir("bum"),zd=ir("um"),h_=ir("sp"),p_=ir("rtg"),m_=ir("rtc");function g_(e,t=It){Ii("ec",e,t)}const xl="components",v_="directives";function X(e,t){return Wd(xl,e,!0,t)||e}const __=Symbol.for("v-ndc");function y_(e){return Wd(v_,e)}function Wd(e,t,s=!0,i=!1){const o=xt||It;if(o){const a=o.type;if(e===xl){const c=ql(a,!1);if(c&&(c===t||c===Kt(t)||c===Yr(Kt(t))))return a}const u=Gd(o[e]||a[e],t)||Gd(o.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===xl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";K(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&K(`resolve${Yr(e.slice(0,-1))} can only be used in render() or setup().`)}function Gd(e,t){return e&&(e[t]||e[Kt(t)]||e[Yr(Kt(t))])}function vt(e,t,s,i){let o;const a=s&&s[i],u=ge(e);if(u||ft(e)){const c=u&&Xr(e);let f=!1;c&&(f=!Qt(e),e=ui(e)),o=new Array(e.length);for(let m=0,p=e.length;m<p;m++)o[m]=t(f?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&K(`The v-for range expect an integer value but got ${e}.`),o=new Array(e);for(let c=0;c<e;c++)o[c]=t(c+1,c,void 0,a&&a[c])}else if(Xe(e))if(e[Symbol.iterator])o=Array.from(e,(c,f)=>t(c,f,void 0,a&&a[f]));else{const c=Object.keys(e);o=new Array(c.length);for(let f=0,m=c.length;f<m;f++){const p=c[f];o[f]=t(e[p],p,f,a&&a[f])}}else o=[];return s&&(s[i]=o),o}function Vt(e,t,s={},i,o){if(xt.ce||xt.parent&&Mo(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),N(),Pt(Fe,null,[A("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(K("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),N();const u=a&&Kd(a(s)),c=s.key||u&&u.key,f=Pt(Fe,{key:(c&&!As(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!o&&f.scopeId&&(f.slotScopeIds=[f.scopeId+"-s"]),a&&a._c&&(a._d=!0),f}function Kd(e){return e.some(t=>ao(t)?!(t.type===wt||t.type===Fe&&!Kd(t.children)):!0)?e:null}const Sl=e=>e?If(e)?Ui(e):Sl(e.parent):null,no=pt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Sl(e.parent),$root:e=>Sl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Al(e),$forceUpdate:e=>e.f||(e.f=()=>{wi(e.update)}),$nextTick:e=>e.n||(e.n=vl.bind(e.proxy)),$watch:e=>J_.bind(e)}),Ol=e=>e==="_"||e==="$",Nl=(e,t)=>e!==rt&&!e.__isScriptSetup&&Ze(e,t),Qd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:o,props:a,accessCache:u,type:c,appContext:f}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return o[t];case 4:return s[t];case 3:return a[t]}else{if(Nl(i,t))return u[t]=1,i[t];if(o!==rt&&Ze(o,t))return u[t]=2,o[t];if((m=e.propsOptions[0])&&Ze(m,t))return u[t]=3,a[t];if(s!==rt&&Ze(s,t))return u[t]=4,s[t];Il&&(u[t]=0)}}const p=no[t];let v,w;if(p)return t==="$attrs"?(Nt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&ki()):{}.NODE_ENV!=="production"&&t==="$slots"&&Nt(e,"get",t),p(e);if((v=c.__cssModules)&&(v=v[t]))return v;if(s!==rt&&Ze(s,t))return u[t]=4,s[t];if(w=f.config.globalProperties,Ze(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!ft(t)||t.indexOf("__v")!==0)&&(o!==rt&&Ol(t[0])&&Ze(o,t)?K(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&K(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:o,ctx:a}=e;return Nl(o,t)?(o[t]=s,!0):{}.NODE_ENV!=="production"&&o.__isScriptSetup&&Ze(o,t)?(K(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==rt&&Ze(i,t)?(i[t]=s,!0):Ze(e.props,t)?({}.NODE_ENV!=="production"&&K(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&K(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:o,propsOptions:a}},u){let c;return!!s[u]||e!==rt&&Ze(e,u)||Nl(t,u)||(c=a[0])&&Ze(c,u)||Ze(i,u)||Ze(no,u)||Ze(o.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ze(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Qd.ownKeys=e=>(K("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function b_(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(no).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>no[s](e),set:Ot})}),t}function w_(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Ot})})}function E_(e){const{ctx:t,setupState:s}=e;Object.keys(Ae(s)).forEach(i=>{if(!s.__isScriptSetup){if(Ol(i[0])){K(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Ot})}})}function Yd(e){return ge(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function C_(){const e=Object.create(null);return(t,s)=>{e[s]?K(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Il=!0;function D_(e){const t=Al(e),s=e.proxy,i=e.ctx;Il=!1,t.beforeCreate&&Zd(t.beforeCreate,e,"bc");const{data:o,computed:a,methods:u,watch:c,provide:f,inject:m,created:p,beforeMount:v,mounted:w,beforeUpdate:D,updated:k,activated:U,deactivated:te,beforeDestroy:T,beforeUnmount:oe,destroyed:Q,unmounted:we,render:Y,renderTracked:he,renderTriggered:be,errorCaptured:Te,serverPrefetch:le,expose:ie,inheritAttrs:Pe,components:ae,directives:We,filters:J}=t,$e={}.NODE_ENV!=="production"?C_():null;if({}.NODE_ENV!=="production"){const[Oe]=e.propsOptions;if(Oe)for(const Ee in Oe)$e("Props",Ee)}if(m&&x_(m,i,$e),u)for(const Oe in u){const Ee=u[Oe];Se(Ee)?({}.NODE_ENV!=="production"?Object.defineProperty(i,Oe,{value:Ee.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[Oe]=Ee.bind(s),{}.NODE_ENV!=="production"&&$e("Methods",Oe)):{}.NODE_ENV!=="production"&&K(`Method "${Oe}" has type "${typeof Ee}" in the component definition. Did you reference the function correctly?`)}if(o){({}).NODE_ENV!=="production"&&!Se(o)&&K("The data option must be a function. Plain object usage is no longer supported.");const Oe=o.call(s,s);if({}.NODE_ENV!=="production"&&Za(Oe)&&K("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Xe(Oe))({}).NODE_ENV!=="production"&&K("data() should return an object.");else if(e.data=hi(Oe),{}.NODE_ENV!=="production")for(const Ee in Oe)$e("Data",Ee),Ol(Ee[0])||Object.defineProperty(i,Ee,{configurable:!0,enumerable:!0,get:()=>Oe[Ee],set:Ot})}if(Il=!0,a)for(const Oe in a){const Ee=a[Oe],Ut=Se(Ee)?Ee.bind(s,s):Se(Ee.get)?Ee.get.bind(s,s):Ot;({}).NODE_ENV!=="production"&&Ut===Ot&&K(`Computed property "${Oe}" has no getter.`);const Xt=!Se(Ee)&&Se(Ee.set)?Ee.set.bind(s):{}.NODE_ENV!=="production"?()=>{K(`Write operation failed: computed property "${Oe}" is readonly.`)}:Ot,_t=Vs({get:Ut,set:Xt});Object.defineProperty(i,Oe,{enumerable:!0,configurable:!0,get:()=>_t.value,set:de=>_t.value=de}),{}.NODE_ENV!=="production"&&$e("Computed",Oe)}if(c)for(const Oe in c)Jd(c[Oe],i,s,Oe);if(f){const Oe=Se(f)?f.call(s):f;Reflect.ownKeys(Oe).forEach(Ee=>{Ti(Ee,Oe[Ee])})}p&&Zd(p,e,"c");function ut(Oe,Ee){ge(Ee)?Ee.forEach(Ut=>Oe(Ut.bind(s))):Ee&&Oe(Ee.bind(s))}if(ut(c_,v),ut(Hd,w),ut(d_,D),ut(f_,k),ut(a_,U),ut(l_,te),ut(g_,Te),ut(m_,he),ut(p_,be),ut(qd,oe),ut(zd,we),ut(h_,le),ge(ie))if(ie.length){const Oe=e.exposed||(e.exposed={});ie.forEach(Ee=>{Object.defineProperty(Oe,Ee,{get:()=>s[Ee],set:Ut=>s[Ee]=Ut})})}else e.exposed||(e.exposed={});Y&&e.render===Ot&&(e.render=Y),Pe!=null&&(e.inheritAttrs=Pe),ae&&(e.components=ae),We&&(e.directives=We),le&&$d(e)}function x_(e,t,s=Ot){ge(e)&&(e=Tl(e));for(const i in e){const o=e[i];let a;Xe(o)?"default"in o?a=ks(o.from||i,o.default,!0):a=ks(o.from||i):a=ks(o),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Zd(e,t,s){Ms(ge(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Jd(e,t,s,i){let o=i.includes(".")?vf(s,i):()=>s[i];if(ft(e)){const a=t[e];Se(a)?ko(o,a):{}.NODE_ENV!=="production"&&K(`Invalid watch handler specified by key "${e}"`,a)}else if(Se(e))ko(o,e.bind(s));else if(Xe(e))if(ge(e))e.forEach(a=>Jd(a,t,s,i));else{const a=Se(e.handler)?e.handler.bind(s):t[e.handler];Se(a)?ko(o,a,e):{}.NODE_ENV!=="production"&&K(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&K(`Invalid watch option: "${i}"`,e)}function Al(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:o,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let f;return c?f=c:!o.length&&!s&&!i?f=t:(f={},o.length&&o.forEach(m=>Ai(f,m,u,!0)),Ai(f,t,u)),Xe(t)&&a.set(t,f),f}function Ai(e,t,s,i=!1){const{mixins:o,extends:a}=t;a&&Ai(e,a,s,!0),o&&o.forEach(u=>Ai(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&K('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=S_[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const S_={data:Xd,props:ef,emits:ef,methods:yn,computed:yn,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:yn,directives:yn,watch:N_,provide:Xd,inject:O_};function Xd(e,t){return t?e?function(){return pt(Se(e)?e.call(this,this):e,Se(t)?t.call(this,this):t)}:t:e}function O_(e,t){return yn(Tl(e),Tl(t))}function Tl(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function yn(e,t){return e?pt(Object.create(null),e,t):t}function ef(e,t){return e?ge(e)&&ge(t)?[...new Set([...e,...t])]:pt(Object.create(null),Yd(e),Yd(t??{})):t}function N_(e,t){if(!e)return t;if(!t)return e;const s=pt(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function tf(){return{app:null,config:{isNativeTag:qg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let I_=0;function A_(e,t){return function(i,o=null){Se(i)||(i=pt({},i)),o!=null&&!Xe(o)&&({}.NODE_ENV!=="production"&&K("root props passed to app.mount() must be an object."),o=null);const a=tf(),u=new WeakSet,c=[];let f=!1;const m=a.app={_uid:I_++,_component:i,_props:o,_container:null,_context:a,_instance:null,version:kf,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&K("app.config cannot be replaced. Modify individual options instead.")},use(p,...v){return u.has(p)?{}.NODE_ENV!=="production"&&K("Plugin has already been applied to target app."):p&&Se(p.install)?(u.add(p),p.install(m,...v)):Se(p)?(u.add(p),p(m,...v)):{}.NODE_ENV!=="production"&&K('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&K("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,v){return{}.NODE_ENV!=="production"&&jl(p,a.config),v?({}.NODE_ENV!=="production"&&a.components[p]&&K(`Component "${p}" has already been registered in target app.`),a.components[p]=v,m):a.components[p]},directive(p,v){return{}.NODE_ENV!=="production"&&Sd(p),v?({}.NODE_ENV!=="production"&&a.directives[p]&&K(`Directive "${p}" has already been registered in target app.`),a.directives[p]=v,m):a.directives[p]},mount(p,v,w){if(f)({}).NODE_ENV!=="production"&&K("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&K("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||A(i,o);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),v&&t?t(D,p):e(D,p,w),f=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,Gv(m,kf)),Ui(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&K(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){f?(Ms(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,Kv(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&K("Cannot unmount an app that is not mounted.")},provide(p,v){return{}.NODE_ENV!=="production"&&p in a.provides&&K(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=v,m},runWithContext(p){const v=Po;Po=m;try{return p()}finally{Po=v}}};return m}}let Po=null;function Ti(e,t){if(!It)({}).NODE_ENV!=="production"&&K("provide() can only be used inside setup().");else{let s=It.provides;const i=It.parent&&It.parent.provides;i===s&&(s=It.provides=Object.create(i)),s[e]=t}}function ks(e,t,s=!1){const i=It||xt;if(i||Po){const o=Po?Po._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return s&&Se(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&K(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&K("inject() can only be used inside setup() or functional components.")}const sf={},rf=()=>Object.create(sf),of=e=>Object.getPrototypeOf(e)===sf;function T_(e,t,s,i=!1){const o={},a=rf();e.propsDefaults=Object.create(null),nf(e,t,o,a);for(const u in e.propsOptions[0])u in o||(o[u]=void 0);({}).NODE_ENV!=="production"&&uf(t||{},o,e),s?e.props=i?o:ld(o):e.type.props?e.props=o:e.props=a,e.attrs=a}function M_(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function P_(e,t,s,i){const{props:o,attrs:a,vnode:{patchFlag:u}}=e,c=Ae(o),[f]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&M_(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let v=0;v<p.length;v++){let w=p[v];if(Pi(e.emitsOptions,w))continue;const D=t[w];if(f)if(Ze(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const k=Kt(w);o[k]=Ml(f,c,k,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{nf(e,t,o,a)&&(m=!0);let p;for(const v in c)(!t||!Ze(t,v)&&((p=Sr(v))===v||!Ze(t,p)))&&(f?s&&(s[v]!==void 0||s[p]!==void 0)&&(o[v]=Ml(f,c,v,void 0,e,!0)):delete o[v]);if(a!==c)for(const v in a)(!t||!Ze(t,v))&&(delete a[v],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&uf(t||{},o,e)}function nf(e,t,s,i){const[o,a]=e.propsOptions;let u=!1,c;if(t)for(let f in t){if(on(f))continue;const m=t[f];let p;o&&Ze(o,p=Kt(f))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Pi(e.emitsOptions,f)||(!(f in i)||m!==i[f])&&(i[f]=m,u=!0)}if(a){const f=Ae(s),m=c||rt;for(let p=0;p<a.length;p++){const v=a[p];s[v]=Ml(o,f,v,m[v],e,!Ze(m,v))}}return u}function Ml(e,t,s,i,o,a){const u=e[s];if(u!=null){const c=Ze(u,"default");if(c&&i===void 0){const f=u.default;if(u.type!==Function&&!u.skipFactory&&Se(f)){const{propsDefaults:m}=o;if(s in m)i=m[s];else{const p=Sn(o);i=m[s]=f.call(null,t),p()}}else i=f;o.ce&&o.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Sr(s))&&(i=!0))}return i}const k_=new WeakMap;function af(e,t,s=!1){const i=s?k_:t.propsCache,o=i.get(e);if(o)return o;const a=e.props,u={},c=[];let f=!1;if(!Se(e)){const p=v=>{f=!0;const[w,D]=af(v,t,!0);pt(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!f)return Xe(e)&&i.set(e,xo),xo;if(ge(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!ft(a[p])&&K("props must be strings when using array syntax.",a[p]);const v=Kt(a[p]);lf(v)&&(u[v]=rt)}else if(a){({}).NODE_ENV!=="production"&&!Xe(a)&&K("invalid props options",a);for(const p in a){const v=Kt(p);if(lf(v)){const w=a[p],D=u[v]=ge(w)||Se(w)?{type:w}:pt({},w),k=D.type;let U=!1,te=!0;if(ge(k))for(let T=0;T<k.length;++T){const oe=k[T],Q=Se(oe)&&oe.name;if(Q==="Boolean"){U=!0;break}else Q==="String"&&(te=!1)}else U=Se(k)&&k.name==="Boolean";D[0]=U,D[1]=te,(U||Ze(D,"default"))&&c.push(v)}}}const m=[u,c];return Xe(e)&&i.set(e,m),m}function lf(e){return e[0]!=="$"&&!on(e)?!0:({}.NODE_ENV!=="production"&&K(`Invalid prop name: "${e}" is a reserved property.`),!1)}function R_(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function uf(e,t,s){const i=Ae(t),o=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in o){let c=o[u];c!=null&&V_(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function V_(e,t,s,i,o){const{type:a,required:u,validator:c,skipCheck:f}=s;if(u&&o){K('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!f){let m=!1;const p=ge(a)?a:[a],v=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:k}=U_(t,p[w]);v.push(k||""),m=D}if(!m){K(L_(e,t,v));return}}c&&!c(t,i)&&K('Invalid prop: custom validator check failed for prop "'+e+'".')}}const F_=er("String,Number,Boolean,Function,Symbol,BigInt");function U_(e,t){let s;const i=R_(t);if(i==="null")s=e===null;else if(F_(i)){const o=typeof e;s=o===i.toLowerCase(),!s&&o==="object"&&(s=e instanceof t)}else i==="Object"?s=Xe(e):i==="Array"?s=ge(e):s=e instanceof t;return{valid:s,expectedType:i}}function L_(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Yr).join(" | ")}`;const o=s[0],a=Ja(t),u=cf(t,o),c=cf(t,a);return s.length===1&&df(o)&&!B_(o,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,df(a)&&(i+=`with value ${c}.`),i}function cf(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function df(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function B_(...e){return e.some(t=>t.toLowerCase()==="boolean")}const ff=e=>e[0]==="_"||e==="$stable",Pl=e=>ge(e)?e.map(Rs):[Rs(e)],$_=(e,t,s)=>{if(t._n)return t;const i=ye((...o)=>({}.NODE_ENV!=="production"&&It&&(!s||s.root===It.root)&&K(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Pl(t(...o))),s);return i._c=!1,i},hf=(e,t,s)=>{const i=e._ctx;for(const o in e){if(ff(o))continue;const a=e[o];if(Se(a))t[o]=$_(o,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&K(`Non-function value encountered for slot "${o}". Prefer function slots for better performance.`);const u=Pl(a);t[o]=()=>u}}},pf=(e,t)=>{({}).NODE_ENV!=="production"&&!_n(e.vnode)&&K("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Pl(t);e.slots.default=()=>s},kl=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},j_=(e,t,s)=>{const i=e.slots=rf();if(e.vnode.shapeFlag&32){const o=t._;o?(kl(i,t,s),s&&ai(i,"_",o,!0)):hf(t,i)}else t&&pf(e,t)},H_=(e,t,s)=>{const{vnode:i,slots:o}=e;let a=!0,u=rt;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&Ps?(kl(o,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:kl(o,t,s):(a=!t.$stable,hf(t,o)),u=t}else t&&(pf(e,t),u={default:1});if(a)for(const c in o)!ff(c)&&u[c]==null&&delete o[c]};let bn,Tr;function ar(e,t){e.appContext.config.performance&&Mi()&&Tr.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&Jv(e,t,Mi()?Tr.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Mi()){const s=`vue-${t}-${e.uid}`,i=s+":end";Tr.mark(i),Tr.measure(`<${Li(e,e.type)}> ${t}`,s,i),Tr.clearMarks(s),Tr.clearMarks(i)}({}).NODE_ENV!=="production"&&Xv(e,t,Mi()?Tr.now():Date.now())}function Mi(){return bn!==void 0||(typeof window<"u"&&window.performance?(bn=!0,Tr=window.performance):bn=!1),bn}function q_(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=ny;function z_(e){return W_(e)}function W_(e,t){q_();const s=nn();s.__VUE__=!0,{}.NODE_ENV!=="production"&&Ed(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:o,patchProp:a,createElement:u,createText:c,createComment:f,setText:m,setElementText:p,parentNode:v,nextSibling:w,setScopeId:D=Ot,insertStaticContent:k}=e,U=(b,C,P,F=null,j=null,q=null,ee=void 0,W=null,Z={}.NODE_ENV!=="production"&&Ps?!1:!!C.dynamicChildren)=>{if(b===C)return;b&&!lo(b,C)&&(F=ne(b),Ke(b,j,q,!0),b=null),C.patchFlag===-2&&(Z=!1,C.dynamicChildren=null);const{type:z,ref:Ce,shapeFlag:se}=C;switch(z){case En:te(b,C,P,F);break;case wt:T(b,C,P,F);break;case Cn:b==null?oe(C,P,F,ee):{}.NODE_ENV!=="production"&&Q(b,C,P,ee);break;case Fe:We(b,C,P,F,j,q,ee,W,Z);break;default:se&1?he(b,C,P,F,j,q,ee,W,Z):se&6?J(b,C,P,F,j,q,ee,W,Z):se&64||se&128?z.process(b,C,P,F,j,q,ee,W,Z,ke):{}.NODE_ENV!=="production"&&K("Invalid VNode type:",z,`(${typeof z})`)}Ce!=null&&j&&Ni(Ce,b&&b.ref,q,C||b,!C)},te=(b,C,P,F)=>{if(b==null)i(C.el=c(C.children),P,F);else{const j=C.el=b.el;C.children!==b.children&&m(j,C.children)}},T=(b,C,P,F)=>{b==null?i(C.el=f(C.children||""),P,F):C.el=b.el},oe=(b,C,P,F)=>{[b.el,b.anchor]=k(b.children,C,P,F,b.el,b.anchor)},Q=(b,C,P,F)=>{if(C.children!==b.children){const j=w(b.anchor);Y(b),[C.el,C.anchor]=k(C.children,P,j,F)}else C.el=b.el,C.anchor=b.anchor},we=({el:b,anchor:C},P,F)=>{let j;for(;b&&b!==C;)j=w(b),i(b,P,F),b=j;i(C,P,F)},Y=({el:b,anchor:C})=>{let P;for(;b&&b!==C;)P=w(b),o(b),b=P;o(C)},he=(b,C,P,F,j,q,ee,W,Z)=>{C.type==="svg"?ee="svg":C.type==="math"&&(ee="mathml"),b==null?be(C,P,F,j,q,ee,W,Z):ie(b,C,j,q,ee,W,Z)},be=(b,C,P,F,j,q,ee,W)=>{let Z,z;const{props:Ce,shapeFlag:se,transition:_e,dirs:De}=b;if(Z=b.el=u(b.type,q,Ce&&Ce.is,Ce),se&8?p(Z,b.children):se&16&&le(b.children,Z,null,F,j,Rl(b,q),ee,W),De&&ro(b,null,F,"created"),Te(Z,b,b.scopeId,ee,F),Ce){for(const tt in Ce)tt!=="value"&&!on(tt)&&a(Z,tt,null,Ce[tt],q,F);"value"in Ce&&a(Z,"value",null,Ce.value,q),(z=Ce.onVnodeBeforeMount)&&Qs(z,F,b)}({}).NODE_ENV!=="production"&&(ai(Z,"__vnode",b,!0),ai(Z,"__vueParentComponent",F,!0)),De&&ro(b,null,F,"beforeMount");const Ue=G_(j,_e);Ue&&_e.beforeEnter(Z),i(Z,C,P),((z=Ce&&Ce.onVnodeMounted)||Ue||De)&&Jt(()=>{z&&Qs(z,F,b),Ue&&_e.enter(Z),De&&ro(b,null,F,"mounted")},j)},Te=(b,C,P,F,j)=>{if(P&&D(b,P),F)for(let q=0;q<F.length;q++)D(b,F[q]);if(j){let q=j.subTree;if({}.NODE_ENV!=="production"&&q.patchFlag>0&&q.patchFlag&2048&&(q=Ll(q.children)||q),C===q||Ef(q.type)&&(q.ssContent===C||q.ssFallback===C)){const ee=j.vnode;Te(b,ee,ee.scopeId,ee.slotScopeIds,j.parent)}}},le=(b,C,P,F,j,q,ee,W,Z=0)=>{for(let z=Z;z<b.length;z++){const Ce=b[z]=W?Mr(b[z]):Rs(b[z]);U(null,Ce,C,P,F,j,q,ee,W)}},ie=(b,C,P,F,j,q,ee)=>{const W=C.el=b.el;({}).NODE_ENV!=="production"&&(W.__vnode=C);let{patchFlag:Z,dynamicChildren:z,dirs:Ce}=C;Z|=b.patchFlag&16;const se=b.props||rt,_e=C.props||rt;let De;if(P&&io(P,!1),(De=_e.onVnodeBeforeUpdate)&&Qs(De,P,C,b),Ce&&ro(C,b,P,"beforeUpdate"),P&&io(P,!0),{}.NODE_ENV!=="production"&&Ps&&(Z=0,ee=!1,z=null),(se.innerHTML&&_e.innerHTML==null||se.textContent&&_e.textContent==null)&&p(W,""),z?(Pe(b.dynamicChildren,z,W,P,F,Rl(C,j),q),{}.NODE_ENV!=="production"&&wn(b,C)):ee||Ut(b,C,W,null,P,F,Rl(C,j),q,!1),Z>0){if(Z&16)ae(W,se,_e,P,j);else if(Z&2&&se.class!==_e.class&&a(W,"class",null,_e.class,j),Z&4&&a(W,"style",se.style,_e.style,j),Z&8){const Ue=C.dynamicProps;for(let tt=0;tt<Ue.length;tt++){const Je=Ue[tt],Lt=se[Je],St=_e[Je];(St!==Lt||Je==="value")&&a(W,Je,Lt,St,j,P)}}Z&1&&b.children!==C.children&&p(W,C.children)}else!ee&&z==null&&ae(W,se,_e,P,j);((De=_e.onVnodeUpdated)||Ce)&&Jt(()=>{De&&Qs(De,P,C,b),Ce&&ro(C,b,P,"updated")},F)},Pe=(b,C,P,F,j,q,ee)=>{for(let W=0;W<C.length;W++){const Z=b[W],z=C[W],Ce=Z.el&&(Z.type===Fe||!lo(Z,z)||Z.shapeFlag&70)?v(Z.el):P;U(Z,z,Ce,null,F,j,q,ee,!0)}},ae=(b,C,P,F,j)=>{if(C!==P){if(C!==rt)for(const q in C)!on(q)&&!(q in P)&&a(b,q,C[q],null,j,F);for(const q in P){if(on(q))continue;const ee=P[q],W=C[q];ee!==W&&q!=="value"&&a(b,q,W,ee,j,F)}"value"in P&&a(b,"value",C.value,P.value,j)}},We=(b,C,P,F,j,q,ee,W,Z)=>{const z=C.el=b?b.el:c(""),Ce=C.anchor=b?b.anchor:c("");let{patchFlag:se,dynamicChildren:_e,slotScopeIds:De}=C;({}).NODE_ENV!=="production"&&(Ps||se&2048)&&(se=0,Z=!1,_e=null),De&&(W=W?W.concat(De):De),b==null?(i(z,P,F),i(Ce,P,F),le(C.children||[],P,Ce,j,q,ee,W,Z)):se>0&&se&64&&_e&&b.dynamicChildren?(Pe(b.dynamicChildren,_e,P,j,q,ee,W),{}.NODE_ENV!=="production"?wn(b,C):(C.key!=null||j&&C===j.subTree)&&wn(b,C,!0)):Ut(b,C,P,Ce,j,q,ee,W,Z)},J=(b,C,P,F,j,q,ee,W,Z)=>{C.slotScopeIds=W,b==null?C.shapeFlag&512?j.ctx.activate(C,P,F,ee,Z):$e(C,P,F,j,q,ee,Z):ut(b,C,Z)},$e=(b,C,P,F,j,q,ee)=>{const W=b.component=hy(b,F,j);if({}.NODE_ENV!=="production"&&W.type.__hmrId&&Hv(W),{}.NODE_ENV!=="production"&&(_i(b),ar(W,"mount")),_n(b)&&(W.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ar(W,"init"),my(W,!1,ee),{}.NODE_ENV!=="production"&&lr(W,"init"),W.asyncDep){if({}.NODE_ENV!=="production"&&Ps&&(b.el=null),j&&j.registerDep(W,Oe,ee),!b.el){const Z=W.subTree=A(wt);T(null,Z,C,P)}}else Oe(W,b,C,P,j,q,ee);({}).NODE_ENV!=="production"&&(yi(),lr(W,"mount"))},ut=(b,C,P)=>{const F=C.component=b.component;if(ry(b,C,P))if(F.asyncDep&&!F.asyncResolved){({}).NODE_ENV!=="production"&&_i(C),Ee(F,C,P),{}.NODE_ENV!=="production"&&yi();return}else F.next=C,F.update();else C.el=b.el,F.vnode=C},Oe=(b,C,P,F,j,q,ee)=>{const W=()=>{if(b.isMounted){let{next:se,bu:_e,u:De,parent:Ue,vnode:tt}=b;{const Bt=mf(b);if(Bt){se&&(se.el=tt.el,Ee(b,se,ee)),Bt.asyncDep.then(()=>{b.isUnmounted||W()});return}}let Je=se,Lt;({}).NODE_ENV!=="production"&&_i(se||b.vnode),io(b,!1),se?(se.el=tt.el,Ee(b,se,ee)):se=tt,_e&&Oo(_e),(Lt=se.props&&se.props.onVnodeBeforeUpdate)&&Qs(Lt,Ue,se,tt),io(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const St=Ul(b);({}).NODE_ENV!=="production"&&lr(b,"render");const es=b.subTree;b.subTree=St,{}.NODE_ENV!=="production"&&ar(b,"patch"),U(es,St,v(es.el),ne(es),b,j,q),{}.NODE_ENV!=="production"&&lr(b,"patch"),se.el=St.el,Je===null&&oy(b,St.el),De&&Jt(De,j),(Lt=se.props&&se.props.onVnodeUpdated)&&Jt(()=>Qs(Lt,Ue,se,tt),j),{}.NODE_ENV!=="production"&&Cd(b),{}.NODE_ENV!=="production"&&yi()}else{let se;const{el:_e,props:De}=C,{bm:Ue,m:tt,parent:Je,root:Lt,type:St}=b,es=Mo(C);if(io(b,!1),Ue&&Oo(Ue),!es&&(se=De&&De.onVnodeBeforeMount)&&Qs(se,Je,C),io(b,!0),_e&&Re){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Ul(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),Re(_e,b.subTree,b,j,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};es&&St.__asyncHydrate?St.__asyncHydrate(_e,b,Bt):Bt()}else{Lt.ce&&Lt.ce._injectChildStyle(St),{}.NODE_ENV!=="production"&&ar(b,"render");const Bt=b.subTree=Ul(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),U(null,Bt,P,F,b,j,q),{}.NODE_ENV!=="production"&&lr(b,"patch"),C.el=Bt.el}if(tt&&Jt(tt,j),!es&&(se=De&&De.onVnodeMounted)){const Bt=C;Jt(()=>Qs(se,Je,Bt),j)}(C.shapeFlag&256||Je&&Mo(Je.vnode)&&Je.vnode.shapeFlag&256)&&b.a&&Jt(b.a,j),b.isMounted=!0,{}.NODE_ENV!=="production"&&Qv(b),C=P=F=null}};b.scope.on();const Z=b.effect=new Hc(W);b.scope.off();const z=b.update=Z.run.bind(Z),Ce=b.job=Z.runIfDirty.bind(Z);Ce.i=b,Ce.id=b.uid,Z.scheduler=()=>wi(Ce),io(b,!0),{}.NODE_ENV!=="production"&&(Z.onTrack=b.rtc?se=>Oo(b.rtc,se):void 0,Z.onTrigger=b.rtg?se=>Oo(b.rtg,se):void 0),z()},Ee=(b,C,P)=>{C.component=b;const F=b.vnode.props;b.vnode=C,b.next=null,P_(b,C.props,F,P),H_(b,C.children,P),tr(),vd(b),sr()},Ut=(b,C,P,F,j,q,ee,W,Z=!1)=>{const z=b&&b.children,Ce=b?b.shapeFlag:0,se=C.children,{patchFlag:_e,shapeFlag:De}=C;if(_e>0){if(_e&128){_t(z,se,P,F,j,q,ee,W,Z);return}else if(_e&256){Xt(z,se,P,F,j,q,ee,W,Z);return}}De&8?(Ce&16&&R(z,j,q),se!==z&&p(P,se)):Ce&16?De&16?_t(z,se,P,F,j,q,ee,W,Z):R(z,j,q,!0):(Ce&8&&p(P,""),De&16&&le(se,P,F,j,q,ee,W,Z))},Xt=(b,C,P,F,j,q,ee,W,Z)=>{b=b||xo,C=C||xo;const z=b.length,Ce=C.length,se=Math.min(z,Ce);let _e;for(_e=0;_e<se;_e++){const De=C[_e]=Z?Mr(C[_e]):Rs(C[_e]);U(b[_e],De,P,null,j,q,ee,W,Z)}z>Ce?R(b,j,q,!0,!1,se):le(C,P,F,j,q,ee,W,Z,se)},_t=(b,C,P,F,j,q,ee,W,Z)=>{let z=0;const Ce=C.length;let se=b.length-1,_e=Ce-1;for(;z<=se&&z<=_e;){const De=b[z],Ue=C[z]=Z?Mr(C[z]):Rs(C[z]);if(lo(De,Ue))U(De,Ue,P,null,j,q,ee,W,Z);else break;z++}for(;z<=se&&z<=_e;){const De=b[se],Ue=C[_e]=Z?Mr(C[_e]):Rs(C[_e]);if(lo(De,Ue))U(De,Ue,P,null,j,q,ee,W,Z);else break;se--,_e--}if(z>se){if(z<=_e){const De=_e+1,Ue=De<Ce?C[De].el:F;for(;z<=_e;)U(null,C[z]=Z?Mr(C[z]):Rs(C[z]),P,Ue,j,q,ee,W,Z),z++}}else if(z>_e)for(;z<=se;)Ke(b[z],j,q,!0),z++;else{const De=z,Ue=z,tt=new Map;for(z=Ue;z<=_e;z++){const At=C[z]=Z?Mr(C[z]):Rs(C[z]);At.key!=null&&({}.NODE_ENV!=="production"&&tt.has(At.key)&&K("Duplicate keys found during update:",JSON.stringify(At.key),"Make sure keys are unique."),tt.set(At.key,z))}let Je,Lt=0;const St=_e-Ue+1;let es=!1,Bt=0;const gr=new Array(St);for(z=0;z<St;z++)gr[z]=0;for(z=De;z<=se;z++){const At=b[z];if(Lt>=St){Ke(At,j,q,!0);continue}let ws;if(At.key!=null)ws=tt.get(At.key);else for(Je=Ue;Je<=_e;Je++)if(gr[Je-Ue]===0&&lo(At,C[Je])){ws=Je;break}ws===void 0?Ke(At,j,q,!0):(gr[ws-Ue]=z+1,ws>=Bt?Bt=ws:es=!0,U(At,C[ws],P,null,j,q,ee,W,Z),Lt++)}const $o=es?K_(gr):xo;for(Je=$o.length-1,z=St-1;z>=0;z--){const At=Ue+z,ws=C[At],aa=At+1<Ce?C[At+1].el:F;gr[z]===0?U(null,ws,P,aa,j,q,ee,W,Z):es&&(Je<0||z!==$o[Je]?de(ws,P,aa,2):Je--)}}},de=(b,C,P,F,j=null)=>{const{el:q,type:ee,transition:W,children:Z,shapeFlag:z}=b;if(z&6){de(b.component.subTree,C,P,F);return}if(z&128){b.suspense.move(C,P,F);return}if(z&64){ee.move(b,C,P,ke);return}if(ee===Fe){i(q,C,P);for(let se=0;se<Z.length;se++)de(Z[se],C,P,F);i(b.anchor,C,P);return}if(ee===Cn){we(b,C,P);return}if(F!==2&&z&1&&W)if(F===0)W.beforeEnter(q),i(q,C,P),Jt(()=>W.enter(q),j);else{const{leave:se,delayLeave:_e,afterLeave:De}=W,Ue=()=>i(q,C,P),tt=()=>{se(q,()=>{Ue(),De&&De()})};_e?_e(q,Ue,tt):tt()}else i(q,C,P)},Ke=(b,C,P,F=!1,j=!1)=>{const{type:q,props:ee,ref:W,children:Z,dynamicChildren:z,shapeFlag:Ce,patchFlag:se,dirs:_e,cacheIndex:De}=b;if(se===-2&&(j=!1),W!=null&&Ni(W,null,P,b,!0),De!=null&&(C.renderCache[De]=void 0),Ce&256){C.ctx.deactivate(b);return}const Ue=Ce&1&&_e,tt=!Mo(b);let Je;if(tt&&(Je=ee&&ee.onVnodeBeforeUnmount)&&Qs(Je,C,b),Ce&6)hs(b.component,P,F);else{if(Ce&128){b.suspense.unmount(P,F);return}Ue&&ro(b,null,C,"beforeUnmount"),Ce&64?b.type.remove(b,C,P,ke,F):z&&!z.hasOnce&&(q!==Fe||se>0&&se&64)?R(z,C,P,!1,!0):(q===Fe&&se&384||!j&&Ce&16)&&R(Z,C,P),F&&fs(b)}(tt&&(Je=ee&&ee.onVnodeUnmounted)||Ue)&&Jt(()=>{Je&&Qs(Je,C,b),Ue&&ro(b,null,C,"unmounted")},P)},fs=b=>{const{type:C,el:P,anchor:F,transition:j}=b;if(C===Fe){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&j&&!j.persisted?b.children.forEach(ee=>{ee.type===wt?o(ee.el):fs(ee)}):zt(P,F);return}if(C===Cn){Y(b);return}const q=()=>{o(P),j&&!j.persisted&&j.afterLeave&&j.afterLeave()};if(b.shapeFlag&1&&j&&!j.persisted){const{leave:ee,delayLeave:W}=j,Z=()=>ee(P,q);W?W(b.el,q,Z):Z()}else q()},zt=(b,C)=>{let P;for(;b!==C;)P=w(b),o(b),b=P;o(C)},hs=(b,C,P)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&qv(b);const{bum:F,scope:j,job:q,subTree:ee,um:W,m:Z,a:z}=b;gf(Z),gf(z),F&&Oo(F),j.stop(),q&&(q.flags|=8,Ke(ee,b,C,P)),W&&Jt(W,C),Jt(()=>{b.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&Zv(b)},R=(b,C,P,F=!1,j=!1,q=0)=>{for(let ee=q;ee<b.length;ee++)Ke(b[ee],C,P,F,j)},ne=b=>{if(b.shapeFlag&6)return ne(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const C=w(b.anchor||b.el),P=C&&C[Od];return P?w(P):C};let re=!1;const me=(b,C,P)=>{b==null?C._vnode&&Ke(C._vnode,null,null,!0):U(C._vnode||null,b,C,null,null,null,P),C._vnode=b,re||(re=!0,vd(),_d(),re=!1)},ke={p:U,um:Ke,m:de,r:fs,mt:$e,mc:le,pc:Ut,pbc:Pe,n:ne,o:e};let at,Re;return t&&([at,Re]=t(ke)),{render:me,hydrate:at,createApp:A_(me,at)}}function Rl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function io({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function G_(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function wn(e,t,s=!1){const i=e.children,o=t.children;if(ge(i)&&ge(o))for(let a=0;a<i.length;a++){const u=i[a];let c=o[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=o[a]=Mr(o[a]),c.el=u.el),!s&&c.patchFlag!==-2&&wn(u,c)),c.type===En&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function K_(e){const t=e.slice(),s=[0];let i,o,a,u,c;const f=e.length;for(i=0;i<f;i++){const m=e[i];if(m!==0){if(o=s[s.length-1],e[o]<m){t[i]=o,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function mf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:mf(t)}function gf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Q_=Symbol.for("v-scx"),Y_=()=>{{const e=ks(Q_);return e||{}.NODE_ENV!=="production"&&K("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Z_(e,t){return Vl(e,null,t)}function ko(e,t,s){return{}.NODE_ENV!=="production"&&!Se(t)&&K("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Vl(e,t,s)}function Vl(e,t,s=rt){const{immediate:i,deep:o,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&K('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),o!==void 0&&K('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&K('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=pt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=K);const f=t&&i||!t&&a!=="post";let m;if(On){if(a==="sync"){const D=Y_();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!f){const D=()=>{};return D.stop=Ot,D.resume=Ot,D.pause=Ot,D}}const p=It;c.call=(D,k,U)=>Ms(D,p,k,U);let v=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(v=!0,c.scheduler=(D,k)=>{k?D():wi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),v&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=kv(e,t,c);return On&&(m?m.push(w):f&&w()),w}function J_(e,t,s){const i=this.proxy,o=ft(e)?e.includes(".")?vf(i,e):()=>i[e]:e.bind(i,i);let a;Se(t)?a=t:(a=t.handler,s=t);const u=Sn(this),c=Vl(o,a.bind(i),s);return u(),c}function vf(e,t){const s=t.split(".");return()=>{let i=e;for(let o=0;o<s.length&&i;o++)i=i[s[o]];return i}}const X_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Sr(t)}Modifiers`];function ey(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||rt;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[v]}=e;if(p)if(!(t in p))(!v||!(Zr(Kt(t))in v))&&K(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Zr(Kt(t))}" prop.`);else{const w=p[t];Se(w)&&(w(...s)||K(`Invalid event arguments: event validation failed for event "${t}".`))}}let o=s;const a=t.startsWith("update:"),u=a&&X_(i,t.slice(7));if(u&&(u.trim&&(o=s.map(p=>ft(p)?p.trim():p)),u.number&&(o=s.map(li))),{}.NODE_ENV!=="production"&&e_(e,t,o),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Zr(p)]&&K(`Event "${p}" is emitted in component ${Li(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Sr(t)}" instead of "${t}".`)}let c,f=i[c=Zr(t)]||i[c=Zr(Kt(t))];!f&&a&&(f=i[c=Zr(Sr(t))]),f&&Ms(f,e,6,o);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ms(m,e,6,o)}}function _f(e,t,s=!1){const i=t.emitsCache,o=i.get(e);if(o!==void 0)return o;const a=e.emits;let u={},c=!1;if(!Se(e)){const f=m=>{const p=_f(m,t,!0);p&&(c=!0,pt(u,p))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!a&&!c?(Xe(e)&&i.set(e,null),null):(ge(a)?a.forEach(f=>u[f]=null):pt(u,a),Xe(e)&&i.set(e,u),u)}function Pi(e,t){return!e||!sn(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ze(e,t[0].toLowerCase()+t.slice(1))||Ze(e,Sr(t))||Ze(e,t))}let Fl=!1;function ki(){Fl=!0}function Ul(e){const{type:t,vnode:s,proxy:i,withProxy:o,propsOptions:[a],slots:u,attrs:c,emit:f,render:m,renderCache:p,props:v,data:w,setupState:D,ctx:k,inheritAttrs:U}=e,te=Di(e);let T,oe;({}).NODE_ENV!=="production"&&(Fl=!1);try{if(s.shapeFlag&4){const Y=o||i,he={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Y,{get(be,Te,le){return K(`Property '${String(Te)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(be,Te,le)}}):Y;T=Rs(m.call(he,Y,p,{}.NODE_ENV!=="production"?zs(v):v,D,w,k)),oe=c}else{const Y=t;({}).NODE_ENV!=="production"&&c===v&&ki(),T=Rs(Y.length>1?Y({}.NODE_ENV!=="production"?zs(v):v,{}.NODE_ENV!=="production"?{get attrs(){return ki(),zs(c)},slots:u,emit:f}:{attrs:c,slots:u,emit:f}):Y({}.NODE_ENV!=="production"?zs(v):v,null)),oe=t.props?c:ty(c)}}catch(Y){Dn.length=0,hn(Y,e,1),T=A(wt)}let Q=T,we;if({}.NODE_ENV!=="production"&&T.patchFlag>0&&T.patchFlag&2048&&([Q,we]=yf(T)),oe&&U!==!1){const Y=Object.keys(oe),{shapeFlag:he}=Q;if(Y.length){if(he&7)a&&Y.some(ni)&&(oe=sy(oe,a)),Q=Ks(Q,oe,!1,!0);else if({}.NODE_ENV!=="production"&&!Fl&&Q.type!==wt){const be=Object.keys(c),Te=[],le=[];for(let ie=0,Pe=be.length;ie<Pe;ie++){const ae=be[ie];sn(ae)?ni(ae)||Te.push(ae[2].toLowerCase()+ae.slice(3)):le.push(ae)}le.length&&K(`Extraneous non-props attributes (${le.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Te.length&&K(`Extraneous non-emits event listeners (${Te.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!bf(Q)&&K("Runtime directive used on component with non-element root node. The directives will not function as intended."),Q=Ks(Q,null,!1,!0),Q.dirs=Q.dirs?Q.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!bf(Q)&&K("Component inside <Transition> renders non-element root node that cannot be animated."),vn(Q,s.transition)),{}.NODE_ENV!=="production"&&we?we(Q):T=Q,Di(te),T}const yf=e=>{const t=e.children,s=e.dynamicChildren,i=Ll(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return yf(i)}else return[e,void 0];const o=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[o]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Rs(i),u]};function Ll(e,t=!0){let s;for(let i=0;i<e.length;i++){const o=e[i];if(ao(o)){if(o.type!==wt||o.children==="v-if"){if(s)return;if(s=o,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ll(s.children)}}else return}return s}const ty=e=>{let t;for(const s in e)(s==="class"||s==="style"||sn(s))&&((t||(t={}))[s]=e[s]);return t},sy=(e,t)=>{const s={};for(const i in e)(!ni(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},bf=e=>e.shapeFlag&7||e.type===wt;function ry(e,t,s){const{props:i,children:o,component:a}=e,{props:u,children:c,patchFlag:f}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(o||c)&&Ps||t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return i?wf(i,u,m):!!u;if(f&8){const p=t.dynamicProps;for(let v=0;v<p.length;v++){const w=p[v];if(u[w]!==i[w]&&!Pi(m,w))return!0}}}else return(o||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?wf(i,u,m):!0:!!u;return!1}function wf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let o=0;o<i.length;o++){const a=i[o];if(t[a]!==e[a]&&!Pi(s,a))return!0}return!1}function oy({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const Ef=e=>e.__isSuspense;function ny(e,t){t&&t.pendingBranch?ge(e)?t.effects.push(...e):t.effects.push(e):gd(e)}const Fe=Symbol.for("v-fgt"),En=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Cn=Symbol.for("v-stc"),Dn=[];let cs=null;function N(e=!1){Dn.push(cs=e?null:[])}function iy(){Dn.pop(),cs=Dn[Dn.length-1]||null}let xn=1;function Cf(e,t=!1){xn+=e,e<0&&cs&&t&&(cs.hasOnce=!0)}function Df(e){return e.dynamicChildren=xn>0?cs||xo:null,iy(),xn>0&&cs&&cs.push(e),e}function I(e,t,s,i,o,a){return Df(h(e,t,s,i,o,a,!0))}function Pt(e,t,s,i,o){return Df(A(e,t,s,i,o,!0))}function ao(e){return e?e.__v_isVNode===!0:!1}function lo(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Ei.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const ay=(...e)=>Sf(...e),xf=({key:e})=>e??null,Ri=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ft(e)||Dt(e)||Se(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function h(e,t=null,s=null,i=0,o=null,a=e===Fe?0:1,u=!1,c=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&xf(t),ref:t&&Ri(t),scopeId:xd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:xt};return c?(Bl(f,s),a&128&&e.normalize(f)):s&&(f.shapeFlag|=ft(s)?8:16),{}.NODE_ENV!=="production"&&f.key!==f.key&&K("VNode created with invalid key (NaN). VNode type:",f.type),xn>0&&!u&&cs&&(f.patchFlag>0||a&6)&&f.patchFlag!==32&&cs.push(f),f}const A={}.NODE_ENV!=="production"?ay:Sf;function Sf(e,t=null,s=null,i=0,o=null,a=!1){if((!e||e===__)&&({}.NODE_ENV!=="production"&&!e&&K(`Invalid vnode type when creating vnode: ${e}.`),e=wt),ao(e)){const c=Ks(e,t,!0);return s&&Bl(c,s),xn>0&&!a&&cs&&(c.shapeFlag&6?cs[cs.indexOf(e)]=c:cs.push(c)),c.patchFlag=-2,c}if(Pf(e)&&(e=e.__vccOpts),t){t=ly(t);let{class:c,style:f}=t;c&&!ft(c)&&(t.class=pe(c)),Xe(f)&&(mi(f)&&!ge(f)&&(f=pt({},f)),t.style=ls(f))}const u=ft(e)?1:Ef(e)?128:Nd(e)?64:Xe(e)?4:Se(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&mi(e)&&(e=Ae(e),K("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),h(e,t,s,i,o,u,a,!0)}function ly(e){return e?mi(e)||of(e)?pt({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:o,ref:a,patchFlag:u,children:c,transition:f}=e,m=t?cy(o||{},t):o,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&xf(m),ref:t&&t.ref?s&&a?ge(a)?a.concat(Ri(t)):[a,Ri(t)]:Ri(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&ge(c)?c.map(Of):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&i&&vn(p,f.clone(p)),p}function Of(e){const t=Ks(e);return ge(e.children)&&(t.children=e.children.map(Of)),t}function nt(e=" ",t=0){return A(En,null,e,t)}function uy(e,t){const s=A(Cn,null,e);return s.staticCount=t,s}function ce(e="",t=!1){return t?(N(),Pt(wt,null,e)):A(wt,null,e)}function Rs(e){return e==null||typeof e=="boolean"?A(wt):ge(e)?A(Fe,null,e.slice()):ao(e)?Mr(e):A(En,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Bl(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(ge(t))s=16;else if(typeof t=="object")if(i&65){const o=t.default;o&&(o._c&&(o._d=!1),Bl(e,o()),o._c&&(o._d=!0));return}else{s=32;const o=t._;!o&&!of(t)?t._ctx=xt:o===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Se(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[nt(t)]):s=8);e.children=t,e.shapeFlag|=s}function cy(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const o in i)if(o==="class")t.class!==i.class&&(t.class=pe([t.class,i.class]));else if(o==="style")t.style=ls([t.style,i.style]);else if(sn(o)){const a=t[o],u=i[o];u&&a!==u&&!(ge(a)&&a.includes(u))&&(t[o]=a?[].concat(a,u):u)}else o!==""&&(t[o]=i[o])}return t}function Qs(e,t,s,i=null){Ms(e,t,7,[s,i])}const dy=tf();let fy=0;function hy(e,t,s){const i=e.type,o=(t?t.appContext:e.appContext)||dy,a={uid:fy++,vnode:e,type:i,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new jc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:af(i,o),emitsOptions:_f(i,o),emit:null,emitted:null,propsDefaults:rt,inheritAttrs:i.inheritAttrs,ctx:rt,data:rt,props:rt,attrs:rt,slots:rt,refs:rt,setupState:rt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=b_(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=ey.bind(null,a),e.ce&&e.ce(a),a}let It=null;const Vi=()=>It||xt;let Fi,$l;{const e=nn(),t=(s,i)=>{let o;return(o=e[s])||(o=e[s]=[]),o.push(i),a=>{o.length>1?o.forEach(u=>u(a)):o[0](a)}};Fi=t("__VUE_INSTANCE_SETTERS__",s=>It=s),$l=t("__VUE_SSR_SETTERS__",s=>On=s)}const Sn=e=>{const t=It;return Fi(e),e.scope.on(),()=>{e.scope.off(),Fi(t)}},Nf=()=>{It&&It.scope.off(),Fi(null)},py=er("slot,component");function jl(e,{isNativeTag:t}){(py(e)||t(e))&&K("Do not use built-in or reserved HTML elements as component id: "+e)}function If(e){return e.vnode.shapeFlag&4}let On=!1;function my(e,t=!1,s=!1){t&&$l(t);const{props:i,children:o}=e.vnode,a=If(e);T_(e,i,a,t),j_(e,o,s);const u=a?gy(e,t):void 0;return t&&$l(!1),u}function gy(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&jl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)jl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Sd(a[u])}i.compilerOptions&&vy()&&K('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Qd),{}.NODE_ENV!=="production"&&w_(e);const{setup:o}=i;if(o){tr();const a=e.setupContext=o.length>1?yy(e):null,u=Sn(e),c=Io(o,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),f=Za(c);if(sr(),u(),(f||e.sp)&&!Mo(e)&&$d(e),f){if(c.then(Nf,Nf),t)return c.then(m=>{Af(e,m,t)}).catch(m=>{hn(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";K(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Af(e,c,t)}else Tf(e,t)}function Af(e,t,s){Se(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Xe(t)?({}.NODE_ENV!=="production"&&ao(t)&&K("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=fd(t),{}.NODE_ENV!=="production"&&E_(e)):{}.NODE_ENV!=="production"&&t!==void 0&&K(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Tf(e,s)}let Hl;const vy=()=>!Hl;function Tf(e,t,s){const i=e.type;if(!e.render){if(!t&&Hl&&!i.render){const o=i.template||Al(e).template;if(o){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:f}=i,m=pt(pt({isCustomElement:a,delimiters:c},u),f);i.render=Hl(o,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||Ot}{const o=Sn(e);tr();try{D_(e)}finally{sr(),o()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Ot&&!t&&(i.template?K('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):K("Component is missing template or render function: ",i))}const Mf={}.NODE_ENV!=="production"?{get(e,t){return ki(),Nt(e,"get",""),e[t]},set(){return K("setupContext.attrs is readonly."),!1},deleteProperty(){return K("setupContext.attrs is readonly."),!1}}:{get(e,t){return Nt(e,"get",""),e[t]}};function _y(e){return new Proxy(e.slots,{get(t,s){return Nt(e,"get","$slots"),t[s]}})}function yy(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&K("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(ge(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&K(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Mf))},get slots(){return i||(i=_y(e))},get emit(){return(o,...a)=>e.emit(o,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Mf),slots:e.slots,emit:e.emit,expose:t}}function Ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fd(hl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in no)return no[s](e)},has(t,s){return s in t||s in no}})):e.proxy}const by=/(?:^|[-_])(\w)/g,wy=e=>e.replace(by,t=>t.toUpperCase()).replace(/[-_]/g,"");function ql(e,t=!0){return Se(e)?e.displayName||e.name:e.name||t&&e.__name}function Li(e,t,s=!1){let i=ql(t);if(!i&&t.__file){const o=t.__file.match(/([^/\\]+)\.\w+$/);o&&(i=o[1])}if(!i&&e&&e.parent){const o=a=>{for(const u in a)if(a[u]===t)return u};i=o(e.components||e.parent.type.components)||o(e.appContext.components)}return i?wy(i):s?"App":"Anonymous"}function Pf(e){return Se(e)&&"__vccOpts"in e}const Vs=(e,t)=>{const s=Mv(e,t,On);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function zl(e,t,s){const i=arguments.length;return i===2?Xe(t)&&!ge(t)?ao(t)?A(e,null,[t]):A(e,t):A(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&ao(s)&&(s=[s]),A(e,t,s))}function Ey(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},o={__vue_custom_formatter:!0,header(v){return Xe(v)?v.__isVue?["div",e,"VueInstance"]:Dt(v)?["div",{},["span",e,p(v)],"<",c("_value"in v?v._value:v),">"]:Xr(v)?["div",{},["span",e,Qt(v)?"ShallowReactive":"Reactive"],"<",c(v),`>${or(v)?" (readonly)":""}`]:or(v)?["div",{},["span",e,Qt(v)?"ShallowReadonly":"Readonly"],"<",c(v),">"]:null:null},hasBody(v){return v&&v.__isVue},body(v){if(v&&v.__isVue)return["div",{},...a(v.$)]}};function a(v){const w=[];v.type.props&&v.props&&w.push(u("props",Ae(v.props))),v.setupState!==rt&&w.push(u("setup",v.setupState)),v.data!==rt&&w.push(u("data",Ae(v.data)));const D=f(v,"computed");D&&w.push(u("computed",D));const k=f(v,"inject");return k&&w.push(u("injected",k)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:v}]]),w}function u(v,w){return w=pt({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},v],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(v,w=!0){return typeof v=="number"?["span",t,v]:typeof v=="string"?["span",s,JSON.stringify(v)]:typeof v=="boolean"?["span",i,v]:Xe(v)?["object",{object:w?Ae(v):v}]:["span",s,String(v)]}function f(v,w){const D=v.type;if(Se(D))return;const k={};for(const U in v.ctx)m(D,U,w)&&(k[U]=v.ctx[U]);return k}function m(v,w,D){const k=v[D];if(ge(k)&&k.includes(w)||Xe(k)&&w in k||v.extends&&m(v.extends,w,D)||v.mixins&&v.mixins.some(U=>m(U,w,D)))return!0}function p(v){return Qt(v)?"ShallowRef":v.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(o):window.devtoolsFormatters=[o]}const kf="3.5.13",Ys={}.NODE_ENV!=="production"?K:Ot;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Wl;const Rf=typeof window<"u"&&window.trustedTypes;if(Rf)try{Wl=Rf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Ys(`Error creating trusted types policy: ${e}`)}const Vf=Wl?e=>Wl.createHTML(e):e=>e,Cy="http://www.w3.org/2000/svg",Dy="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,Ff=ur&&ur.createElement("template"),xy={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const o=t==="svg"?ur.createElementNS(Cy,e):t==="mathml"?ur.createElementNS(Dy,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&o.setAttribute("multiple",i.multiple),o},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,o,a){const u=s?s.previousSibling:t.lastChild;if(o&&(o===a||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),s),!(o===a||!(o=o.nextSibling)););else{Ff.innerHTML=Vf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Ff.content;if(i==="svg"||i==="mathml"){const f=c.firstChild;for(;f.firstChild;)c.appendChild(f.firstChild);c.removeChild(f)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",Nn="animation",In=Symbol("_vtc"),Uf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Sy=pt({},kd,Uf),Lf=(e=>(e.displayName="Transition",e.props=Sy,e))((e,{slots:t})=>zl(n_,Oy(e),t)),uo=(e,t=[])=>{ge(e)?e.forEach(s=>s(...t)):e&&e(...t)},Bf=e=>e?ge(e)?e.some(t=>t.length>1):e.length>1:!1;function Oy(e){const t={};for(const ae in e)ae in Uf||(t[ae]=e[ae]);if(e.css===!1)return t;const{name:s="v",type:i,duration:o,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:f=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:v=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,k=Ny(o),U=k&&k[0],te=k&&k[1],{onBeforeEnter:T,onEnter:oe,onEnterCancelled:Q,onLeave:we,onLeaveCancelled:Y,onBeforeAppear:he=T,onAppear:be=oe,onAppearCancelled:Te=Q}=t,le=(ae,We,J,$e)=>{ae._enterCancelled=$e,co(ae,We?p:c),co(ae,We?m:u),J&&J()},ie=(ae,We)=>{ae._isLeaving=!1,co(ae,v),co(ae,D),co(ae,w),We&&We()},Pe=ae=>(We,J)=>{const $e=ae?be:oe,ut=()=>le(We,ae,J);uo($e,[We,ut]),$f(()=>{co(We,ae?f:a),cr(We,ae?p:c),Bf($e)||jf(We,i,U,ut)})};return pt(t,{onBeforeEnter(ae){uo(T,[ae]),cr(ae,a),cr(ae,u)},onBeforeAppear(ae){uo(he,[ae]),cr(ae,f),cr(ae,m)},onEnter:Pe(!1),onAppear:Pe(!0),onLeave(ae,We){ae._isLeaving=!0;const J=()=>ie(ae,We);cr(ae,v),ae._enterCancelled?(cr(ae,w),zf()):(zf(),cr(ae,w)),$f(()=>{ae._isLeaving&&(co(ae,v),cr(ae,D),Bf(we)||jf(ae,i,te,J))}),uo(we,[ae,J])},onEnterCancelled(ae){le(ae,!1,void 0,!0),uo(Q,[ae])},onAppearCancelled(ae){le(ae,!0,void 0,!0),uo(Te,[ae])},onLeaveCancelled(ae){ie(ae),uo(Y,[ae])}})}function Ny(e){if(e==null)return null;if(Xe(e))return[Gl(e.enter),Gl(e.leave)];{const t=Gl(e);return[t,t]}}function Gl(e){const t=Qg(e);return{}.NODE_ENV!=="production"&&Lv(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[In]||(e[In]=new Set)).add(t)}function co(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[In];s&&(s.delete(t),s.size||(e[In]=void 0))}function $f(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Iy=0;function jf(e,t,s,i){const o=e._endId=++Iy,a=()=>{o===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:f}=Ay(e,t);if(!u)return i();const m=u+"end";let p=0;const v=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=f&&v()};setTimeout(()=>{p<f&&v()},c+1),e.addEventListener(m,w)}function Ay(e,t){const s=window.getComputedStyle(e),i=k=>(s[k]||"").split(", "),o=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Hf(o,a),c=i(`${Nn}Delay`),f=i(`${Nn}Duration`),m=Hf(c,f);let p=null,v=0,w=0;t===Pr?u>0&&(p=Pr,v=u,w=a.length):t===Nn?m>0&&(p=Nn,v=m,w=f.length):(v=Math.max(u,m),p=v>0?u>m?Pr:Nn:null,w=p?p===Pr?a.length:f.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:v,propCount:w,hasTransform:D}}function Hf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>qf(s)+qf(e[i])))}function qf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function zf(){return document.body.offsetHeight}function Ty(e,t,s){const i=e[In];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Bi=Symbol("_vod"),Wf=Symbol("_vsh"),Kl={beforeMount(e,{value:t},{transition:s}){e[Bi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):An(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),An(e,!0),i.enter(e)):i.leave(e,()=>{An(e,!1)}):An(e,t))},beforeUnmount(e,{value:t}){An(e,t)}};({}).NODE_ENV!=="production"&&(Kl.name="show");function An(e,t){e.style.display=t?e[Bi]:"none",e[Wf]=!t}const My=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Py=/(^|;)\s*display\s*:/;function ky(e,t,s){const i=e.style,o=ft(s);let a=!1;if(s&&!o){if(t)if(ft(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&$i(i,c,"")}else for(const u in t)s[u]==null&&$i(i,u,"");for(const u in s)u==="display"&&(a=!0),$i(i,u,s[u])}else if(o){if(t!==s){const u=i[My];u&&(s+=";"+u),i.cssText=s,a=Py.test(s)}}else t&&e.removeAttribute("style");Bi in e&&(e[Bi]=a?i.display:"",e[Wf]&&(i.display="none"))}const Ry=/[^\\];\s*$/,Gf=/\s*!important$/;function $i(e,t,s){if(ge(s))s.forEach(i=>$i(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Ry.test(s)&&Ys(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Vy(e,t);Gf.test(s)?e.setProperty(Sr(i),s.replace(Gf,""),"important"):e[i]=s}}const Kf=["Webkit","Moz","ms"],Ql={};function Vy(e,t){const s=Ql[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Ql[t]=i;i=Yr(i);for(let o=0;o<Kf.length;o++){const a=Kf[o]+i;if(a in e)return Ql[t]=a}return t}const Qf="http://www.w3.org/1999/xlink";function Yf(e,t,s,i,o,a=iv(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Qf,t.slice(6,t.length)):e.setAttributeNS(Qf,t,s):s==null||a&&!Lc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":As(s)?String(s):s)}function Zf(e,t,s,i,o){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Vf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(c!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Lc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Ys(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(o||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function Fy(e,t,s,i){e.removeEventListener(t,s,i)}const Jf=Symbol("_vei");function Uy(e,t,s,i,o=null){const a=e[Jf]||(e[Jf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?eh(i,t):i;else{const[c,f]=Ly(t);if(i){const m=a[t]=jy({}.NODE_ENV!=="production"?eh(i,t):i,o);kr(e,c,m,f)}else u&&(Fy(e,c,u,f),a[t]=void 0)}}const Xf=/(?:Once|Passive|Capture)$/;function Ly(e){let t;if(Xf.test(e)){t={};let i;for(;i=e.match(Xf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Sr(e.slice(2)),t]}let Yl=0;const By=Promise.resolve(),$y=()=>Yl||(By.then(()=>Yl=0),Yl=Date.now());function jy(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ms(Hy(i,s.value),t,5,[i])};return s.value=e,s.attached=$y(),s}function eh(e,t){return Se(e)||ge(e)?e:(Ys(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Ot)}function Hy(e,t){if(ge(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>o=>!o._stopped&&i&&i(o))}else return t}const th=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,qy=(e,t,s,i,o,a)=>{const u=o==="svg";t==="class"?Ty(e,i,u):t==="style"?ky(e,s,i):sn(t)?ni(t)||Uy(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):zy(e,t,i,u))?(Zf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Yf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ft(i))?Zf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Yf(e,t,i,u))};function zy(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&th(t)&&Se(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return th(t)&&ft(s)?!1:t in e}const Ro=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ge(t)?s=>Oo(t,s):t};function Wy(e){e.target.composing=!0}function sh(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),Fs={created(e,{modifiers:{lazy:t,trim:s,number:i}},o){e[dr]=Ro(o);const a=i||o.props&&o.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=li(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",Wy),kr(e,"compositionend",sh),kr(e,"change",sh))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:o,number:a}},u){if(e[dr]=Ro(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?li(e.value):e.value,f=t??"";c!==f&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||o&&e.value.trim()===f)||(e.value=f))}},ji={deep:!0,created(e,t,s){e[dr]=Ro(s),kr(e,"change",()=>{const i=e._modelValue,o=Tn(e),a=e.checked,u=e[dr];if(ge(i)){const c=el(i,o),f=c!==-1;if(a&&!f)u(i.concat(o));else if(!a&&f){const m=[...i];m.splice(c,1),u(m)}}else if(So(i)){const c=new Set(i);a?c.add(o):c.delete(o),u(c)}else u(nh(e,a))})},mounted:rh,beforeUpdate(e,t,s){e[dr]=Ro(s),rh(e,t,s)}};function rh(e,{value:t,oldValue:s},i){e._modelValue=t;let o;if(ge(t))o=el(t,i.props.value)>-1;else if(So(t))o=t.has(i.props.value);else{if(t===s)return;o=an(t,nh(e,!0))}e.checked!==o&&(e.checked=o)}const Zl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const o=So(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?li(Tn(u)):Tn(u));e[dr](e.multiple?o?new Set(a):a:a[0]),e._assigning=!0,vl(()=>{e._assigning=!1})}),e[dr]=Ro(i)},mounted(e,{value:t}){oh(e,t)},beforeUpdate(e,t,s){e[dr]=Ro(s)},updated(e,{value:t}){e._assigning||oh(e,t)}};function oh(e,t){const s=e.multiple,i=ge(t);if(s&&!i&&!So(t)){({}).NODE_ENV!=="production"&&Ys(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let o=0,a=e.options.length;o<a;o++){const u=e.options[o],c=Tn(u);if(s)if(i){const f=typeof c;f==="string"||f==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=el(t,c)>-1}else u.selected=t.has(c);else if(an(Tn(u),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Tn(e){return"_value"in e?e._value:e.value}function nh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Gy=["ctrl","shift","alt","meta"],Ky={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Gy.some(s=>e[`${s}Key`]&&!t.includes(s))},Ft=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(o,...a)=>{for(let u=0;u<t.length;u++){const c=Ky[t[u]];if(c&&c(o,t))return}return e(o,...a)})},Qy=pt({patchProp:qy},xy);let ih;function Yy(){return ih||(ih=z_(Qy))}const Zy=(...e)=>{const t=Yy().createApp(...e);({}).NODE_ENV!=="production"&&(Xy(t),eb(t));const{mount:s}=t;return t.mount=i=>{const o=tb(i);if(!o)return;const a=t._component;!Se(a)&&!a.render&&!a.template&&(a.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const u=s(o,!1,Jy(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),u},t};function Jy(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Xy(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>rv(t)||ov(t)||nv(t),writable:!1})}function eb(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Ys("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Ys(i),s},set(){Ys(i)}})}}function tb(e){if(ft(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Ys(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Ys('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function sb(){Ey()}({}).NODE_ENV!=="production"&&sb();var rb=!1;function ob(){return ah().__VUE_DEVTOOLS_GLOBAL_HOOK__}function ah(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const nb=typeof Proxy=="function",ib="devtools-plugin:setup",ab="plugin:settings:set";let Vo,Jl;function lb(){var e;return Vo!==void 0||(typeof window<"u"&&window.performance?(Vo=!0,Jl=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vo=!0,Jl=globalThis.perf_hooks.performance):Vo=!1),Vo}function ub(){return lb()?Jl.now():Date.now()}class cb{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const o=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(o),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(o,JSON.stringify(u))}catch{}a=u},now(){return ub()}},s&&s.on(ab,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...f)=>{this.onQueue.push({method:c,args:f})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...f)=>(this.targetQueue.push({method:c,args:f,resolve:()=>{}}),this.fallbacks[c](...f)):(...f)=>new Promise(m=>{this.targetQueue.push({method:c,args:f,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Xl(e,t){const s=e,i=ah(),o=ob(),a=nb&&s.enableEarlyProxy;if(o&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))o.emit(ib,e,t);else{const u=a?new cb(s,o):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const db={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var fo;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(fo||(fo={}));const eu=typeof window<"u",lh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function fb(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function tu(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){dh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function uh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function Hi(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const qi=typeof navigator=="object"?navigator:{userAgent:""},ch=(()=>/Macintosh/.test(qi.userAgent)&&/AppleWebKit/.test(qi.userAgent)&&!/Safari/.test(qi.userAgent))(),dh=eu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ch?hb:"msSaveOrOpenBlob"in qi?pb:mb:()=>{};function hb(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?uh(i.href)?tu(e,t,s):(i.target="_blank",Hi(i)):Hi(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){Hi(i)},0))}function pb(e,t="download",s){if(typeof e=="string")if(uh(e))tu(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){Hi(i)})}else navigator.msSaveOrOpenBlob(fb(e,s),t)}function mb(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return tu(e,t,s);const o=e.type==="application/octet-stream",a=/constructor/i.test(String(lh.HTMLElement))||"safari"in lh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||o&&a||ch)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let f=c.result;if(typeof f!="string")throw i=null,new Error("Wrong reader.result type");f=u?f:f.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=f:location.assign(f),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function kt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function su(e){return"_a"in e&&"install"in e}function fh(){if(!("clipboard"in navigator))return kt("Your browser doesn't support the Clipboard API","error"),!0}function hh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(kt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function gb(e){if(!fh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),kt("Global state copied to clipboard.")}catch(t){if(hh(t))return;kt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function vb(e){if(!fh())try{ph(e,JSON.parse(await navigator.clipboard.readText())),kt("Global state pasted from clipboard.")}catch(t){if(hh(t))return;kt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function _b(e){try{dh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){kt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function yb(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const o=i.item(0);return t(o?{text:await o.text(),file:o}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function bb(e){try{const s=await yb()();if(!s)return;const{text:i,file:o}=s;ph(e,JSON.parse(i)),kt(`Global state imported from "${o.name}".`)}catch(t){kt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function ph(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Us(e){return{_custom:{display:e}}}const mh="🍍 Pinia (root)",zi="_root";function wb(e){return su(e)?{id:zi,label:mh}:{id:e.$id,label:e.$id}}function Eb(e){if(su(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,f)=>(c[f]=u[f],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function Cb(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Us(e.type),key:Us(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function Db(e){switch(e){case fo.direct:return"mutation";case fo.patchFunction:return"$patch";case fo.patchObject:return"$patch";default:return"unknown"}}let Fo=!0;const Wi=[],ho="pinia:mutations",qt="pinia",{assign:xb}=Object,Gi=e=>"🍍 "+e;function Sb(e,t){Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Wi,app:e},s=>{typeof s.now!="function"&&kt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:ho,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{gb(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await vb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{_b(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await bb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const o=t._s.get(i);o?typeof o.$reset!="function"?kt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(o.$reset(),kt(`Store "${i}" reset.`)):kt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,o)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Gi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Ae(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((f,m)=>(f[m]=c.$state[m],f),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Gi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((f,m)=>{try{f[m]=c[m]}catch(p){f[m]=p}return f},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let o=[t];o=o.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?o.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):mh.toLowerCase().includes(i.filter.toLowerCase())):o).map(wb)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const o=i.nodeId===zi?t:t._s.get(i.nodeId);if(!o)return;o&&(i.nodeId!==zi&&(globalThis.$store=Ae(o)),i.state=Eb(o))}}),s.on.editInspectorState((i,o)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===zi?t:t._s.get(i.nodeId);if(!a)return kt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;su(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Fo=!1,i.set(a,u,i.state.value),Fo=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const o=i.type.replace(/^🍍\s*/,""),a=t._s.get(o);if(!a)return kt(`store "${o}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return kt(`Invalid path for store "${o}":
${u}
Only state can be modified.`);u[0]="$state",Fo=!1,i.set(a,u,i.state.value),Fo=!0}})})}function Ob(e,t){Wi.includes(Gi(t.$id))||Wi.push(Gi(t.$id)),Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Wi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:f,args:m})=>{const p=gh++;s.addTimelineEvent({layerId:ho,event:{time:i(),title:"🛫 "+f,subtitle:"start",data:{store:Us(t.$id),action:Us(f),args:m},groupId:p}}),u(v=>{Rr=void 0,s.addTimelineEvent({layerId:ho,event:{time:i(),title:"🛬 "+f,subtitle:"end",data:{store:Us(t.$id),action:Us(f),args:m,result:v},groupId:p}})}),c(v=>{Rr=void 0,s.addTimelineEvent({layerId:ho,event:{time:i(),logType:"error",title:"💥 "+f,subtitle:"end",data:{store:Us(t.$id),action:Us(f),args:m,error:v},groupId:p}})})},!0),t._customProperties.forEach(u=>{ko(()=>Nr(t[u]),(c,f)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Fo&&s.addTimelineEvent({layerId:ho,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:f},groupId:Rr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},f)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Fo)return;const m={time:i(),title:Db(c),data:xb({store:Us(t.$id)},Cb(u)),groupId:Rr};c===fo.patchFunction?m.subtitle="⤵️":c===fo.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:ho,event:m})},{detached:!0,flush:"sync"});const o=t._hotUpdate;t._hotUpdate=hl(u=>{o(u),s.addTimelineEvent({layerId:ho,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Us(t.$id),info:Us("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`"${t.$id}" store installed 🆕`)})}let gh=0,Rr;function vh(e,t,s){const i=t.reduce((o,a)=>(o[a]=Ae(e)[a],o),{});for(const o in i)e[o]=function(){const a=gh,u=s?new Proxy(e,{get(...f){return Rr=a,Reflect.get(...f)},set(...f){return Rr=a,Reflect.set(...f)}}):e;Rr=a;const c=i[o].apply(u,arguments);return Rr=void 0,c}}function Nb({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){vh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Ae(t)._hotUpdate=function(o){i.apply(this,arguments),vh(t,Object.keys(o._hmrPayload.actions),!!t._isOptionsAPI)}}Ob(e,t)}}function Ib(){const e=lv(!0),t=e.run(()=>cd({}));let s=[],i=[];const o=hl({install(a){o._a=a,a.provide(db,o),a.config.globalProperties.$pinia=o,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&Sb(a,o),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!rb?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&typeof Proxy<"u"&&o.use(Nb),o}const BR="",ze=(e,t)=>{const s=e.__vccOpts||e;for(const[i,o]of t)s[i]=o;return s},Ab={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Tb={id:"app"};function Mb(e,t,s,i,o,a){const u=X("router-view");return N(),I("div",Tb,[A(u)])}const Pb=ze(Ab,[["render",Mb]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function _h(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function kb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&_h(e.default)}const et=Object.assign;function ru(e,t){const s={};for(const i in t){const o=t[i];s[i]=ds(o)?o.map(e):e(o)}return s}const Mn=()=>{},ds=Array.isArray;function He(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const yh=/#/g,Rb=/&/g,Vb=/\//g,Fb=/=/g,Ub=/\?/g,bh=/\+/g,Lb=/%5B/g,Bb=/%5D/g,wh=/%5E/g,$b=/%60/g,Eh=/%7B/g,jb=/%7C/g,Ch=/%7D/g,Hb=/%20/g;function ou(e){return encodeURI(""+e).replace(jb,"|").replace(Lb,"[").replace(Bb,"]")}function qb(e){return ou(e).replace(Eh,"{").replace(Ch,"}").replace(wh,"^")}function nu(e){return ou(e).replace(bh,"%2B").replace(Hb,"+").replace(yh,"%23").replace(Rb,"%26").replace($b,"`").replace(Eh,"{").replace(Ch,"}").replace(wh,"^")}function zb(e){return nu(e).replace(Fb,"%3D")}function Wb(e){return ou(e).replace(yh,"%23").replace(Ub,"%3F")}function Gb(e){return e==null?"":Wb(e).replace(Vb,"%2F")}function Uo(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&He(`Error decoding "${e}". Using original value`)}return""+e}const Kb=/\/$/,Qb=e=>e.replace(Kb,"");function iu(e,t,s="/"){let i,o={},a="",u="";const c=t.indexOf("#");let f=t.indexOf("?");return c<f&&c>=0&&(f=-1),f>-1&&(i=t.slice(0,f),a=t.slice(f+1,c>-1?c:t.length),o=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Jb(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:o,hash:Uo(u)}}function Yb(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function Dh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function xh(e,t,s){const i=t.matched.length-1,o=s.matched.length-1;return i>-1&&i===o&&Vr(t.matched[i],s.matched[o])&&Sh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Vr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Sh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Zb(e[s],t[s]))return!1;return!0}function Zb(e,t){return ds(e)?Oh(e,t):ds(t)?Oh(t,e):e===t}function Oh(e,t){return ds(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Jb(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return He(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),o=i[i.length-1];(o===".."||o===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Fr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Pn;(function(e){e.pop="pop",e.push="push"})(Pn||(Pn={}));var kn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(kn||(kn={}));function Xb(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Qb(e)}const e0=/^[^#]+#/;function t0(e,t){return e.replace(e0,"#")+t}function s0(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Ki=()=>({left:window.scrollX,top:window.scrollY});function r0(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){He(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{He(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const o=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o){({}).NODE_ENV!=="production"&&He(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=s0(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Nh(e,t){return(history.state?history.state.position-t:-1)+e}const au=new Map;function o0(e,t){au.set(e,t)}function n0(e){const t=au.get(e);return au.delete(e),t}let i0=()=>location.protocol+"//"+location.host;function Ih(e,t){const{pathname:s,search:i,hash:o}=t,a=e.indexOf("#");if(a>-1){let c=o.includes(e.slice(a))?e.slice(a).length:1,f=o.slice(c);return f[0]!=="/"&&(f="/"+f),Dh(f,"")}return Dh(s,e)+i+o}function a0(e,t,s,i){let o=[],a=[],u=null;const c=({state:w})=>{const D=Ih(e,location),k=s.value,U=t.value;let te=0;if(w){if(s.value=D,t.value=w,u&&u===k){u=null;return}te=U?w.position-U.position:0}else i(D);o.forEach(T=>{T(s.value,k,{delta:te,type:Pn.pop,direction:te?te>0?kn.forward:kn.back:kn.unknown})})};function f(){u=s.value}function m(w){o.push(w);const D=()=>{const k=o.indexOf(w);k>-1&&o.splice(k,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(et({},w.state,{scroll:Ki()}),"")}function v(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:f,listen:m,destroy:v}}function Ah(e,t,s,i=!1,o=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:o?Ki():null}}function l0(e){const{history:t,location:s}=window,i={value:Ih(e,s)},o={value:t.state};o.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(f,m,p){const v=e.indexOf("#"),w=v>-1?(s.host&&document.querySelector("base")?e:e.slice(v))+f:i0()+e+f;try{t[p?"replaceState":"pushState"](m,"",w),o.value=m}catch(D){({}).NODE_ENV!=="production"?He("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(f,m){const p=et({},t.state,Ah(o.value.back,f,o.value.forward,!0),m,{position:o.value.position});a(f,p,!0),i.value=f}function c(f,m){const p=et({},o.value,t.state,{forward:f,scroll:Ki()});({}).NODE_ENV!=="production"&&!t.state&&He(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const v=et({},Ah(i.value,f,null),{position:p.position+1},m);a(f,v,!1),i.value=f}return{location:i,state:o,push:c,replace:u}}function u0(e){e=Xb(e);const t=l0(e),s=a0(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const o=et({location:"",base:e,go:i,createHref:t0.bind(null,e)},t,s);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Qi(e){return typeof e=="string"||e&&typeof e=="object"}function Th(e){return typeof e=="string"||typeof e=="symbol"}const lu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Mh;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Mh||(Mh={}));const c0={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${f0(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Lo(e,t){return{}.NODE_ENV!=="production"?et(new Error(c0[e](t)),{type:e,[lu]:!0},t):et(new Error,{type:e,[lu]:!0},t)}function pr(e,t){return e instanceof Error&&lu in e&&(t==null||!!(e.type&t))}const d0=["params","query","hash"];function f0(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of d0)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ph="[^/]+?",h0={sensitive:!1,strict:!1,start:!0,end:!0},p0=/[.+*?^${}()[\]/\\]/g;function m0(e,t){const s=et({},h0,t),i=[];let o=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(o+="/");for(let v=0;v<m.length;v++){const w=m[v];let D=40+(s.sensitive?.25:0);if(w.type===0)v||(o+="/"),o+=w.value.replace(p0,"\\$&"),D+=40;else if(w.type===1){const{value:k,repeatable:U,optional:te,regexp:T}=w;a.push({name:k,repeatable:U,optional:te});const oe=T||Ph;if(oe!==Ph){D+=10;try{new RegExp(`(${oe})`)}catch(we){throw new Error(`Invalid custom RegExp for param "${k}" (${oe}): `+we.message)}}let Q=U?`((?:${oe})(?:/(?:${oe}))*)`:`(${oe})`;v||(Q=te&&m.length<2?`(?:/${Q})`:"/"+Q),te&&(Q+="?"),o+=Q,D+=20,te&&(D+=-8),U&&(D+=-20),oe===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(o+="/?"),s.end?o+="$":s.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const u=new RegExp(o,s.sensitive?"":"i");function c(m){const p=m.match(u),v={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",k=a[w-1];v[k.name]=D&&k.repeatable?D.split("/"):D}return v}function f(m){let p="",v=!1;for(const w of e){(!v||!p.endsWith("/"))&&(p+="/"),v=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:k,repeatable:U,optional:te}=D,T=k in m?m[k]:"";if(ds(T)&&!U)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const oe=ds(T)?T.join("/"):T;if(!oe)if(te)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):v=!0);else throw new Error(`Missing required param "${k}"`);p+=oe}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:f}}function g0(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function kh(e,t){let s=0;const i=e.score,o=t.score;for(;s<i.length&&s<o.length;){const a=g0(i[s],o[s]);if(a)return a;s++}if(Math.abs(o.length-i.length)===1){if(Rh(i))return 1;if(Rh(o))return-1}return o.length-i.length}function Rh(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const v0={type:0,value:""},_0=/[a-zA-Z0-9_]/;function y0(e){if(!e)return[[]];if(e==="/")return[[v0]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const o=[];let a;function u(){a&&o.push(a),a=[]}let c=0,f,m="",p="";function v(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=f}for(;c<e.length;){if(f=e[c++],f==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:f==="/"?(m&&v(),u()):f===":"?(v(),s=1):w();break;case 4:w(),s=i;break;case 1:f==="("?s=2:_0.test(f)?w():(v(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&c--);break;case 2:f===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+f:s=3:p+=f;break;case 3:v(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),v(),u(),o}function b0(e,t,s){const i=m0(y0(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&He(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const o=et(i,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function w0(e,t){const s=[],i=new Map;t=Lh({strict:!1,end:!0,sensitive:!1},t);function o(v){return i.get(v)}function a(v,w,D){const k=!D,U=Fh(v);({}).NODE_ENV!=="production"&&x0(U,w),U.aliasOf=D&&D.record;const te=Lh(t,v),T=[U];if("alias"in v){const we=typeof v.alias=="string"?[v.alias]:v.alias;for(const Y of we)T.push(Fh(et({},U,{components:D?D.record.components:U.components,path:Y,aliasOf:D?D.record:U})))}let oe,Q;for(const we of T){const{path:Y}=we;if(w&&Y[0]!=="/"){const he=w.record.path,be=he[he.length-1]==="/"?"":"/";we.path=w.record.path+(Y&&be+Y)}if({}.NODE_ENV!=="production"&&we.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(oe=b0(we,w,te),{}.NODE_ENV!=="production"&&w&&Y[0]==="/"&&O0(oe,w),D?(D.alias.push(oe),{}.NODE_ENV!=="production"&&D0(D,oe)):(Q=Q||oe,Q!==oe&&Q.alias.push(oe),k&&v.name&&!Uh(oe)&&({}.NODE_ENV!=="production"&&S0(v,w),u(v.name))),Bh(oe)&&f(oe),U.children){const he=U.children;for(let be=0;be<he.length;be++)a(he[be],oe,D&&D.children[be])}D=D||oe}return Q?()=>{u(Q)}:Mn}function u(v){if(Th(v)){const w=i.get(v);w&&(i.delete(v),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(v);w>-1&&(s.splice(w,1),v.record.name&&i.delete(v.record.name),v.children.forEach(u),v.alias.forEach(u))}}function c(){return s}function f(v){const w=N0(v,s);s.splice(w,0,v),v.record.name&&!Uh(v)&&i.set(v.record.name,v)}function m(v,w){let D,k={},U,te;if("name"in v&&v.name){if(D=i.get(v.name),!D)throw Lo(1,{location:v});if({}.NODE_ENV!=="production"){const Q=Object.keys(v.params||{}).filter(we=>!D.keys.find(Y=>Y.name===we));Q.length&&He(`Discarded invalid param(s) "${Q.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}te=D.record.name,k=et(Vh(w.params,D.keys.filter(Q=>!Q.optional).concat(D.parent?D.parent.keys.filter(Q=>Q.optional):[]).map(Q=>Q.name)),v.params&&Vh(v.params,D.keys.map(Q=>Q.name))),U=D.stringify(k)}else if(v.path!=null)U=v.path,{}.NODE_ENV!=="production"&&!U.startsWith("/")&&He(`The Matcher cannot resolve relative paths but received "${U}". Unless you directly called \`matcher.resolve("${U}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(Q=>Q.re.test(U)),D&&(k=D.parse(U),te=D.record.name);else{if(D=w.name?i.get(w.name):s.find(Q=>Q.re.test(w.path)),!D)throw Lo(1,{location:v,currentLocation:w});te=D.record.name,k=et({},w.params,v.params),U=D.stringify(k)}const T=[];let oe=D;for(;oe;)T.unshift(oe.record),oe=oe.parent;return{name:te,path:U,params:k,matched:T,meta:C0(T)}}e.forEach(v=>a(v));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:o}}function Vh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Fh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:E0(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function E0(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Uh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function C0(e){return e.reduce((t,s)=>et(t,s.meta),{})}function Lh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function uu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function D0(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(uu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(uu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function x0(e,t){t&&t.record.name&&!e.name&&!e.path&&He(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function S0(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function O0(e,t){for(const s of t.keys)if(!e.keys.find(uu.bind(null,s)))return He(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function N0(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;kh(e,t[a])<0?i=a:s=a+1}const o=I0(e);return o&&(i=t.lastIndexOf(o,i-1),{}.NODE_ENV!=="production"&&i<0&&He(`Finding ancestor route "${o.record.path}" failed for "${e.record.path}"`)),i}function I0(e){let t=e;for(;t=t.parent;)if(Bh(t)&&kh(e,t)===0)return t}function Bh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function A0(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<i.length;++o){const a=i[o].replace(bh," "),u=a.indexOf("="),c=Uo(u<0?a:a.slice(0,u)),f=u<0?null:Uo(a.slice(u+1));if(c in t){let m=t[c];ds(m)||(m=t[c]=[m]),m.push(f)}else t[c]=f}return t}function $h(e){let t="";for(let s in e){const i=e[s];if(s=zb(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(ds(i)?i.map(a=>a&&nu(a)):[i&&nu(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function T0(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=ds(i)?i.map(o=>o==null?null:""+o):i==null?i:""+i)}return t}const M0=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),jh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Yi=Symbol({}.NODE_ENV!=="production"?"router":""),cu=Symbol({}.NODE_ENV!=="production"?"route location":""),du=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Rn(){let e=[];function t(i){return e.push(i),()=>{const o=e.indexOf(i);o>-1&&e.splice(o,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ur(e,t,s,i,o,a=u=>u()){const u=i&&(i.enterCallbacks[o]=i.enterCallbacks[o]||[]);return()=>new Promise((c,f)=>{const m=w=>{w===!1?f(Lo(4,{from:s,to:t})):w instanceof Error?f(w):Qi(w)?f(Lo(2,{from:t,to:w})):(u&&i.enterCallbacks[o]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[o],t,s,{}.NODE_ENV!=="production"?P0(m,t,s):m));let v=Promise.resolve(p);if(e.length<3&&(v=v.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)v=v.then(D=>m._called?D:(He(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){He(w),f(new Error("Invalid navigation guard"));return}}v.catch(w=>f(w))})}function P0(e,t,s){let i=0;return function(){i++===1&&He(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function fu(e,t,s,i,o=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&He(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let f=u.components[c];if({}.NODE_ENV!=="production"){if(!f||typeof f!="object"&&typeof f!="function")throw He(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(f)}".`),new Error("Invalid route component");if("then"in f){He(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=f;f=()=>m}else f.__asyncLoader&&!f.__warnedDefineAsync&&(f.__warnedDefineAsync=!0,He(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(_h(f)){const p=(f.__vccOpts||f)[t];p&&a.push(Ur(p,s,i,u,c,o))}else{let m=f();({}).NODE_ENV!=="production"&&!("catch"in m)&&(He(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const v=kb(p)?p.default:p;u.mods[c]=p,u.components[c]=v;const D=(v.__vccOpts||v)[t];return D&&Ur(D,s,i,u,c,o)()}))}}}return a}function Hh(e){const t=ks(Yi),s=ks(cu);let i=!1,o=null;const a=Vs(()=>{const p=Nr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==o)&&(Qi(p)||(i?He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,o,`
- props:`,e):He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),o=p,i=!0),t.resolve(p)}),u=Vs(()=>{const{matched:p}=a.value,{length:v}=p,w=p[v-1],D=s.matched;if(!w||!D.length)return-1;const k=D.findIndex(Vr.bind(null,w));if(k>-1)return k;const U=qh(p[v-2]);return v>1&&qh(w)===U&&D[D.length-1].path!==U?D.findIndex(Vr.bind(null,p[v-2])):k}),c=Vs(()=>u.value>-1&&F0(s.params,a.value.params)),f=Vs(()=>u.value>-1&&u.value===s.matched.length-1&&Sh(s.params,a.value.params));function m(p={}){if(V0(p)){const v=t[Nr(e.replace)?"replace":"push"](Nr(e.to)).catch(Mn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>v),v}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const v={route:a.value,isActive:c.value,isExactActive:f.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(v),Z_(()=>{v.route=a.value,v.isActive=c.value,v.isExactActive=f.value,v.error=Qi(Nr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Vs(()=>a.value.href),isActive:c,isExactActive:f,navigate:m}}function k0(e){return e.length===1?e[0]:e}const R0=Bd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Hh,setup(e,{slots:t}){const s=hi(Hh(e)),{options:i}=ks(Yi),o=Vs(()=>({[zh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[zh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&k0(t.default(s));return e.custom?a:zl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},a)}}});function V0(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function F0(e,t){for(const s in t){const i=t[s],o=e[s];if(typeof i=="string"){if(i!==o)return!1}else if(!ds(o)||o.length!==i.length||i.some((a,u)=>a!==o[u]))return!1}return!0}function qh(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const zh=(e,t,s)=>e??t??s,U0=Bd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&B0();const i=ks(du),o=Vs(()=>e.route||i.value),a=ks(jh,0),u=Vs(()=>{let m=Nr(a);const{matched:p}=o.value;let v;for(;(v=p[m])&&!v.components;)m++;return m}),c=Vs(()=>o.value.matched[u.value]);Ti(jh,Vs(()=>u.value+1)),Ti(M0,c),Ti(du,o);const f=cd();return ko(()=>[f.value,c.value,e.name],([m,p,v],[w,D,k])=>{p&&(p.instances[v]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Vr(p,D)||!w)&&(p.enterCallbacks[v]||[]).forEach(U=>U(m))},{flush:"post"}),()=>{const m=o.value,p=e.name,v=c.value,w=v&&v.components[p];if(!w)return Wh(s.default,{Component:w,route:m});const D=v.props[p],k=D?D===!0?m.params:typeof D=="function"?D(m):D:null,te=zl(w,et({},k,t,{onVnodeUnmounted:T=>{T.component.isUnmounted&&(v.instances[p]=null)},ref:f}));if({}.NODE_ENV!=="production"&&hr&&te.ref){const T={depth:u.value,name:v.name,path:v.path,meta:v.meta};(ds(te.ref)?te.ref.map(Q=>Q.i):[te.ref.i]).forEach(Q=>{Q.__vrv_devtools=T})}return Wh(s.default,{Component:te,route:m})||te}}});function Wh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const L0=U0;function B0(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";He(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vn(e,t){const s=et({},e,{matched:e.matched.map(i=>Z0(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Zi(e){return{_custom:{display:e}}}let $0=0;function j0(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=$0++;Xl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},o=>{typeof o.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),o.on.inspectComponent((p,v)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vn(t.currentRoute.value,"Current Route")})}),o.on.visitComponentTree(({treeNode:p,componentInstance:v})=>{if(v.__vrv_devtools){const w=v.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Gh})}ds(v.__vrl_devtools)&&(v.__devtoolsApi=o,v.__vrl_devtools.forEach(w=>{let D=w.route.path,k=Yh,U="",te=0;w.error?(D=w.error,k=G0,te=K0):w.isExactActive?(k=Qh,U="This is exactly active"):w.isActive&&(k=Kh,U="This link is active"),p.tags.push({label:D,textColor:te,tooltip:U,backgroundColor:k})}))}),ko(t.currentRoute,()=>{f(),o.notifyComponentUpdate(),o.sendInspectorTree(c),o.sendInspectorState(c)});const a="router:navigations:"+i;o.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,v)=>{o.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:v.fullPath,logType:"error",time:o.now(),data:{error:p},groupId:v.meta.__navigationId}})});let u=0;t.beforeEach((p,v)=>{const w={guard:Zi("beforeEach"),from:Vn(v,"Current Location during this navigation"),to:Vn(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),o.addTimelineEvent({layerId:a,event:{time:o.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,v,w)=>{const D={guard:Zi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Zi("❌")):D.status=Zi("✅"),D.from=Vn(v,"Current Location during this navigation"),D.to=Vn(p,"Target location"),o.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:o.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;o.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function f(){if(!m)return;const p=m;let v=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);v.forEach(Xh),p.filter&&(v=v.filter(w=>hu(w,p.filter.toLowerCase()))),v.forEach(w=>Jh(w,t.currentRoute.value)),p.rootNodes=v.map(Zh)}let m;o.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&f()}),o.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:q0(w)})}}),o.sendInspectorTree(c),o.sendInspectorState(c)})}function H0(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function q0(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${H0(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const Gh=15485081,Kh=2450411,Qh=8702998,z0=2282478,Yh=16486972,W0=6710886,G0=16704226,K0=12131356;function Zh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:z0}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Yh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Gh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Qh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Kh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:W0});let i=s.__vd_id;return i==null&&(i=String(Q0++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Zh)}}let Q0=0;const Y0=/^\/(.*)\/([a-z]*)$/;function Jh(e,t){const s=t.matched.length&&Vr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Vr(i,e.record))),e.children.forEach(i=>Jh(i,t))}function Xh(e){e.__vd_match=!1,e.children.forEach(Xh)}function hu(e,t){const s=String(e.re).match(Y0);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>hu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const o=e.record.path.toLowerCase(),a=Uo(o);return!t.startsWith("/")&&(a.includes(t)||o.includes(t))||a.startsWith(t)||o.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>hu(u,t))}function Z0(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function J0(e){const t=w0(e.routes,e),s=e.parseQuery||A0,i=e.stringifyQuery||$h,o=e.history;if({}.NODE_ENV!=="production"&&!o)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Rn(),u=Rn(),c=Rn(),f=Nv(Fr);let m=Fr;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=ru.bind(null,R=>""+R),v=ru.bind(null,Gb),w=ru.bind(null,Uo);function D(R,ne){let re,me;return Th(R)?(re=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!re&&He(`Parent route "${String(R)}" not found when adding child route`,ne),me=ne):me=R,t.addRoute(me,re)}function k(R){const ne=t.getRecordMatcher(R);ne?t.removeRoute(ne):{}.NODE_ENV!=="production"&&He(`Cannot remove non-existent route "${String(R)}"`)}function U(){return t.getRoutes().map(R=>R.record)}function te(R){return!!t.getRecordMatcher(R)}function T(R,ne){if(ne=et({},ne||f.value),typeof R=="string"){const b=iu(s,R,ne.path),C=t.resolve({path:b.path},ne),P=o.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?He(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):C.matched.length||He(`No match found for location with path "${R}"`)),et(b,C,{params:w(C.params),hash:Uo(b.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Qi(R))return He(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),T({});let re;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&He(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),re=et({},R,{path:iu(s,R.path,ne.path).path});else{const b=et({},R.params);for(const C in b)b[C]==null&&delete b[C];re=et({},R,{params:v(b)}),ne.params=v(ne.params)}const me=t.resolve(re,ne),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&He(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),me.params=p(w(me.params));const at=Yb(i,et({},R,{hash:qb(ke),path:me.path})),Re=o.createHref(at);return{}.NODE_ENV!=="production"&&(Re.startsWith("//")?He(`Location "${R}" resolved to "${Re}". A resolved location cannot start with multiple slashes.`):me.matched.length||He(`No match found for location with path "${R.path!=null?R.path:R}"`)),et({fullPath:at,hash:ke,query:i===$h?T0(R.query):R.query||{}},me,{redirectedFrom:void 0,href:Re})}function oe(R){return typeof R=="string"?iu(s,R,f.value.path):et({},R)}function Q(R,ne){if(m!==R)return Lo(8,{from:ne,to:R})}function we(R){return be(R)}function Y(R){return we(et(oe(R),{replace:!0}))}function he(R){const ne=R.matched[R.matched.length-1];if(ne&&ne.redirect){const{redirect:re}=ne;let me=typeof re=="function"?re(R):re;if(typeof me=="string"&&(me=me.includes("?")||me.includes("#")?me=oe(me):{path:me},me.params={}),{}.NODE_ENV!=="production"&&me.path==null&&!("name"in me))throw He(`Invalid redirect found:
${JSON.stringify(me,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return et({query:R.query,hash:R.hash,params:me.path!=null?{}:R.params},me)}}function be(R,ne){const re=m=T(R),me=f.value,ke=R.state,at=R.force,Re=R.replace===!0,b=he(re);if(b)return be(et(oe(b),{state:typeof b=="object"?et({},ke,b.state):ke,force:at,replace:Re}),ne||re);const C=re;C.redirectedFrom=ne;let P;return!at&&xh(i,me,re)&&(P=Lo(16,{to:C,from:me}),_t(me,me,!0,!1)),(P?Promise.resolve(P):ie(C,me)).catch(F=>pr(F)?pr(F,2)?F:Xt(F):Ee(F,C,me)).then(F=>{if(F){if(pr(F,2))return{}.NODE_ENV!=="production"&&xh(i,T(F.to),C)&&ne&&(ne._count=ne._count?ne._count+1:1)>30?(He(`Detected a possibly infinite redirection in a navigation guard when going from "${me.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):be(et({replace:Re},oe(F.to),{state:typeof F.to=="object"?et({},ke,F.to.state):ke,force:at}),ne||C)}else F=ae(C,me,!0,Re,ke);return Pe(C,me,F),F})}function Te(R,ne){const re=Q(R,ne);return re?Promise.reject(re):Promise.resolve()}function le(R){const ne=fs.values().next().value;return ne&&typeof ne.runWithContext=="function"?ne.runWithContext(R):R()}function ie(R,ne){let re;const[me,ke,at]=X0(R,ne);re=fu(me.reverse(),"beforeRouteLeave",R,ne);for(const b of me)b.leaveGuards.forEach(C=>{re.push(Ur(C,R,ne))});const Re=Te.bind(null,R,ne);return re.push(Re),hs(re).then(()=>{re=[];for(const b of a.list())re.push(Ur(b,R,ne));return re.push(Re),hs(re)}).then(()=>{re=fu(ke,"beforeRouteUpdate",R,ne);for(const b of ke)b.updateGuards.forEach(C=>{re.push(Ur(C,R,ne))});return re.push(Re),hs(re)}).then(()=>{re=[];for(const b of at)if(b.beforeEnter)if(ds(b.beforeEnter))for(const C of b.beforeEnter)re.push(Ur(C,R,ne));else re.push(Ur(b.beforeEnter,R,ne));return re.push(Re),hs(re)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),re=fu(at,"beforeRouteEnter",R,ne,le),re.push(Re),hs(re))).then(()=>{re=[];for(const b of u.list())re.push(Ur(b,R,ne));return re.push(Re),hs(re)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function Pe(R,ne,re){c.list().forEach(me=>le(()=>me(R,ne,re)))}function ae(R,ne,re,me,ke){const at=Q(R,ne);if(at)return at;const Re=ne===Fr,b=hr?history.state:{};re&&(me||Re?o.replace(R.fullPath,et({scroll:Re&&b&&b.scroll},ke)):o.push(R.fullPath,ke)),f.value=R,_t(R,ne,re,Re),Xt()}let We;function J(){We||(We=o.listen((R,ne,re)=>{if(!zt.listening)return;const me=T(R),ke=he(me);if(ke){be(et(ke,{replace:!0,force:!0}),me).catch(Mn);return}m=me;const at=f.value;hr&&o0(Nh(at.fullPath,re.delta),Ki()),ie(me,at).catch(Re=>pr(Re,12)?Re:pr(Re,2)?(be(et(oe(Re.to),{force:!0}),me).then(b=>{pr(b,20)&&!re.delta&&re.type===Pn.pop&&o.go(-1,!1)}).catch(Mn),Promise.reject()):(re.delta&&o.go(-re.delta,!1),Ee(Re,me,at))).then(Re=>{Re=Re||ae(me,at,!1),Re&&(re.delta&&!pr(Re,8)?o.go(-re.delta,!1):re.type===Pn.pop&&pr(Re,20)&&o.go(-1,!1)),Pe(me,at,Re)}).catch(Mn)}))}let $e=Rn(),ut=Rn(),Oe;function Ee(R,ne,re){Xt(R);const me=ut.list();return me.length?me.forEach(ke=>ke(R,ne,re)):({}.NODE_ENV!=="production"&&He("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Ut(){return Oe&&f.value!==Fr?Promise.resolve():new Promise((R,ne)=>{$e.add([R,ne])})}function Xt(R){return Oe||(Oe=!R,J(),$e.list().forEach(([ne,re])=>R?re(R):ne()),$e.reset()),R}function _t(R,ne,re,me){const{scrollBehavior:ke}=e;if(!hr||!ke)return Promise.resolve();const at=!re&&n0(Nh(R.fullPath,0))||(me||!re)&&history.state&&history.state.scroll||null;return vl().then(()=>ke(R,ne,at)).then(Re=>Re&&r0(Re)).catch(Re=>Ee(Re,R,ne))}const de=R=>o.go(R);let Ke;const fs=new Set,zt={currentRoute:f,listening:!0,addRoute:D,removeRoute:k,clearRoutes:t.clearRoutes,hasRoute:te,getRoutes:U,resolve:T,options:e,push:we,replace:Y,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ut.add,isReady:Ut,install(R){const ne=this;R.component("RouterLink",R0),R.component("RouterView",L0),R.config.globalProperties.$router=ne,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Nr(f)}),hr&&!Ke&&f.value===Fr&&(Ke=!0,we(o.location).catch(ke=>{({}).NODE_ENV!=="production"&&He("Unexpected error when starting the router:",ke)}));const re={};for(const ke in Fr)Object.defineProperty(re,ke,{get:()=>f.value[ke],enumerable:!0});R.provide(Yi,ne),R.provide(cu,ld(re)),R.provide(du,f);const me=R.unmount;fs.add(R),R.unmount=function(){fs.delete(R),fs.size<1&&(m=Fr,We&&We(),We=null,f.value=Fr,Ke=!1,Oe=!1),me()},{}.NODE_ENV!=="production"&&hr&&j0(R,ne,t)}};function hs(R){return R.reduce((ne,re)=>ne.then(()=>le(re)),Promise.resolve())}return zt}function X0(e,t){const s=[],i=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Vr(m,c))?i.push(c):s.push(c));const f=e.matched[u];f&&(t.matched.find(m=>Vr(m,f))||o.push(f))}return[s,i,o]}function Ji(){return ks(Yi)}function ep(e){return ks(cu)}const tp="data:image/svg+xml;base64,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";var Fn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Xi={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Xi.exports,function(e,t){(function(){var s,i="4.17.21",o=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",f="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",v=1,w=2,D=4,k=1,U=2,te=1,T=2,oe=4,Q=8,we=16,Y=32,he=64,be=128,Te=256,le=512,ie=30,Pe="...",ae=800,We=16,J=1,$e=2,ut=3,Oe=1/0,Ee=9007199254740991,Ut=17976931348623157e292,Xt=0/0,_t=**********,de=_t-1,Ke=_t>>>1,fs=[["ary",be],["bind",te],["bindKey",T],["curry",Q],["curryRight",we],["flip",le],["partial",Y],["partialRight",he],["rearg",Te]],zt="[object Arguments]",hs="[object Array]",R="[object AsyncFunction]",ne="[object Boolean]",re="[object Date]",me="[object DOMException]",ke="[object Error]",at="[object Function]",Re="[object GeneratorFunction]",b="[object Map]",C="[object Number]",P="[object Null]",F="[object Object]",j="[object Promise]",q="[object Proxy]",ee="[object RegExp]",W="[object Set]",Z="[object String]",z="[object Symbol]",Ce="[object Undefined]",se="[object WeakMap]",_e="[object WeakSet]",De="[object ArrayBuffer]",Ue="[object DataView]",tt="[object Float32Array]",Je="[object Float64Array]",Lt="[object Int8Array]",St="[object Int16Array]",es="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",$o="[object Uint16Array]",At="[object Uint32Array]",ws=/\b__p \+= '';/g,aa=/\b(__p \+=) '' \+/g,aI=/(__e\(.*?\)|\b__t\)) \+\n'';/g,mp=/&(?:amp|lt|gt|quot|#39);/g,gp=/[&<>"']/g,lI=RegExp(mp.source),uI=RegExp(gp.source),cI=/<%-([\s\S]+?)%>/g,dI=/<%([\s\S]+?)%>/g,vp=/<%=([\s\S]+?)%>/g,fI=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,hI=/^\w*$/,pI=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Cu=/[\\^$.*+?()[\]{}|]/g,mI=RegExp(Cu.source),Du=/^\s+/,gI=/\s/,vI=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,_I=/\{\n\/\* \[wrapped with (.+)\] \*/,yI=/,? & /,bI=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,wI=/[()=,{}\[\]\/\s]/,EI=/\\(\\)?/g,CI=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,_p=/\w*$/,DI=/^[-+]0x[0-9a-f]+$/i,xI=/^0b[01]+$/i,SI=/^\[object .+?Constructor\]$/,OI=/^0o[0-7]+$/i,NI=/^(?:0|[1-9]\d*)$/,II=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,la=/($^)/,AI=/['\n\r\u2028\u2029\\]/g,ua="\\ud800-\\udfff",TI="\\u0300-\\u036f",MI="\\ufe20-\\ufe2f",PI="\\u20d0-\\u20ff",yp=TI+MI+PI,bp="\\u2700-\\u27bf",wp="a-z\\xdf-\\xf6\\xf8-\\xff",kI="\\xac\\xb1\\xd7\\xf7",RI="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",VI="\\u2000-\\u206f",FI=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ep="A-Z\\xc0-\\xd6\\xd8-\\xde",Cp="\\ufe0e\\ufe0f",Dp=kI+RI+VI+FI,xu="['’]",UI="["+ua+"]",xp="["+Dp+"]",ca="["+yp+"]",Sp="\\d+",LI="["+bp+"]",Op="["+wp+"]",Np="[^"+ua+Dp+Sp+bp+wp+Ep+"]",Su="\\ud83c[\\udffb-\\udfff]",BI="(?:"+ca+"|"+Su+")",Ip="[^"+ua+"]",Ou="(?:\\ud83c[\\udde6-\\uddff]){2}",Nu="[\\ud800-\\udbff][\\udc00-\\udfff]",jo="["+Ep+"]",Ap="\\u200d",Tp="(?:"+Op+"|"+Np+")",$I="(?:"+jo+"|"+Np+")",Mp="(?:"+xu+"(?:d|ll|m|re|s|t|ve))?",Pp="(?:"+xu+"(?:D|LL|M|RE|S|T|VE))?",kp=BI+"?",Rp="["+Cp+"]?",jI="(?:"+Ap+"(?:"+[Ip,Ou,Nu].join("|")+")"+Rp+kp+")*",HI="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",qI="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Vp=Rp+kp+jI,zI="(?:"+[LI,Ou,Nu].join("|")+")"+Vp,WI="(?:"+[Ip+ca+"?",ca,Ou,Nu,UI].join("|")+")",GI=RegExp(xu,"g"),KI=RegExp(ca,"g"),Iu=RegExp(Su+"(?="+Su+")|"+WI+Vp,"g"),QI=RegExp([jo+"?"+Op+"+"+Mp+"(?="+[xp,jo,"$"].join("|")+")",$I+"+"+Pp+"(?="+[xp,jo+Tp,"$"].join("|")+")",jo+"?"+Tp+"+"+Mp,jo+"+"+Pp,qI,HI,Sp,zI].join("|"),"g"),YI=RegExp("["+Ap+ua+yp+Cp+"]"),ZI=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,JI=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],XI=-1,ht={};ht[tt]=ht[Je]=ht[Lt]=ht[St]=ht[es]=ht[Bt]=ht[gr]=ht[$o]=ht[At]=!0,ht[zt]=ht[hs]=ht[De]=ht[ne]=ht[Ue]=ht[re]=ht[ke]=ht[at]=ht[b]=ht[C]=ht[F]=ht[ee]=ht[W]=ht[Z]=ht[se]=!1;var dt={};dt[zt]=dt[hs]=dt[De]=dt[Ue]=dt[ne]=dt[re]=dt[tt]=dt[Je]=dt[Lt]=dt[St]=dt[es]=dt[b]=dt[C]=dt[F]=dt[ee]=dt[W]=dt[Z]=dt[z]=dt[Bt]=dt[gr]=dt[$o]=dt[At]=!0,dt[ke]=dt[at]=dt[se]=!1;var eA={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},tA={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},sA={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},rA={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},oA=parseFloat,nA=parseInt,Fp=typeof Fn=="object"&&Fn&&Fn.Object===Object&&Fn,iA=typeof self=="object"&&self&&self.Object===Object&&self,$t=Fp||iA||Function("return this")(),Au=t&&!t.nodeType&&t,go=Au&&!0&&e&&!e.nodeType&&e,Up=go&&go.exports===Au,Tu=Up&&Fp.process,Es=function(){try{var S=go&&go.require&&go.require("util").types;return S||Tu&&Tu.binding&&Tu.binding("util")}catch{}}(),Lp=Es&&Es.isArrayBuffer,Bp=Es&&Es.isDate,$p=Es&&Es.isMap,jp=Es&&Es.isRegExp,Hp=Es&&Es.isSet,qp=Es&&Es.isTypedArray;function ps(S,V,M){switch(M.length){case 0:return S.call(V);case 1:return S.call(V,M[0]);case 2:return S.call(V,M[0],M[1]);case 3:return S.call(V,M[0],M[1],M[2])}return S.apply(V,M)}function aA(S,V,M,fe){for(var Me=-1,st=S==null?0:S.length;++Me<st;){var Tt=S[Me];V(fe,Tt,M(Tt),S)}return fe}function Cs(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe&&V(S[M],M,S)!==!1;);return S}function lA(S,V){for(var M=S==null?0:S.length;M--&&V(S[M],M,S)!==!1;);return S}function zp(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe;)if(!V(S[M],M,S))return!1;return!0}function Br(S,V){for(var M=-1,fe=S==null?0:S.length,Me=0,st=[];++M<fe;){var Tt=S[M];V(Tt,M,S)&&(st[Me++]=Tt)}return st}function da(S,V){var M=S==null?0:S.length;return!!M&&Ho(S,V,0)>-1}function Mu(S,V,M){for(var fe=-1,Me=S==null?0:S.length;++fe<Me;)if(M(V,S[fe]))return!0;return!1}function mt(S,V){for(var M=-1,fe=S==null?0:S.length,Me=Array(fe);++M<fe;)Me[M]=V(S[M],M,S);return Me}function $r(S,V){for(var M=-1,fe=V.length,Me=S.length;++M<fe;)S[Me+M]=V[M];return S}function Pu(S,V,M,fe){var Me=-1,st=S==null?0:S.length;for(fe&&st&&(M=S[++Me]);++Me<st;)M=V(M,S[Me],Me,S);return M}function uA(S,V,M,fe){var Me=S==null?0:S.length;for(fe&&Me&&(M=S[--Me]);Me--;)M=V(M,S[Me],Me,S);return M}function ku(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe;)if(V(S[M],M,S))return!0;return!1}var cA=Ru("length");function dA(S){return S.split("")}function fA(S){return S.match(bI)||[]}function Wp(S,V,M){var fe;return M(S,function(Me,st,Tt){if(V(Me,st,Tt))return fe=st,!1}),fe}function fa(S,V,M,fe){for(var Me=S.length,st=M+(fe?1:-1);fe?st--:++st<Me;)if(V(S[st],st,S))return st;return-1}function Ho(S,V,M){return V===V?DA(S,V,M):fa(S,Gp,M)}function hA(S,V,M,fe){for(var Me=M-1,st=S.length;++Me<st;)if(fe(S[Me],V))return Me;return-1}function Gp(S){return S!==S}function Kp(S,V){var M=S==null?0:S.length;return M?Fu(S,V)/M:Xt}function Ru(S){return function(V){return V==null?s:V[S]}}function Vu(S){return function(V){return S==null?s:S[V]}}function Qp(S,V,M,fe,Me){return Me(S,function(st,Tt,ct){M=fe?(fe=!1,st):V(M,st,Tt,ct)}),M}function pA(S,V){var M=S.length;for(S.sort(V);M--;)S[M]=S[M].value;return S}function Fu(S,V){for(var M,fe=-1,Me=S.length;++fe<Me;){var st=V(S[fe]);st!==s&&(M=M===s?st:M+st)}return M}function Uu(S,V){for(var M=-1,fe=Array(S);++M<S;)fe[M]=V(M);return fe}function mA(S,V){return mt(V,function(M){return[M,S[M]]})}function Yp(S){return S&&S.slice(0,em(S)+1).replace(Du,"")}function ms(S){return function(V){return S(V)}}function Lu(S,V){return mt(V,function(M){return S[M]})}function qn(S,V){return S.has(V)}function Zp(S,V){for(var M=-1,fe=S.length;++M<fe&&Ho(V,S[M],0)>-1;);return M}function Jp(S,V){for(var M=S.length;M--&&Ho(V,S[M],0)>-1;);return M}function gA(S,V){for(var M=S.length,fe=0;M--;)S[M]===V&&++fe;return fe}var vA=Vu(eA),_A=Vu(tA);function yA(S){return"\\"+rA[S]}function bA(S,V){return S==null?s:S[V]}function qo(S){return YI.test(S)}function wA(S){return ZI.test(S)}function EA(S){for(var V,M=[];!(V=S.next()).done;)M.push(V.value);return M}function Bu(S){var V=-1,M=Array(S.size);return S.forEach(function(fe,Me){M[++V]=[Me,fe]}),M}function Xp(S,V){return function(M){return S(V(M))}}function jr(S,V){for(var M=-1,fe=S.length,Me=0,st=[];++M<fe;){var Tt=S[M];(Tt===V||Tt===p)&&(S[M]=p,st[Me++]=M)}return st}function ha(S){var V=-1,M=Array(S.size);return S.forEach(function(fe){M[++V]=fe}),M}function CA(S){var V=-1,M=Array(S.size);return S.forEach(function(fe){M[++V]=[fe,fe]}),M}function DA(S,V,M){for(var fe=M-1,Me=S.length;++fe<Me;)if(S[fe]===V)return fe;return-1}function xA(S,V,M){for(var fe=M+1;fe--;)if(S[fe]===V)return fe;return fe}function zo(S){return qo(S)?OA(S):cA(S)}function Ls(S){return qo(S)?NA(S):dA(S)}function em(S){for(var V=S.length;V--&&gI.test(S.charAt(V)););return V}var SA=Vu(sA);function OA(S){for(var V=Iu.lastIndex=0;Iu.test(S);)++V;return V}function NA(S){return S.match(Iu)||[]}function IA(S){return S.match(QI)||[]}var AA=function S(V){V=V==null?$t:Wo.defaults($t.Object(),V,Wo.pick($t,JI));var M=V.Array,fe=V.Date,Me=V.Error,st=V.Function,Tt=V.Math,ct=V.Object,$u=V.RegExp,TA=V.String,Ds=V.TypeError,pa=M.prototype,MA=st.prototype,Go=ct.prototype,ma=V["__core-js_shared__"],ga=MA.toString,lt=Go.hasOwnProperty,PA=0,tm=function(){var r=/[^.]+$/.exec(ma&&ma.keys&&ma.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),va=Go.toString,kA=ga.call(ct),RA=$t._,VA=$u("^"+ga.call(lt).replace(Cu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),_a=Up?V.Buffer:s,Hr=V.Symbol,ya=V.Uint8Array,sm=_a?_a.allocUnsafe:s,ba=Xp(ct.getPrototypeOf,ct),rm=ct.create,om=Go.propertyIsEnumerable,wa=pa.splice,nm=Hr?Hr.isConcatSpreadable:s,zn=Hr?Hr.iterator:s,vo=Hr?Hr.toStringTag:s,Ea=function(){try{var r=Eo(ct,"defineProperty");return r({},"",{}),r}catch{}}(),FA=V.clearTimeout!==$t.clearTimeout&&V.clearTimeout,UA=fe&&fe.now!==$t.Date.now&&fe.now,LA=V.setTimeout!==$t.setTimeout&&V.setTimeout,Ca=Tt.ceil,Da=Tt.floor,ju=ct.getOwnPropertySymbols,BA=_a?_a.isBuffer:s,im=V.isFinite,$A=pa.join,jA=Xp(ct.keys,ct),Mt=Tt.max,Wt=Tt.min,HA=fe.now,qA=V.parseInt,am=Tt.random,zA=pa.reverse,Hu=Eo(V,"DataView"),Wn=Eo(V,"Map"),qu=Eo(V,"Promise"),Ko=Eo(V,"Set"),Gn=Eo(V,"WeakMap"),Kn=Eo(ct,"create"),xa=Gn&&new Gn,Qo={},WA=Co(Hu),GA=Co(Wn),KA=Co(qu),QA=Co(Ko),YA=Co(Gn),Sa=Hr?Hr.prototype:s,Qn=Sa?Sa.valueOf:s,lm=Sa?Sa.toString:s;function _(r){if(yt(r)&&!Ve(r)&&!(r instanceof Qe)){if(r instanceof xs)return r;if(lt.call(r,"__wrapped__"))return ug(r)}return new xs(r)}var Yo=function(){function r(){}return function(n){if(!gt(n))return{};if(rm)return rm(n);r.prototype=n;var l=new r;return r.prototype=s,l}}();function Oa(){}function xs(r,n){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=s}_.templateSettings={escape:cI,evaluate:dI,interpolate:vp,variable:"",imports:{_}},_.prototype=Oa.prototype,_.prototype.constructor=_,xs.prototype=Yo(Oa.prototype),xs.prototype.constructor=xs;function Qe(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=_t,this.__views__=[]}function ZA(){var r=new Qe(this.__wrapped__);return r.__actions__=os(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=os(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=os(this.__views__),r}function JA(){if(this.__filtered__){var r=new Qe(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function XA(){var r=this.__wrapped__.value(),n=this.__dir__,l=Ve(r),d=n<0,g=l?r.length:0,y=dM(0,g,this.__views__),E=y.start,x=y.end,O=x-E,L=d?x:E-1,B=this.__iteratees__,H=B.length,ue=0,ve=Wt(O,this.__takeCount__);if(!l||!d&&g==O&&ve==O)return Mm(r,this.__actions__);var Ne=[];e:for(;O--&&ue<ve;){L+=n;for(var je=-1,Ie=r[L];++je<H;){var Ge=B[je],Ye=Ge.iteratee,_s=Ge.type,rs=Ye(Ie);if(_s==$e)Ie=rs;else if(!rs){if(_s==J)continue e;break e}}Ne[ue++]=Ie}return Ne}Qe.prototype=Yo(Oa.prototype),Qe.prototype.constructor=Qe;function _o(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function eT(){this.__data__=Kn?Kn(null):{},this.size=0}function tT(r){var n=this.has(r)&&delete this.__data__[r];return this.size-=n?1:0,n}function sT(r){var n=this.__data__;if(Kn){var l=n[r];return l===f?s:l}return lt.call(n,r)?n[r]:s}function rT(r){var n=this.__data__;return Kn?n[r]!==s:lt.call(n,r)}function oT(r,n){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Kn&&n===s?f:n,this}_o.prototype.clear=eT,_o.prototype.delete=tT,_o.prototype.get=sT,_o.prototype.has=rT,_o.prototype.set=oT;function vr(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function nT(){this.__data__=[],this.size=0}function iT(r){var n=this.__data__,l=Na(n,r);if(l<0)return!1;var d=n.length-1;return l==d?n.pop():wa.call(n,l,1),--this.size,!0}function aT(r){var n=this.__data__,l=Na(n,r);return l<0?s:n[l][1]}function lT(r){return Na(this.__data__,r)>-1}function uT(r,n){var l=this.__data__,d=Na(l,r);return d<0?(++this.size,l.push([r,n])):l[d][1]=n,this}vr.prototype.clear=nT,vr.prototype.delete=iT,vr.prototype.get=aT,vr.prototype.has=lT,vr.prototype.set=uT;function _r(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function cT(){this.size=0,this.__data__={hash:new _o,map:new(Wn||vr),string:new _o}}function dT(r){var n=Ba(this,r).delete(r);return this.size-=n?1:0,n}function fT(r){return Ba(this,r).get(r)}function hT(r){return Ba(this,r).has(r)}function pT(r,n){var l=Ba(this,r),d=l.size;return l.set(r,n),this.size+=l.size==d?0:1,this}_r.prototype.clear=cT,_r.prototype.delete=dT,_r.prototype.get=fT,_r.prototype.has=hT,_r.prototype.set=pT;function yo(r){var n=-1,l=r==null?0:r.length;for(this.__data__=new _r;++n<l;)this.add(r[n])}function mT(r){return this.__data__.set(r,f),this}function gT(r){return this.__data__.has(r)}yo.prototype.add=yo.prototype.push=mT,yo.prototype.has=gT;function Bs(r){var n=this.__data__=new vr(r);this.size=n.size}function vT(){this.__data__=new vr,this.size=0}function _T(r){var n=this.__data__,l=n.delete(r);return this.size=n.size,l}function yT(r){return this.__data__.get(r)}function bT(r){return this.__data__.has(r)}function wT(r,n){var l=this.__data__;if(l instanceof vr){var d=l.__data__;if(!Wn||d.length<o-1)return d.push([r,n]),this.size=++l.size,this;l=this.__data__=new _r(d)}return l.set(r,n),this.size=l.size,this}Bs.prototype.clear=vT,Bs.prototype.delete=_T,Bs.prototype.get=yT,Bs.prototype.has=bT,Bs.prototype.set=wT;function um(r,n){var l=Ve(r),d=!l&&Do(r),g=!l&&!d&&Kr(r),y=!l&&!d&&!g&&en(r),E=l||d||g||y,x=E?Uu(r.length,TA):[],O=x.length;for(var L in r)(n||lt.call(r,L))&&!(E&&(L=="length"||g&&(L=="offset"||L=="parent")||y&&(L=="buffer"||L=="byteLength"||L=="byteOffset")||Er(L,O)))&&x.push(L);return x}function cm(r){var n=r.length;return n?r[tc(0,n-1)]:s}function ET(r,n){return $a(os(r),bo(n,0,r.length))}function CT(r){return $a(os(r))}function zu(r,n,l){(l!==s&&!$s(r[n],l)||l===s&&!(n in r))&&yr(r,n,l)}function Yn(r,n,l){var d=r[n];(!(lt.call(r,n)&&$s(d,l))||l===s&&!(n in r))&&yr(r,n,l)}function Na(r,n){for(var l=r.length;l--;)if($s(r[l][0],n))return l;return-1}function DT(r,n,l,d){return qr(r,function(g,y,E){n(d,g,l(g),E)}),d}function dm(r,n){return r&&Js(n,Rt(n),r)}function xT(r,n){return r&&Js(n,is(n),r)}function yr(r,n,l){n=="__proto__"&&Ea?Ea(r,n,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[n]=l}function Wu(r,n){for(var l=-1,d=n.length,g=M(d),y=r==null;++l<d;)g[l]=y?s:Sc(r,n[l]);return g}function bo(r,n,l){return r===r&&(l!==s&&(r=r<=l?r:l),n!==s&&(r=r>=n?r:n)),r}function Ss(r,n,l,d,g,y){var E,x=n&v,O=n&w,L=n&D;if(l&&(E=g?l(r,d,g,y):l(r)),E!==s)return E;if(!gt(r))return r;var B=Ve(r);if(B){if(E=hM(r),!x)return os(r,E)}else{var H=Gt(r),ue=H==at||H==Re;if(Kr(r))return Rm(r,x);if(H==F||H==zt||ue&&!g){if(E=O||ue?{}:eg(r),!x)return O?sM(r,xT(E,r)):tM(r,dm(E,r))}else{if(!dt[H])return g?r:{};E=pM(r,H,x)}}y||(y=new Bs);var ve=y.get(r);if(ve)return ve;y.set(r,E),Ig(r)?r.forEach(function(Ie){E.add(Ss(Ie,n,l,Ie,r,y))}):Og(r)&&r.forEach(function(Ie,Ge){E.set(Ge,Ss(Ie,n,l,Ge,r,y))});var Ne=L?O?fc:dc:O?is:Rt,je=B?s:Ne(r);return Cs(je||r,function(Ie,Ge){je&&(Ge=Ie,Ie=r[Ge]),Yn(E,Ge,Ss(Ie,n,l,Ge,r,y))}),E}function ST(r){var n=Rt(r);return function(l){return fm(l,r,n)}}function fm(r,n,l){var d=l.length;if(r==null)return!d;for(r=ct(r);d--;){var g=l[d],y=n[g],E=r[g];if(E===s&&!(g in r)||!y(E))return!1}return!0}function hm(r,n,l){if(typeof r!="function")throw new Ds(u);return ri(function(){r.apply(s,l)},n)}function Zn(r,n,l,d){var g=-1,y=da,E=!0,x=r.length,O=[],L=n.length;if(!x)return O;l&&(n=mt(n,ms(l))),d?(y=Mu,E=!1):n.length>=o&&(y=qn,E=!1,n=new yo(n));e:for(;++g<x;){var B=r[g],H=l==null?B:l(B);if(B=d||B!==0?B:0,E&&H===H){for(var ue=L;ue--;)if(n[ue]===H)continue e;O.push(B)}else y(n,H,d)||O.push(B)}return O}var qr=Bm(Zs),pm=Bm(Ku,!0);function OT(r,n){var l=!0;return qr(r,function(d,g,y){return l=!!n(d,g,y),l}),l}function Ia(r,n,l){for(var d=-1,g=r.length;++d<g;){var y=r[d],E=n(y);if(E!=null&&(x===s?E===E&&!vs(E):l(E,x)))var x=E,O=y}return O}function NT(r,n,l,d){var g=r.length;for(l=Le(l),l<0&&(l=-l>g?0:g+l),d=d===s||d>g?g:Le(d),d<0&&(d+=g),d=l>d?0:Tg(d);l<d;)r[l++]=n;return r}function mm(r,n){var l=[];return qr(r,function(d,g,y){n(d,g,y)&&l.push(d)}),l}function jt(r,n,l,d,g){var y=-1,E=r.length;for(l||(l=gM),g||(g=[]);++y<E;){var x=r[y];n>0&&l(x)?n>1?jt(x,n-1,l,d,g):$r(g,x):d||(g[g.length]=x)}return g}var Gu=$m(),gm=$m(!0);function Zs(r,n){return r&&Gu(r,n,Rt)}function Ku(r,n){return r&&gm(r,n,Rt)}function Aa(r,n){return Br(n,function(l){return Cr(r[l])})}function wo(r,n){n=Wr(n,r);for(var l=0,d=n.length;r!=null&&l<d;)r=r[Xs(n[l++])];return l&&l==d?r:s}function vm(r,n,l){var d=n(r);return Ve(r)?d:$r(d,l(r))}function ts(r){return r==null?r===s?Ce:P:vo&&vo in ct(r)?cM(r):CM(r)}function Qu(r,n){return r>n}function IT(r,n){return r!=null&&lt.call(r,n)}function AT(r,n){return r!=null&&n in ct(r)}function TT(r,n,l){return r>=Wt(n,l)&&r<Mt(n,l)}function Yu(r,n,l){for(var d=l?Mu:da,g=r[0].length,y=r.length,E=y,x=M(y),O=1/0,L=[];E--;){var B=r[E];E&&n&&(B=mt(B,ms(n))),O=Wt(B.length,O),x[E]=!l&&(n||g>=120&&B.length>=120)?new yo(E&&B):s}B=r[0];var H=-1,ue=x[0];e:for(;++H<g&&L.length<O;){var ve=B[H],Ne=n?n(ve):ve;if(ve=l||ve!==0?ve:0,!(ue?qn(ue,Ne):d(L,Ne,l))){for(E=y;--E;){var je=x[E];if(!(je?qn(je,Ne):d(r[E],Ne,l)))continue e}ue&&ue.push(Ne),L.push(ve)}}return L}function MT(r,n,l,d){return Zs(r,function(g,y,E){n(d,l(g),y,E)}),d}function Jn(r,n,l){n=Wr(n,r),r=og(r,n);var d=r==null?r:r[Xs(Ns(n))];return d==null?s:ps(d,r,l)}function _m(r){return yt(r)&&ts(r)==zt}function PT(r){return yt(r)&&ts(r)==De}function kT(r){return yt(r)&&ts(r)==re}function Xn(r,n,l,d,g){return r===n?!0:r==null||n==null||!yt(r)&&!yt(n)?r!==r&&n!==n:RT(r,n,l,d,Xn,g)}function RT(r,n,l,d,g,y){var E=Ve(r),x=Ve(n),O=E?hs:Gt(r),L=x?hs:Gt(n);O=O==zt?F:O,L=L==zt?F:L;var B=O==F,H=L==F,ue=O==L;if(ue&&Kr(r)){if(!Kr(n))return!1;E=!0,B=!1}if(ue&&!B)return y||(y=new Bs),E||en(r)?Zm(r,n,l,d,g,y):lM(r,n,O,l,d,g,y);if(!(l&k)){var ve=B&&lt.call(r,"__wrapped__"),Ne=H&&lt.call(n,"__wrapped__");if(ve||Ne){var je=ve?r.value():r,Ie=Ne?n.value():n;return y||(y=new Bs),g(je,Ie,l,d,y)}}return ue?(y||(y=new Bs),uM(r,n,l,d,g,y)):!1}function VT(r){return yt(r)&&Gt(r)==b}function Zu(r,n,l,d){var g=l.length,y=g,E=!d;if(r==null)return!y;for(r=ct(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<y;){x=l[g];var O=x[0],L=r[O],B=x[1];if(E&&x[2]){if(L===s&&!(O in r))return!1}else{var H=new Bs;if(d)var ue=d(L,B,O,r,n,H);if(!(ue===s?Xn(B,L,k|U,d,H):ue))return!1}}return!0}function ym(r){if(!gt(r)||_M(r))return!1;var n=Cr(r)?VA:SI;return n.test(Co(r))}function FT(r){return yt(r)&&ts(r)==ee}function UT(r){return yt(r)&&Gt(r)==W}function LT(r){return yt(r)&&Ga(r.length)&&!!ht[ts(r)]}function bm(r){return typeof r=="function"?r:r==null?as:typeof r=="object"?Ve(r)?Cm(r[0],r[1]):Em(r):jg(r)}function Ju(r){if(!si(r))return jA(r);var n=[];for(var l in ct(r))lt.call(r,l)&&l!="constructor"&&n.push(l);return n}function BT(r){if(!gt(r))return EM(r);var n=si(r),l=[];for(var d in r)d=="constructor"&&(n||!lt.call(r,d))||l.push(d);return l}function Xu(r,n){return r<n}function wm(r,n){var l=-1,d=ns(r)?M(r.length):[];return qr(r,function(g,y,E){d[++l]=n(g,y,E)}),d}function Em(r){var n=pc(r);return n.length==1&&n[0][2]?sg(n[0][0],n[0][1]):function(l){return l===r||Zu(l,r,n)}}function Cm(r,n){return gc(r)&&tg(n)?sg(Xs(r),n):function(l){var d=Sc(l,r);return d===s&&d===n?Oc(l,r):Xn(n,d,k|U)}}function Ta(r,n,l,d,g){r!==n&&Gu(n,function(y,E){if(g||(g=new Bs),gt(y))$T(r,n,E,l,Ta,d,g);else{var x=d?d(_c(r,E),y,E+"",r,n,g):s;x===s&&(x=y),zu(r,E,x)}},is)}function $T(r,n,l,d,g,y,E){var x=_c(r,l),O=_c(n,l),L=E.get(O);if(L){zu(r,l,L);return}var B=y?y(x,O,l+"",r,n,E):s,H=B===s;if(H){var ue=Ve(O),ve=!ue&&Kr(O),Ne=!ue&&!ve&&en(O);B=O,ue||ve||Ne?Ve(x)?B=x:Et(x)?B=os(x):ve?(H=!1,B=Rm(O,!0)):Ne?(H=!1,B=Vm(O,!0)):B=[]:oi(O)||Do(O)?(B=x,Do(x)?B=Mg(x):(!gt(x)||Cr(x))&&(B=eg(O))):H=!1}H&&(E.set(O,B),g(B,O,d,y,E),E.delete(O)),zu(r,l,B)}function Dm(r,n){var l=r.length;if(l)return n+=n<0?l:0,Er(n,l)?r[n]:s}function xm(r,n,l){n.length?n=mt(n,function(y){return Ve(y)?function(E){return wo(E,y.length===1?y[0]:y)}:y}):n=[as];var d=-1;n=mt(n,ms(xe()));var g=wm(r,function(y,E,x){var O=mt(n,function(L){return L(y)});return{criteria:O,index:++d,value:y}});return pA(g,function(y,E){return eM(y,E,l)})}function jT(r,n){return Sm(r,n,function(l,d){return Oc(r,d)})}function Sm(r,n,l){for(var d=-1,g=n.length,y={};++d<g;){var E=n[d],x=wo(r,E);l(x,E)&&ei(y,Wr(E,r),x)}return y}function HT(r){return function(n){return wo(n,r)}}function ec(r,n,l,d){var g=d?hA:Ho,y=-1,E=n.length,x=r;for(r===n&&(n=os(n)),l&&(x=mt(r,ms(l)));++y<E;)for(var O=0,L=n[y],B=l?l(L):L;(O=g(x,B,O,d))>-1;)x!==r&&wa.call(x,O,1),wa.call(r,O,1);return r}function Om(r,n){for(var l=r?n.length:0,d=l-1;l--;){var g=n[l];if(l==d||g!==y){var y=g;Er(g)?wa.call(r,g,1):oc(r,g)}}return r}function tc(r,n){return r+Da(am()*(n-r+1))}function qT(r,n,l,d){for(var g=-1,y=Mt(Ca((n-r)/(l||1)),0),E=M(y);y--;)E[d?y:++g]=r,r+=l;return E}function sc(r,n){var l="";if(!r||n<1||n>Ee)return l;do n%2&&(l+=r),n=Da(n/2),n&&(r+=r);while(n);return l}function qe(r,n){return yc(rg(r,n,as),r+"")}function zT(r){return cm(tn(r))}function WT(r,n){var l=tn(r);return $a(l,bo(n,0,l.length))}function ei(r,n,l,d){if(!gt(r))return r;n=Wr(n,r);for(var g=-1,y=n.length,E=y-1,x=r;x!=null&&++g<y;){var O=Xs(n[g]),L=l;if(O==="__proto__"||O==="constructor"||O==="prototype")return r;if(g!=E){var B=x[O];L=d?d(B,O,x):s,L===s&&(L=gt(B)?B:Er(n[g+1])?[]:{})}Yn(x,O,L),x=x[O]}return r}var Nm=xa?function(r,n){return xa.set(r,n),r}:as,GT=Ea?function(r,n){return Ea(r,"toString",{configurable:!0,enumerable:!1,value:Ic(n),writable:!0})}:as;function KT(r){return $a(tn(r))}function Os(r,n,l){var d=-1,g=r.length;n<0&&(n=-n>g?0:g+n),l=l>g?g:l,l<0&&(l+=g),g=n>l?0:l-n>>>0,n>>>=0;for(var y=M(g);++d<g;)y[d]=r[d+n];return y}function QT(r,n){var l;return qr(r,function(d,g,y){return l=n(d,g,y),!l}),!!l}function Ma(r,n,l){var d=0,g=r==null?d:r.length;if(typeof n=="number"&&n===n&&g<=Ke){for(;d<g;){var y=d+g>>>1,E=r[y];E!==null&&!vs(E)&&(l?E<=n:E<n)?d=y+1:g=y}return g}return rc(r,n,as,l)}function rc(r,n,l,d){var g=0,y=r==null?0:r.length;if(y===0)return 0;n=l(n);for(var E=n!==n,x=n===null,O=vs(n),L=n===s;g<y;){var B=Da((g+y)/2),H=l(r[B]),ue=H!==s,ve=H===null,Ne=H===H,je=vs(H);if(E)var Ie=d||Ne;else L?Ie=Ne&&(d||ue):x?Ie=Ne&&ue&&(d||!ve):O?Ie=Ne&&ue&&!ve&&(d||!je):ve||je?Ie=!1:Ie=d?H<=n:H<n;Ie?g=B+1:y=B}return Wt(y,de)}function Im(r,n){for(var l=-1,d=r.length,g=0,y=[];++l<d;){var E=r[l],x=n?n(E):E;if(!l||!$s(x,O)){var O=x;y[g++]=E===0?0:E}}return y}function Am(r){return typeof r=="number"?r:vs(r)?Xt:+r}function gs(r){if(typeof r=="string")return r;if(Ve(r))return mt(r,gs)+"";if(vs(r))return lm?lm.call(r):"";var n=r+"";return n=="0"&&1/r==-Oe?"-0":n}function zr(r,n,l){var d=-1,g=da,y=r.length,E=!0,x=[],O=x;if(l)E=!1,g=Mu;else if(y>=o){var L=n?null:iM(r);if(L)return ha(L);E=!1,g=qn,O=new yo}else O=n?[]:x;e:for(;++d<y;){var B=r[d],H=n?n(B):B;if(B=l||B!==0?B:0,E&&H===H){for(var ue=O.length;ue--;)if(O[ue]===H)continue e;n&&O.push(H),x.push(B)}else g(O,H,l)||(O!==x&&O.push(H),x.push(B))}return x}function oc(r,n){return n=Wr(n,r),r=og(r,n),r==null||delete r[Xs(Ns(n))]}function Tm(r,n,l,d){return ei(r,n,l(wo(r,n)),d)}function Pa(r,n,l,d){for(var g=r.length,y=d?g:-1;(d?y--:++y<g)&&n(r[y],y,r););return l?Os(r,d?0:y,d?y+1:g):Os(r,d?y+1:0,d?g:y)}function Mm(r,n){var l=r;return l instanceof Qe&&(l=l.value()),Pu(n,function(d,g){return g.func.apply(g.thisArg,$r([d],g.args))},l)}function nc(r,n,l){var d=r.length;if(d<2)return d?zr(r[0]):[];for(var g=-1,y=M(d);++g<d;)for(var E=r[g],x=-1;++x<d;)x!=g&&(y[g]=Zn(y[g]||E,r[x],n,l));return zr(jt(y,1),n,l)}function Pm(r,n,l){for(var d=-1,g=r.length,y=n.length,E={};++d<g;){var x=d<y?n[d]:s;l(E,r[d],x)}return E}function ic(r){return Et(r)?r:[]}function ac(r){return typeof r=="function"?r:as}function Wr(r,n){return Ve(r)?r:gc(r,n)?[r]:lg(it(r))}var YT=qe;function Gr(r,n,l){var d=r.length;return l=l===s?d:l,!n&&l>=d?r:Os(r,n,l)}var km=FA||function(r){return $t.clearTimeout(r)};function Rm(r,n){if(n)return r.slice();var l=r.length,d=sm?sm(l):new r.constructor(l);return r.copy(d),d}function lc(r){var n=new r.constructor(r.byteLength);return new ya(n).set(new ya(r)),n}function ZT(r,n){var l=n?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function JT(r){var n=new r.constructor(r.source,_p.exec(r));return n.lastIndex=r.lastIndex,n}function XT(r){return Qn?ct(Qn.call(r)):{}}function Vm(r,n){var l=n?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Fm(r,n){if(r!==n){var l=r!==s,d=r===null,g=r===r,y=vs(r),E=n!==s,x=n===null,O=n===n,L=vs(n);if(!x&&!L&&!y&&r>n||y&&E&&O&&!x&&!L||d&&E&&O||!l&&O||!g)return 1;if(!d&&!y&&!L&&r<n||L&&l&&g&&!d&&!y||x&&l&&g||!E&&g||!O)return-1}return 0}function eM(r,n,l){for(var d=-1,g=r.criteria,y=n.criteria,E=g.length,x=l.length;++d<E;){var O=Fm(g[d],y[d]);if(O){if(d>=x)return O;var L=l[d];return O*(L=="desc"?-1:1)}}return r.index-n.index}function Um(r,n,l,d){for(var g=-1,y=r.length,E=l.length,x=-1,O=n.length,L=Mt(y-E,0),B=M(O+L),H=!d;++x<O;)B[x]=n[x];for(;++g<E;)(H||g<y)&&(B[l[g]]=r[g]);for(;L--;)B[x++]=r[g++];return B}function Lm(r,n,l,d){for(var g=-1,y=r.length,E=-1,x=l.length,O=-1,L=n.length,B=Mt(y-x,0),H=M(B+L),ue=!d;++g<B;)H[g]=r[g];for(var ve=g;++O<L;)H[ve+O]=n[O];for(;++E<x;)(ue||g<y)&&(H[ve+l[E]]=r[g++]);return H}function os(r,n){var l=-1,d=r.length;for(n||(n=M(d));++l<d;)n[l]=r[l];return n}function Js(r,n,l,d){var g=!l;l||(l={});for(var y=-1,E=n.length;++y<E;){var x=n[y],O=d?d(l[x],r[x],x,l,r):s;O===s&&(O=r[x]),g?yr(l,x,O):Yn(l,x,O)}return l}function tM(r,n){return Js(r,mc(r),n)}function sM(r,n){return Js(r,Jm(r),n)}function ka(r,n){return function(l,d){var g=Ve(l)?aA:DT,y=n?n():{};return g(l,r,xe(d,2),y)}}function Zo(r){return qe(function(n,l){var d=-1,g=l.length,y=g>1?l[g-1]:s,E=g>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(g--,y):s,E&&ss(l[0],l[1],E)&&(y=g<3?s:y,g=1),n=ct(n);++d<g;){var x=l[d];x&&r(n,x,d,y)}return n})}function Bm(r,n){return function(l,d){if(l==null)return l;if(!ns(l))return r(l,d);for(var g=l.length,y=n?g:-1,E=ct(l);(n?y--:++y<g)&&d(E[y],y,E)!==!1;);return l}}function $m(r){return function(n,l,d){for(var g=-1,y=ct(n),E=d(n),x=E.length;x--;){var O=E[r?x:++g];if(l(y[O],O,y)===!1)break}return n}}function rM(r,n,l){var d=n&te,g=ti(r);function y(){var E=this&&this!==$t&&this instanceof y?g:r;return E.apply(d?l:this,arguments)}return y}function jm(r){return function(n){n=it(n);var l=qo(n)?Ls(n):s,d=l?l[0]:n.charAt(0),g=l?Gr(l,1).join(""):n.slice(1);return d[r]()+g}}function Jo(r){return function(n){return Pu(Bg(Lg(n).replace(GI,"")),r,"")}}function ti(r){return function(){var n=arguments;switch(n.length){case 0:return new r;case 1:return new r(n[0]);case 2:return new r(n[0],n[1]);case 3:return new r(n[0],n[1],n[2]);case 4:return new r(n[0],n[1],n[2],n[3]);case 5:return new r(n[0],n[1],n[2],n[3],n[4]);case 6:return new r(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new r(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var l=Yo(r.prototype),d=r.apply(l,n);return gt(d)?d:l}}function oM(r,n,l){var d=ti(r);function g(){for(var y=arguments.length,E=M(y),x=y,O=Xo(g);x--;)E[x]=arguments[x];var L=y<3&&E[0]!==O&&E[y-1]!==O?[]:jr(E,O);if(y-=L.length,y<l)return Gm(r,n,Ra,g.placeholder,s,E,L,s,s,l-y);var B=this&&this!==$t&&this instanceof g?d:r;return ps(B,this,E)}return g}function Hm(r){return function(n,l,d){var g=ct(n);if(!ns(n)){var y=xe(l,3);n=Rt(n),l=function(x){return y(g[x],x,g)}}var E=r(n,l,d);return E>-1?g[y?n[E]:E]:s}}function qm(r){return wr(function(n){var l=n.length,d=l,g=xs.prototype.thru;for(r&&n.reverse();d--;){var y=n[d];if(typeof y!="function")throw new Ds(u);if(g&&!E&&La(y)=="wrapper")var E=new xs([],!0)}for(d=E?d:l;++d<l;){y=n[d];var x=La(y),O=x=="wrapper"?hc(y):s;O&&vc(O[0])&&O[1]==(be|Q|Y|Te)&&!O[4].length&&O[9]==1?E=E[La(O[0])].apply(E,O[3]):E=y.length==1&&vc(y)?E[x]():E.thru(y)}return function(){var L=arguments,B=L[0];if(E&&L.length==1&&Ve(B))return E.plant(B).value();for(var H=0,ue=l?n[H].apply(this,L):B;++H<l;)ue=n[H].call(this,ue);return ue}})}function Ra(r,n,l,d,g,y,E,x,O,L){var B=n&be,H=n&te,ue=n&T,ve=n&(Q|we),Ne=n&le,je=ue?s:ti(r);function Ie(){for(var Ge=arguments.length,Ye=M(Ge),_s=Ge;_s--;)Ye[_s]=arguments[_s];if(ve)var rs=Xo(Ie),ys=gA(Ye,rs);if(d&&(Ye=Um(Ye,d,g,ve)),y&&(Ye=Lm(Ye,y,E,ve)),Ge-=ys,ve&&Ge<L){var Ct=jr(Ye,rs);return Gm(r,n,Ra,Ie.placeholder,l,Ye,Ct,x,O,L-Ge)}var js=H?l:this,xr=ue?js[r]:r;return Ge=Ye.length,x?Ye=DM(Ye,x):Ne&&Ge>1&&Ye.reverse(),B&&O<Ge&&(Ye.length=O),this&&this!==$t&&this instanceof Ie&&(xr=je||ti(xr)),xr.apply(js,Ye)}return Ie}function zm(r,n){return function(l,d){return MT(l,r,n(d),{})}}function Va(r,n){return function(l,d){var g;if(l===s&&d===s)return n;if(l!==s&&(g=l),d!==s){if(g===s)return d;typeof l=="string"||typeof d=="string"?(l=gs(l),d=gs(d)):(l=Am(l),d=Am(d)),g=r(l,d)}return g}}function uc(r){return wr(function(n){return n=mt(n,ms(xe())),qe(function(l){var d=this;return r(n,function(g){return ps(g,d,l)})})})}function Fa(r,n){n=n===s?" ":gs(n);var l=n.length;if(l<2)return l?sc(n,r):n;var d=sc(n,Ca(r/zo(n)));return qo(n)?Gr(Ls(d),0,r).join(""):d.slice(0,r)}function nM(r,n,l,d){var g=n&te,y=ti(r);function E(){for(var x=-1,O=arguments.length,L=-1,B=d.length,H=M(B+O),ue=this&&this!==$t&&this instanceof E?y:r;++L<B;)H[L]=d[L];for(;O--;)H[L++]=arguments[++x];return ps(ue,g?l:this,H)}return E}function Wm(r){return function(n,l,d){return d&&typeof d!="number"&&ss(n,l,d)&&(l=d=s),n=Dr(n),l===s?(l=n,n=0):l=Dr(l),d=d===s?n<l?1:-1:Dr(d),qT(n,l,d,r)}}function Ua(r){return function(n,l){return typeof n=="string"&&typeof l=="string"||(n=Is(n),l=Is(l)),r(n,l)}}function Gm(r,n,l,d,g,y,E,x,O,L){var B=n&Q,H=B?E:s,ue=B?s:E,ve=B?y:s,Ne=B?s:y;n|=B?Y:he,n&=~(B?he:Y),n&oe||(n&=~(te|T));var je=[r,n,g,ve,H,Ne,ue,x,O,L],Ie=l.apply(s,je);return vc(r)&&ng(Ie,je),Ie.placeholder=d,ig(Ie,r,n)}function cc(r){var n=Tt[r];return function(l,d){if(l=Is(l),d=d==null?0:Wt(Le(d),292),d&&im(l)){var g=(it(l)+"e").split("e"),y=n(g[0]+"e"+(+g[1]+d));return g=(it(y)+"e").split("e"),+(g[0]+"e"+(+g[1]-d))}return n(l)}}var iM=Ko&&1/ha(new Ko([,-0]))[1]==Oe?function(r){return new Ko(r)}:Mc;function Km(r){return function(n){var l=Gt(n);return l==b?Bu(n):l==W?CA(n):mA(n,r(n))}}function br(r,n,l,d,g,y,E,x){var O=n&T;if(!O&&typeof r!="function")throw new Ds(u);var L=d?d.length:0;if(L||(n&=~(Y|he),d=g=s),E=E===s?E:Mt(Le(E),0),x=x===s?x:Le(x),L-=g?g.length:0,n&he){var B=d,H=g;d=g=s}var ue=O?s:hc(r),ve=[r,n,l,d,g,B,H,y,E,x];if(ue&&wM(ve,ue),r=ve[0],n=ve[1],l=ve[2],d=ve[3],g=ve[4],x=ve[9]=ve[9]===s?O?0:r.length:Mt(ve[9]-L,0),!x&&n&(Q|we)&&(n&=~(Q|we)),!n||n==te)var Ne=rM(r,n,l);else n==Q||n==we?Ne=oM(r,n,x):(n==Y||n==(te|Y))&&!g.length?Ne=nM(r,n,l,d):Ne=Ra.apply(s,ve);var je=ue?Nm:ng;return ig(je(Ne,ve),r,n)}function Qm(r,n,l,d){return r===s||$s(r,Go[l])&&!lt.call(d,l)?n:r}function Ym(r,n,l,d,g,y){return gt(r)&&gt(n)&&(y.set(n,r),Ta(r,n,s,Ym,y),y.delete(n)),r}function aM(r){return oi(r)?s:r}function Zm(r,n,l,d,g,y){var E=l&k,x=r.length,O=n.length;if(x!=O&&!(E&&O>x))return!1;var L=y.get(r),B=y.get(n);if(L&&B)return L==n&&B==r;var H=-1,ue=!0,ve=l&U?new yo:s;for(y.set(r,n),y.set(n,r);++H<x;){var Ne=r[H],je=n[H];if(d)var Ie=E?d(je,Ne,H,n,r,y):d(Ne,je,H,r,n,y);if(Ie!==s){if(Ie)continue;ue=!1;break}if(ve){if(!ku(n,function(Ge,Ye){if(!qn(ve,Ye)&&(Ne===Ge||g(Ne,Ge,l,d,y)))return ve.push(Ye)})){ue=!1;break}}else if(!(Ne===je||g(Ne,je,l,d,y))){ue=!1;break}}return y.delete(r),y.delete(n),ue}function lM(r,n,l,d,g,y,E){switch(l){case Ue:if(r.byteLength!=n.byteLength||r.byteOffset!=n.byteOffset)return!1;r=r.buffer,n=n.buffer;case De:return!(r.byteLength!=n.byteLength||!y(new ya(r),new ya(n)));case ne:case re:case C:return $s(+r,+n);case ke:return r.name==n.name&&r.message==n.message;case ee:case Z:return r==n+"";case b:var x=Bu;case W:var O=d&k;if(x||(x=ha),r.size!=n.size&&!O)return!1;var L=E.get(r);if(L)return L==n;d|=U,E.set(r,n);var B=Zm(x(r),x(n),d,g,y,E);return E.delete(r),B;case z:if(Qn)return Qn.call(r)==Qn.call(n)}return!1}function uM(r,n,l,d,g,y){var E=l&k,x=dc(r),O=x.length,L=dc(n),B=L.length;if(O!=B&&!E)return!1;for(var H=O;H--;){var ue=x[H];if(!(E?ue in n:lt.call(n,ue)))return!1}var ve=y.get(r),Ne=y.get(n);if(ve&&Ne)return ve==n&&Ne==r;var je=!0;y.set(r,n),y.set(n,r);for(var Ie=E;++H<O;){ue=x[H];var Ge=r[ue],Ye=n[ue];if(d)var _s=E?d(Ye,Ge,ue,n,r,y):d(Ge,Ye,ue,r,n,y);if(!(_s===s?Ge===Ye||g(Ge,Ye,l,d,y):_s)){je=!1;break}Ie||(Ie=ue=="constructor")}if(je&&!Ie){var rs=r.constructor,ys=n.constructor;rs!=ys&&"constructor"in r&&"constructor"in n&&!(typeof rs=="function"&&rs instanceof rs&&typeof ys=="function"&&ys instanceof ys)&&(je=!1)}return y.delete(r),y.delete(n),je}function wr(r){return yc(rg(r,s,fg),r+"")}function dc(r){return vm(r,Rt,mc)}function fc(r){return vm(r,is,Jm)}var hc=xa?function(r){return xa.get(r)}:Mc;function La(r){for(var n=r.name+"",l=Qo[n],d=lt.call(Qo,n)?l.length:0;d--;){var g=l[d],y=g.func;if(y==null||y==r)return g.name}return n}function Xo(r){var n=lt.call(_,"placeholder")?_:r;return n.placeholder}function xe(){var r=_.iteratee||Ac;return r=r===Ac?bm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ba(r,n){var l=r.__data__;return vM(n)?l[typeof n=="string"?"string":"hash"]:l.map}function pc(r){for(var n=Rt(r),l=n.length;l--;){var d=n[l],g=r[d];n[l]=[d,g,tg(g)]}return n}function Eo(r,n){var l=bA(r,n);return ym(l)?l:s}function cM(r){var n=lt.call(r,vo),l=r[vo];try{r[vo]=s;var d=!0}catch{}var g=va.call(r);return d&&(n?r[vo]=l:delete r[vo]),g}var mc=ju?function(r){return r==null?[]:(r=ct(r),Br(ju(r),function(n){return om.call(r,n)}))}:Pc,Jm=ju?function(r){for(var n=[];r;)$r(n,mc(r)),r=ba(r);return n}:Pc,Gt=ts;(Hu&&Gt(new Hu(new ArrayBuffer(1)))!=Ue||Wn&&Gt(new Wn)!=b||qu&&Gt(qu.resolve())!=j||Ko&&Gt(new Ko)!=W||Gn&&Gt(new Gn)!=se)&&(Gt=function(r){var n=ts(r),l=n==F?r.constructor:s,d=l?Co(l):"";if(d)switch(d){case WA:return Ue;case GA:return b;case KA:return j;case QA:return W;case YA:return se}return n});function dM(r,n,l){for(var d=-1,g=l.length;++d<g;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":n-=E;break;case"take":n=Wt(n,r+E);break;case"takeRight":r=Mt(r,n-E);break}}return{start:r,end:n}}function fM(r){var n=r.match(_I);return n?n[1].split(yI):[]}function Xm(r,n,l){n=Wr(n,r);for(var d=-1,g=n.length,y=!1;++d<g;){var E=Xs(n[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=g?y:(g=r==null?0:r.length,!!g&&Ga(g)&&Er(E,g)&&(Ve(r)||Do(r)))}function hM(r){var n=r.length,l=new r.constructor(n);return n&&typeof r[0]=="string"&&lt.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function eg(r){return typeof r.constructor=="function"&&!si(r)?Yo(ba(r)):{}}function pM(r,n,l){var d=r.constructor;switch(n){case De:return lc(r);case ne:case re:return new d(+r);case Ue:return ZT(r,l);case tt:case Je:case Lt:case St:case es:case Bt:case gr:case $o:case At:return Vm(r,l);case b:return new d;case C:case Z:return new d(r);case ee:return JT(r);case W:return new d;case z:return XT(r)}}function mM(r,n){var l=n.length;if(!l)return r;var d=l-1;return n[d]=(l>1?"& ":"")+n[d],n=n.join(l>2?", ":" "),r.replace(vI,`{
/* [wrapped with `+n+`] */
`)}function gM(r){return Ve(r)||Do(r)||!!(nm&&r&&r[nm])}function Er(r,n){var l=typeof r;return n=n??Ee,!!n&&(l=="number"||l!="symbol"&&NI.test(r))&&r>-1&&r%1==0&&r<n}function ss(r,n,l){if(!gt(l))return!1;var d=typeof n;return(d=="number"?ns(l)&&Er(n,l.length):d=="string"&&n in l)?$s(l[n],r):!1}function gc(r,n){if(Ve(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||vs(r)?!0:hI.test(r)||!fI.test(r)||n!=null&&r in ct(n)}function vM(r){var n=typeof r;return n=="string"||n=="number"||n=="symbol"||n=="boolean"?r!=="__proto__":r===null}function vc(r){var n=La(r),l=_[n];if(typeof l!="function"||!(n in Qe.prototype))return!1;if(r===l)return!0;var d=hc(l);return!!d&&r===d[0]}function _M(r){return!!tm&&tm in r}var yM=ma?Cr:kc;function si(r){var n=r&&r.constructor,l=typeof n=="function"&&n.prototype||Go;return r===l}function tg(r){return r===r&&!gt(r)}function sg(r,n){return function(l){return l==null?!1:l[r]===n&&(n!==s||r in ct(l))}}function bM(r){var n=za(r,function(d){return l.size===m&&l.clear(),d}),l=n.cache;return n}function wM(r,n){var l=r[1],d=n[1],g=l|d,y=g<(te|T|be),E=d==be&&l==Q||d==be&&l==Te&&r[7].length<=n[8]||d==(be|Te)&&n[7].length<=n[8]&&l==Q;if(!(y||E))return r;d&te&&(r[2]=n[2],g|=l&te?0:oe);var x=n[3];if(x){var O=r[3];r[3]=O?Um(O,x,n[4]):x,r[4]=O?jr(r[3],p):n[4]}return x=n[5],x&&(O=r[5],r[5]=O?Lm(O,x,n[6]):x,r[6]=O?jr(r[5],p):n[6]),x=n[7],x&&(r[7]=x),d&be&&(r[8]=r[8]==null?n[8]:Wt(r[8],n[8])),r[9]==null&&(r[9]=n[9]),r[0]=n[0],r[1]=g,r}function EM(r){var n=[];if(r!=null)for(var l in ct(r))n.push(l);return n}function CM(r){return va.call(r)}function rg(r,n,l){return n=Mt(n===s?r.length-1:n,0),function(){for(var d=arguments,g=-1,y=Mt(d.length-n,0),E=M(y);++g<y;)E[g]=d[n+g];g=-1;for(var x=M(n+1);++g<n;)x[g]=d[g];return x[n]=l(E),ps(r,this,x)}}function og(r,n){return n.length<2?r:wo(r,Os(n,0,-1))}function DM(r,n){for(var l=r.length,d=Wt(n.length,l),g=os(r);d--;){var y=n[d];r[d]=Er(y,l)?g[y]:s}return r}function _c(r,n){if(!(n==="constructor"&&typeof r[n]=="function")&&n!="__proto__")return r[n]}var ng=ag(Nm),ri=LA||function(r,n){return $t.setTimeout(r,n)},yc=ag(GT);function ig(r,n,l){var d=n+"";return yc(r,mM(d,xM(fM(d),l)))}function ag(r){var n=0,l=0;return function(){var d=HA(),g=We-(d-l);if(l=d,g>0){if(++n>=ae)return arguments[0]}else n=0;return r.apply(s,arguments)}}function $a(r,n){var l=-1,d=r.length,g=d-1;for(n=n===s?d:n;++l<n;){var y=tc(l,g),E=r[y];r[y]=r[l],r[l]=E}return r.length=n,r}var lg=bM(function(r){var n=[];return r.charCodeAt(0)===46&&n.push(""),r.replace(pI,function(l,d,g,y){n.push(g?y.replace(EI,"$1"):d||l)}),n});function Xs(r){if(typeof r=="string"||vs(r))return r;var n=r+"";return n=="0"&&1/r==-Oe?"-0":n}function Co(r){if(r!=null){try{return ga.call(r)}catch{}try{return r+""}catch{}}return""}function xM(r,n){return Cs(fs,function(l){var d="_."+l[0];n&l[1]&&!da(r,d)&&r.push(d)}),r.sort()}function ug(r){if(r instanceof Qe)return r.clone();var n=new xs(r.__wrapped__,r.__chain__);return n.__actions__=os(r.__actions__),n.__index__=r.__index__,n.__values__=r.__values__,n}function SM(r,n,l){(l?ss(r,n,l):n===s)?n=1:n=Mt(Le(n),0);var d=r==null?0:r.length;if(!d||n<1)return[];for(var g=0,y=0,E=M(Ca(d/n));g<d;)E[y++]=Os(r,g,g+=n);return E}function OM(r){for(var n=-1,l=r==null?0:r.length,d=0,g=[];++n<l;){var y=r[n];y&&(g[d++]=y)}return g}function NM(){var r=arguments.length;if(!r)return[];for(var n=M(r-1),l=arguments[0],d=r;d--;)n[d-1]=arguments[d];return $r(Ve(l)?os(l):[l],jt(n,1))}var IM=qe(function(r,n){return Et(r)?Zn(r,jt(n,1,Et,!0)):[]}),AM=qe(function(r,n){var l=Ns(n);return Et(l)&&(l=s),Et(r)?Zn(r,jt(n,1,Et,!0),xe(l,2)):[]}),TM=qe(function(r,n){var l=Ns(n);return Et(l)&&(l=s),Et(r)?Zn(r,jt(n,1,Et,!0),s,l):[]});function MM(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Le(n),Os(r,n<0?0:n,d)):[]}function PM(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Le(n),n=d-n,Os(r,0,n<0?0:n)):[]}function kM(r,n){return r&&r.length?Pa(r,xe(n,3),!0,!0):[]}function RM(r,n){return r&&r.length?Pa(r,xe(n,3),!0):[]}function VM(r,n,l,d){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&ss(r,n,l)&&(l=0,d=g),NT(r,n,l,d)):[]}function cg(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Le(l);return g<0&&(g=Mt(d+g,0)),fa(r,xe(n,3),g)}function dg(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=d-1;return l!==s&&(g=Le(l),g=l<0?Mt(d+g,0):Wt(g,d-1)),fa(r,xe(n,3),g,!0)}function fg(r){var n=r==null?0:r.length;return n?jt(r,1):[]}function FM(r){var n=r==null?0:r.length;return n?jt(r,Oe):[]}function UM(r,n){var l=r==null?0:r.length;return l?(n=n===s?1:Le(n),jt(r,n)):[]}function LM(r){for(var n=-1,l=r==null?0:r.length,d={};++n<l;){var g=r[n];d[g[0]]=g[1]}return d}function hg(r){return r&&r.length?r[0]:s}function BM(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Le(l);return g<0&&(g=Mt(d+g,0)),Ho(r,n,g)}function $M(r){var n=r==null?0:r.length;return n?Os(r,0,-1):[]}var jM=qe(function(r){var n=mt(r,ic);return n.length&&n[0]===r[0]?Yu(n):[]}),HM=qe(function(r){var n=Ns(r),l=mt(r,ic);return n===Ns(l)?n=s:l.pop(),l.length&&l[0]===r[0]?Yu(l,xe(n,2)):[]}),qM=qe(function(r){var n=Ns(r),l=mt(r,ic);return n=typeof n=="function"?n:s,n&&l.pop(),l.length&&l[0]===r[0]?Yu(l,s,n):[]});function zM(r,n){return r==null?"":$A.call(r,n)}function Ns(r){var n=r==null?0:r.length;return n?r[n-1]:s}function WM(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=d;return l!==s&&(g=Le(l),g=g<0?Mt(d+g,0):Wt(g,d-1)),n===n?xA(r,n,g):fa(r,Gp,g,!0)}function GM(r,n){return r&&r.length?Dm(r,Le(n)):s}var KM=qe(pg);function pg(r,n){return r&&r.length&&n&&n.length?ec(r,n):r}function QM(r,n,l){return r&&r.length&&n&&n.length?ec(r,n,xe(l,2)):r}function YM(r,n,l){return r&&r.length&&n&&n.length?ec(r,n,s,l):r}var ZM=wr(function(r,n){var l=r==null?0:r.length,d=Wu(r,n);return Om(r,mt(n,function(g){return Er(g,l)?+g:g}).sort(Fm)),d});function JM(r,n){var l=[];if(!(r&&r.length))return l;var d=-1,g=[],y=r.length;for(n=xe(n,3);++d<y;){var E=r[d];n(E,d,r)&&(l.push(E),g.push(d))}return Om(r,g),l}function bc(r){return r==null?r:zA.call(r)}function XM(r,n,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&ss(r,n,l)?(n=0,l=d):(n=n==null?0:Le(n),l=l===s?d:Le(l)),Os(r,n,l)):[]}function e2(r,n){return Ma(r,n)}function t2(r,n,l){return rc(r,n,xe(l,2))}function s2(r,n){var l=r==null?0:r.length;if(l){var d=Ma(r,n);if(d<l&&$s(r[d],n))return d}return-1}function r2(r,n){return Ma(r,n,!0)}function o2(r,n,l){return rc(r,n,xe(l,2),!0)}function n2(r,n){var l=r==null?0:r.length;if(l){var d=Ma(r,n,!0)-1;if($s(r[d],n))return d}return-1}function i2(r){return r&&r.length?Im(r):[]}function a2(r,n){return r&&r.length?Im(r,xe(n,2)):[]}function l2(r){var n=r==null?0:r.length;return n?Os(r,1,n):[]}function u2(r,n,l){return r&&r.length?(n=l||n===s?1:Le(n),Os(r,0,n<0?0:n)):[]}function c2(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Le(n),n=d-n,Os(r,n<0?0:n,d)):[]}function d2(r,n){return r&&r.length?Pa(r,xe(n,3),!1,!0):[]}function f2(r,n){return r&&r.length?Pa(r,xe(n,3)):[]}var h2=qe(function(r){return zr(jt(r,1,Et,!0))}),p2=qe(function(r){var n=Ns(r);return Et(n)&&(n=s),zr(jt(r,1,Et,!0),xe(n,2))}),m2=qe(function(r){var n=Ns(r);return n=typeof n=="function"?n:s,zr(jt(r,1,Et,!0),s,n)});function g2(r){return r&&r.length?zr(r):[]}function v2(r,n){return r&&r.length?zr(r,xe(n,2)):[]}function _2(r,n){return n=typeof n=="function"?n:s,r&&r.length?zr(r,s,n):[]}function wc(r){if(!(r&&r.length))return[];var n=0;return r=Br(r,function(l){if(Et(l))return n=Mt(l.length,n),!0}),Uu(n,function(l){return mt(r,Ru(l))})}function mg(r,n){if(!(r&&r.length))return[];var l=wc(r);return n==null?l:mt(l,function(d){return ps(n,s,d)})}var y2=qe(function(r,n){return Et(r)?Zn(r,n):[]}),b2=qe(function(r){return nc(Br(r,Et))}),w2=qe(function(r){var n=Ns(r);return Et(n)&&(n=s),nc(Br(r,Et),xe(n,2))}),E2=qe(function(r){var n=Ns(r);return n=typeof n=="function"?n:s,nc(Br(r,Et),s,n)}),C2=qe(wc);function D2(r,n){return Pm(r||[],n||[],Yn)}function x2(r,n){return Pm(r||[],n||[],ei)}var S2=qe(function(r){var n=r.length,l=n>1?r[n-1]:s;return l=typeof l=="function"?(r.pop(),l):s,mg(r,l)});function gg(r){var n=_(r);return n.__chain__=!0,n}function O2(r,n){return n(r),r}function ja(r,n){return n(r)}var N2=wr(function(r){var n=r.length,l=n?r[0]:0,d=this.__wrapped__,g=function(y){return Wu(y,r)};return n>1||this.__actions__.length||!(d instanceof Qe)||!Er(l)?this.thru(g):(d=d.slice(l,+l+(n?1:0)),d.__actions__.push({func:ja,args:[g],thisArg:s}),new xs(d,this.__chain__).thru(function(y){return n&&!y.length&&y.push(s),y}))});function I2(){return gg(this)}function A2(){return new xs(this.value(),this.__chain__)}function T2(){this.__values__===s&&(this.__values__=Ag(this.value()));var r=this.__index__>=this.__values__.length,n=r?s:this.__values__[this.__index__++];return{done:r,value:n}}function M2(){return this}function P2(r){for(var n,l=this;l instanceof Oa;){var d=ug(l);d.__index__=0,d.__values__=s,n?g.__wrapped__=d:n=d;var g=d;l=l.__wrapped__}return g.__wrapped__=r,n}function k2(){var r=this.__wrapped__;if(r instanceof Qe){var n=r;return this.__actions__.length&&(n=new Qe(this)),n=n.reverse(),n.__actions__.push({func:ja,args:[bc],thisArg:s}),new xs(n,this.__chain__)}return this.thru(bc)}function R2(){return Mm(this.__wrapped__,this.__actions__)}var V2=ka(function(r,n,l){lt.call(r,l)?++r[l]:yr(r,l,1)});function F2(r,n,l){var d=Ve(r)?zp:OT;return l&&ss(r,n,l)&&(n=s),d(r,xe(n,3))}function U2(r,n){var l=Ve(r)?Br:mm;return l(r,xe(n,3))}var L2=Hm(cg),B2=Hm(dg);function $2(r,n){return jt(Ha(r,n),1)}function j2(r,n){return jt(Ha(r,n),Oe)}function H2(r,n,l){return l=l===s?1:Le(l),jt(Ha(r,n),l)}function vg(r,n){var l=Ve(r)?Cs:qr;return l(r,xe(n,3))}function _g(r,n){var l=Ve(r)?lA:pm;return l(r,xe(n,3))}var q2=ka(function(r,n,l){lt.call(r,l)?r[l].push(n):yr(r,l,[n])});function z2(r,n,l,d){r=ns(r)?r:tn(r),l=l&&!d?Le(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),Ka(r)?l<=g&&r.indexOf(n,l)>-1:!!g&&Ho(r,n,l)>-1}var W2=qe(function(r,n,l){var d=-1,g=typeof n=="function",y=ns(r)?M(r.length):[];return qr(r,function(E){y[++d]=g?ps(n,E,l):Jn(E,n,l)}),y}),G2=ka(function(r,n,l){yr(r,l,n)});function Ha(r,n){var l=Ve(r)?mt:wm;return l(r,xe(n,3))}function K2(r,n,l,d){return r==null?[]:(Ve(n)||(n=n==null?[]:[n]),l=d?s:l,Ve(l)||(l=l==null?[]:[l]),xm(r,n,l))}var Q2=ka(function(r,n,l){r[l?0:1].push(n)},function(){return[[],[]]});function Y2(r,n,l){var d=Ve(r)?Pu:Qp,g=arguments.length<3;return d(r,xe(n,4),l,g,qr)}function Z2(r,n,l){var d=Ve(r)?uA:Qp,g=arguments.length<3;return d(r,xe(n,4),l,g,pm)}function J2(r,n){var l=Ve(r)?Br:mm;return l(r,Wa(xe(n,3)))}function X2(r){var n=Ve(r)?cm:zT;return n(r)}function eP(r,n,l){(l?ss(r,n,l):n===s)?n=1:n=Le(n);var d=Ve(r)?ET:WT;return d(r,n)}function tP(r){var n=Ve(r)?CT:KT;return n(r)}function sP(r){if(r==null)return 0;if(ns(r))return Ka(r)?zo(r):r.length;var n=Gt(r);return n==b||n==W?r.size:Ju(r).length}function rP(r,n,l){var d=Ve(r)?ku:QT;return l&&ss(r,n,l)&&(n=s),d(r,xe(n,3))}var oP=qe(function(r,n){if(r==null)return[];var l=n.length;return l>1&&ss(r,n[0],n[1])?n=[]:l>2&&ss(n[0],n[1],n[2])&&(n=[n[0]]),xm(r,jt(n,1),[])}),qa=UA||function(){return $t.Date.now()};function nP(r,n){if(typeof n!="function")throw new Ds(u);return r=Le(r),function(){if(--r<1)return n.apply(this,arguments)}}function yg(r,n,l){return n=l?s:n,n=r&&n==null?r.length:n,br(r,be,s,s,s,s,n)}function bg(r,n){var l;if(typeof n!="function")throw new Ds(u);return r=Le(r),function(){return--r>0&&(l=n.apply(this,arguments)),r<=1&&(n=s),l}}var Ec=qe(function(r,n,l){var d=te;if(l.length){var g=jr(l,Xo(Ec));d|=Y}return br(r,d,n,l,g)}),wg=qe(function(r,n,l){var d=te|T;if(l.length){var g=jr(l,Xo(wg));d|=Y}return br(n,d,r,l,g)});function Eg(r,n,l){n=l?s:n;var d=br(r,Q,s,s,s,s,s,n);return d.placeholder=Eg.placeholder,d}function Cg(r,n,l){n=l?s:n;var d=br(r,we,s,s,s,s,s,n);return d.placeholder=Cg.placeholder,d}function Dg(r,n,l){var d,g,y,E,x,O,L=0,B=!1,H=!1,ue=!0;if(typeof r!="function")throw new Ds(u);n=Is(n)||0,gt(l)&&(B=!!l.leading,H="maxWait"in l,y=H?Mt(Is(l.maxWait)||0,n):y,ue="trailing"in l?!!l.trailing:ue);function ve(Ct){var js=d,xr=g;return d=g=s,L=Ct,E=r.apply(xr,js),E}function Ne(Ct){return L=Ct,x=ri(Ge,n),B?ve(Ct):E}function je(Ct){var js=Ct-O,xr=Ct-L,Hg=n-js;return H?Wt(Hg,y-xr):Hg}function Ie(Ct){var js=Ct-O,xr=Ct-L;return O===s||js>=n||js<0||H&&xr>=y}function Ge(){var Ct=qa();if(Ie(Ct))return Ye(Ct);x=ri(Ge,je(Ct))}function Ye(Ct){return x=s,ue&&d?ve(Ct):(d=g=s,E)}function _s(){x!==s&&km(x),L=0,d=O=g=x=s}function rs(){return x===s?E:Ye(qa())}function ys(){var Ct=qa(),js=Ie(Ct);if(d=arguments,g=this,O=Ct,js){if(x===s)return Ne(O);if(H)return km(x),x=ri(Ge,n),ve(O)}return x===s&&(x=ri(Ge,n)),E}return ys.cancel=_s,ys.flush=rs,ys}var iP=qe(function(r,n){return hm(r,1,n)}),aP=qe(function(r,n,l){return hm(r,Is(n)||0,l)});function lP(r){return br(r,le)}function za(r,n){if(typeof r!="function"||n!=null&&typeof n!="function")throw new Ds(u);var l=function(){var d=arguments,g=n?n.apply(this,d):d[0],y=l.cache;if(y.has(g))return y.get(g);var E=r.apply(this,d);return l.cache=y.set(g,E)||y,E};return l.cache=new(za.Cache||_r),l}za.Cache=_r;function Wa(r){if(typeof r!="function")throw new Ds(u);return function(){var n=arguments;switch(n.length){case 0:return!r.call(this);case 1:return!r.call(this,n[0]);case 2:return!r.call(this,n[0],n[1]);case 3:return!r.call(this,n[0],n[1],n[2])}return!r.apply(this,n)}}function uP(r){return bg(2,r)}var cP=YT(function(r,n){n=n.length==1&&Ve(n[0])?mt(n[0],ms(xe())):mt(jt(n,1),ms(xe()));var l=n.length;return qe(function(d){for(var g=-1,y=Wt(d.length,l);++g<y;)d[g]=n[g].call(this,d[g]);return ps(r,this,d)})}),Cc=qe(function(r,n){var l=jr(n,Xo(Cc));return br(r,Y,s,n,l)}),xg=qe(function(r,n){var l=jr(n,Xo(xg));return br(r,he,s,n,l)}),dP=wr(function(r,n){return br(r,Te,s,s,s,n)});function fP(r,n){if(typeof r!="function")throw new Ds(u);return n=n===s?n:Le(n),qe(r,n)}function hP(r,n){if(typeof r!="function")throw new Ds(u);return n=n==null?0:Mt(Le(n),0),qe(function(l){var d=l[n],g=Gr(l,0,n);return d&&$r(g,d),ps(r,this,g)})}function pP(r,n,l){var d=!0,g=!0;if(typeof r!="function")throw new Ds(u);return gt(l)&&(d="leading"in l?!!l.leading:d,g="trailing"in l?!!l.trailing:g),Dg(r,n,{leading:d,maxWait:n,trailing:g})}function mP(r){return yg(r,1)}function gP(r,n){return Cc(ac(n),r)}function vP(){if(!arguments.length)return[];var r=arguments[0];return Ve(r)?r:[r]}function _P(r){return Ss(r,D)}function yP(r,n){return n=typeof n=="function"?n:s,Ss(r,D,n)}function bP(r){return Ss(r,v|D)}function wP(r,n){return n=typeof n=="function"?n:s,Ss(r,v|D,n)}function EP(r,n){return n==null||fm(r,n,Rt(n))}function $s(r,n){return r===n||r!==r&&n!==n}var CP=Ua(Qu),DP=Ua(function(r,n){return r>=n}),Do=_m(function(){return arguments}())?_m:function(r){return yt(r)&&lt.call(r,"callee")&&!om.call(r,"callee")},Ve=M.isArray,xP=Lp?ms(Lp):PT;function ns(r){return r!=null&&Ga(r.length)&&!Cr(r)}function Et(r){return yt(r)&&ns(r)}function SP(r){return r===!0||r===!1||yt(r)&&ts(r)==ne}var Kr=BA||kc,OP=Bp?ms(Bp):kT;function NP(r){return yt(r)&&r.nodeType===1&&!oi(r)}function IP(r){if(r==null)return!0;if(ns(r)&&(Ve(r)||typeof r=="string"||typeof r.splice=="function"||Kr(r)||en(r)||Do(r)))return!r.length;var n=Gt(r);if(n==b||n==W)return!r.size;if(si(r))return!Ju(r).length;for(var l in r)if(lt.call(r,l))return!1;return!0}function AP(r,n){return Xn(r,n)}function TP(r,n,l){l=typeof l=="function"?l:s;var d=l?l(r,n):s;return d===s?Xn(r,n,s,l):!!d}function Dc(r){if(!yt(r))return!1;var n=ts(r);return n==ke||n==me||typeof r.message=="string"&&typeof r.name=="string"&&!oi(r)}function MP(r){return typeof r=="number"&&im(r)}function Cr(r){if(!gt(r))return!1;var n=ts(r);return n==at||n==Re||n==R||n==q}function Sg(r){return typeof r=="number"&&r==Le(r)}function Ga(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Ee}function gt(r){var n=typeof r;return r!=null&&(n=="object"||n=="function")}function yt(r){return r!=null&&typeof r=="object"}var Og=$p?ms($p):VT;function PP(r,n){return r===n||Zu(r,n,pc(n))}function kP(r,n,l){return l=typeof l=="function"?l:s,Zu(r,n,pc(n),l)}function RP(r){return Ng(r)&&r!=+r}function VP(r){if(yM(r))throw new Me(a);return ym(r)}function FP(r){return r===null}function UP(r){return r==null}function Ng(r){return typeof r=="number"||yt(r)&&ts(r)==C}function oi(r){if(!yt(r)||ts(r)!=F)return!1;var n=ba(r);if(n===null)return!0;var l=lt.call(n,"constructor")&&n.constructor;return typeof l=="function"&&l instanceof l&&ga.call(l)==kA}var xc=jp?ms(jp):FT;function LP(r){return Sg(r)&&r>=-Ee&&r<=Ee}var Ig=Hp?ms(Hp):UT;function Ka(r){return typeof r=="string"||!Ve(r)&&yt(r)&&ts(r)==Z}function vs(r){return typeof r=="symbol"||yt(r)&&ts(r)==z}var en=qp?ms(qp):LT;function BP(r){return r===s}function $P(r){return yt(r)&&Gt(r)==se}function jP(r){return yt(r)&&ts(r)==_e}var HP=Ua(Xu),qP=Ua(function(r,n){return r<=n});function Ag(r){if(!r)return[];if(ns(r))return Ka(r)?Ls(r):os(r);if(zn&&r[zn])return EA(r[zn]());var n=Gt(r),l=n==b?Bu:n==W?ha:tn;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=Is(r),r===Oe||r===-Oe){var n=r<0?-1:1;return n*Ut}return r===r?r:0}function Le(r){var n=Dr(r),l=n%1;return n===n?l?n-l:n:0}function Tg(r){return r?bo(Le(r),0,_t):0}function Is(r){if(typeof r=="number")return r;if(vs(r))return Xt;if(gt(r)){var n=typeof r.valueOf=="function"?r.valueOf():r;r=gt(n)?n+"":n}if(typeof r!="string")return r===0?r:+r;r=Yp(r);var l=xI.test(r);return l||OI.test(r)?nA(r.slice(2),l?2:8):DI.test(r)?Xt:+r}function Mg(r){return Js(r,is(r))}function zP(r){return r?bo(Le(r),-Ee,Ee):r===0?r:0}function it(r){return r==null?"":gs(r)}var WP=Zo(function(r,n){if(si(n)||ns(n)){Js(n,Rt(n),r);return}for(var l in n)lt.call(n,l)&&Yn(r,l,n[l])}),Pg=Zo(function(r,n){Js(n,is(n),r)}),Qa=Zo(function(r,n,l,d){Js(n,is(n),r,d)}),GP=Zo(function(r,n,l,d){Js(n,Rt(n),r,d)}),KP=wr(Wu);function QP(r,n){var l=Yo(r);return n==null?l:dm(l,n)}var YP=qe(function(r,n){r=ct(r);var l=-1,d=n.length,g=d>2?n[2]:s;for(g&&ss(n[0],n[1],g)&&(d=1);++l<d;)for(var y=n[l],E=is(y),x=-1,O=E.length;++x<O;){var L=E[x],B=r[L];(B===s||$s(B,Go[L])&&!lt.call(r,L))&&(r[L]=y[L])}return r}),ZP=qe(function(r){return r.push(s,Ym),ps(kg,s,r)});function JP(r,n){return Wp(r,xe(n,3),Zs)}function XP(r,n){return Wp(r,xe(n,3),Ku)}function ek(r,n){return r==null?r:Gu(r,xe(n,3),is)}function tk(r,n){return r==null?r:gm(r,xe(n,3),is)}function sk(r,n){return r&&Zs(r,xe(n,3))}function rk(r,n){return r&&Ku(r,xe(n,3))}function ok(r){return r==null?[]:Aa(r,Rt(r))}function nk(r){return r==null?[]:Aa(r,is(r))}function Sc(r,n,l){var d=r==null?s:wo(r,n);return d===s?l:d}function ik(r,n){return r!=null&&Xm(r,n,IT)}function Oc(r,n){return r!=null&&Xm(r,n,AT)}var ak=zm(function(r,n,l){n!=null&&typeof n.toString!="function"&&(n=va.call(n)),r[n]=l},Ic(as)),lk=zm(function(r,n,l){n!=null&&typeof n.toString!="function"&&(n=va.call(n)),lt.call(r,n)?r[n].push(l):r[n]=[l]},xe),uk=qe(Jn);function Rt(r){return ns(r)?um(r):Ju(r)}function is(r){return ns(r)?um(r,!0):BT(r)}function ck(r,n){var l={};return n=xe(n,3),Zs(r,function(d,g,y){yr(l,n(d,g,y),d)}),l}function dk(r,n){var l={};return n=xe(n,3),Zs(r,function(d,g,y){yr(l,g,n(d,g,y))}),l}var fk=Zo(function(r,n,l){Ta(r,n,l)}),kg=Zo(function(r,n,l,d){Ta(r,n,l,d)}),hk=wr(function(r,n){var l={};if(r==null)return l;var d=!1;n=mt(n,function(y){return y=Wr(y,r),d||(d=y.length>1),y}),Js(r,fc(r),l),d&&(l=Ss(l,v|w|D,aM));for(var g=n.length;g--;)oc(l,n[g]);return l});function pk(r,n){return Rg(r,Wa(xe(n)))}var mk=wr(function(r,n){return r==null?{}:jT(r,n)});function Rg(r,n){if(r==null)return{};var l=mt(fc(r),function(d){return[d]});return n=xe(n),Sm(r,l,function(d,g){return n(d,g[0])})}function gk(r,n,l){n=Wr(n,r);var d=-1,g=n.length;for(g||(g=1,r=s);++d<g;){var y=r==null?s:r[Xs(n[d])];y===s&&(d=g,y=l),r=Cr(y)?y.call(r):y}return r}function vk(r,n,l){return r==null?r:ei(r,n,l)}function _k(r,n,l,d){return d=typeof d=="function"?d:s,r==null?r:ei(r,n,l,d)}var Vg=Km(Rt),Fg=Km(is);function yk(r,n,l){var d=Ve(r),g=d||Kr(r)||en(r);if(n=xe(n,4),l==null){var y=r&&r.constructor;g?l=d?new y:[]:gt(r)?l=Cr(y)?Yo(ba(r)):{}:l={}}return(g?Cs:Zs)(r,function(E,x,O){return n(l,E,x,O)}),l}function bk(r,n){return r==null?!0:oc(r,n)}function wk(r,n,l){return r==null?r:Tm(r,n,ac(l))}function Ek(r,n,l,d){return d=typeof d=="function"?d:s,r==null?r:Tm(r,n,ac(l),d)}function tn(r){return r==null?[]:Lu(r,Rt(r))}function Ck(r){return r==null?[]:Lu(r,is(r))}function Dk(r,n,l){return l===s&&(l=n,n=s),l!==s&&(l=Is(l),l=l===l?l:0),n!==s&&(n=Is(n),n=n===n?n:0),bo(Is(r),n,l)}function xk(r,n,l){return n=Dr(n),l===s?(l=n,n=0):l=Dr(l),r=Is(r),TT(r,n,l)}function Sk(r,n,l){if(l&&typeof l!="boolean"&&ss(r,n,l)&&(n=l=s),l===s&&(typeof n=="boolean"?(l=n,n=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&n===s?(r=0,n=1):(r=Dr(r),n===s?(n=r,r=0):n=Dr(n)),r>n){var d=r;r=n,n=d}if(l||r%1||n%1){var g=am();return Wt(r+g*(n-r+oA("1e-"+((g+"").length-1))),n)}return tc(r,n)}var Ok=Jo(function(r,n,l){return n=n.toLowerCase(),r+(l?Ug(n):n)});function Ug(r){return Nc(it(r).toLowerCase())}function Lg(r){return r=it(r),r&&r.replace(II,vA).replace(KI,"")}function Nk(r,n,l){r=it(r),n=gs(n);var d=r.length;l=l===s?d:bo(Le(l),0,d);var g=l;return l-=n.length,l>=0&&r.slice(l,g)==n}function Ik(r){return r=it(r),r&&uI.test(r)?r.replace(gp,_A):r}function Ak(r){return r=it(r),r&&mI.test(r)?r.replace(Cu,"\\$&"):r}var Tk=Jo(function(r,n,l){return r+(l?"-":"")+n.toLowerCase()}),Mk=Jo(function(r,n,l){return r+(l?" ":"")+n.toLowerCase()}),Pk=jm("toLowerCase");function kk(r,n,l){r=it(r),n=Le(n);var d=n?zo(r):0;if(!n||d>=n)return r;var g=(n-d)/2;return Fa(Da(g),l)+r+Fa(Ca(g),l)}function Rk(r,n,l){r=it(r),n=Le(n);var d=n?zo(r):0;return n&&d<n?r+Fa(n-d,l):r}function Vk(r,n,l){r=it(r),n=Le(n);var d=n?zo(r):0;return n&&d<n?Fa(n-d,l)+r:r}function Fk(r,n,l){return l||n==null?n=0:n&&(n=+n),qA(it(r).replace(Du,""),n||0)}function Uk(r,n,l){return(l?ss(r,n,l):n===s)?n=1:n=Le(n),sc(it(r),n)}function Lk(){var r=arguments,n=it(r[0]);return r.length<3?n:n.replace(r[1],r[2])}var Bk=Jo(function(r,n,l){return r+(l?"_":"")+n.toLowerCase()});function $k(r,n,l){return l&&typeof l!="number"&&ss(r,n,l)&&(n=l=s),l=l===s?_t:l>>>0,l?(r=it(r),r&&(typeof n=="string"||n!=null&&!xc(n))&&(n=gs(n),!n&&qo(r))?Gr(Ls(r),0,l):r.split(n,l)):[]}var jk=Jo(function(r,n,l){return r+(l?" ":"")+Nc(n)});function Hk(r,n,l){return r=it(r),l=l==null?0:bo(Le(l),0,r.length),n=gs(n),r.slice(l,l+n.length)==n}function qk(r,n,l){var d=_.templateSettings;l&&ss(r,n,l)&&(n=s),r=it(r),n=Qa({},n,d,Qm);var g=Qa({},n.imports,d.imports,Qm),y=Rt(g),E=Lu(g,y),x,O,L=0,B=n.interpolate||la,H="__p += '",ue=$u((n.escape||la).source+"|"+B.source+"|"+(B===vp?CI:la).source+"|"+(n.evaluate||la).source+"|$","g"),ve="//# sourceURL="+(lt.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++XI+"]")+`
`;r.replace(ue,function(Ie,Ge,Ye,_s,rs,ys){return Ye||(Ye=_s),H+=r.slice(L,ys).replace(AI,yA),Ge&&(x=!0,H+=`' +
__e(`+Ge+`) +
'`),rs&&(O=!0,H+=`';
`+rs+`;
__p += '`),Ye&&(H+=`' +
((__t = (`+Ye+`)) == null ? '' : __t) +
'`),L=ys+Ie.length,Ie}),H+=`';
`;var Ne=lt.call(n,"variable")&&n.variable;if(!Ne)H=`with (obj) {
`+H+`
}
`;else if(wI.test(Ne))throw new Me(c);H=(O?H.replace(ws,""):H).replace(aa,"$1").replace(aI,"$1;"),H="function("+(Ne||"obj")+`) {
`+(Ne?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(O?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+H+`return __p
}`;var je=$g(function(){return st(y,ve+"return "+H).apply(s,E)});if(je.source=H,Dc(je))throw je;return je}function zk(r){return it(r).toLowerCase()}function Wk(r){return it(r).toUpperCase()}function Gk(r,n,l){if(r=it(r),r&&(l||n===s))return Yp(r);if(!r||!(n=gs(n)))return r;var d=Ls(r),g=Ls(n),y=Zp(d,g),E=Jp(d,g)+1;return Gr(d,y,E).join("")}function Kk(r,n,l){if(r=it(r),r&&(l||n===s))return r.slice(0,em(r)+1);if(!r||!(n=gs(n)))return r;var d=Ls(r),g=Jp(d,Ls(n))+1;return Gr(d,0,g).join("")}function Qk(r,n,l){if(r=it(r),r&&(l||n===s))return r.replace(Du,"");if(!r||!(n=gs(n)))return r;var d=Ls(r),g=Zp(d,Ls(n));return Gr(d,g).join("")}function Yk(r,n){var l=ie,d=Pe;if(gt(n)){var g="separator"in n?n.separator:g;l="length"in n?Le(n.length):l,d="omission"in n?gs(n.omission):d}r=it(r);var y=r.length;if(qo(r)){var E=Ls(r);y=E.length}if(l>=y)return r;var x=l-zo(d);if(x<1)return d;var O=E?Gr(E,0,x).join(""):r.slice(0,x);if(g===s)return O+d;if(E&&(x+=O.length-x),xc(g)){if(r.slice(x).search(g)){var L,B=O;for(g.global||(g=$u(g.source,it(_p.exec(g))+"g")),g.lastIndex=0;L=g.exec(B);)var H=L.index;O=O.slice(0,H===s?x:H)}}else if(r.indexOf(gs(g),x)!=x){var ue=O.lastIndexOf(g);ue>-1&&(O=O.slice(0,ue))}return O+d}function Zk(r){return r=it(r),r&&lI.test(r)?r.replace(mp,SA):r}var Jk=Jo(function(r,n,l){return r+(l?" ":"")+n.toUpperCase()}),Nc=jm("toUpperCase");function Bg(r,n,l){return r=it(r),n=l?s:n,n===s?wA(r)?IA(r):fA(r):r.match(n)||[]}var $g=qe(function(r,n){try{return ps(r,s,n)}catch(l){return Dc(l)?l:new Me(l)}}),Xk=wr(function(r,n){return Cs(n,function(l){l=Xs(l),yr(r,l,Ec(r[l],r))}),r});function eR(r){var n=r==null?0:r.length,l=xe();return r=n?mt(r,function(d){if(typeof d[1]!="function")throw new Ds(u);return[l(d[0]),d[1]]}):[],qe(function(d){for(var g=-1;++g<n;){var y=r[g];if(ps(y[0],this,d))return ps(y[1],this,d)}})}function tR(r){return ST(Ss(r,v))}function Ic(r){return function(){return r}}function sR(r,n){return r==null||r!==r?n:r}var rR=qm(),oR=qm(!0);function as(r){return r}function Ac(r){return bm(typeof r=="function"?r:Ss(r,v))}function nR(r){return Em(Ss(r,v))}function iR(r,n){return Cm(r,Ss(n,v))}var aR=qe(function(r,n){return function(l){return Jn(l,r,n)}}),lR=qe(function(r,n){return function(l){return Jn(r,l,n)}});function Tc(r,n,l){var d=Rt(n),g=Aa(n,d);l==null&&!(gt(n)&&(g.length||!d.length))&&(l=n,n=r,r=this,g=Aa(n,Rt(n)));var y=!(gt(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Cs(g,function(x){var O=n[x];r[x]=O,E&&(r.prototype[x]=function(){var L=this.__chain__;if(y||L){var B=r(this.__wrapped__),H=B.__actions__=os(this.__actions__);return H.push({func:O,args:arguments,thisArg:r}),B.__chain__=L,B}return O.apply(r,$r([this.value()],arguments))})}),r}function uR(){return $t._===this&&($t._=RA),this}function Mc(){}function cR(r){return r=Le(r),qe(function(n){return Dm(n,r)})}var dR=uc(mt),fR=uc(zp),hR=uc(ku);function jg(r){return gc(r)?Ru(Xs(r)):HT(r)}function pR(r){return function(n){return r==null?s:wo(r,n)}}var mR=Wm(),gR=Wm(!0);function Pc(){return[]}function kc(){return!1}function vR(){return{}}function _R(){return""}function yR(){return!0}function bR(r,n){if(r=Le(r),r<1||r>Ee)return[];var l=_t,d=Wt(r,_t);n=xe(n),r-=_t;for(var g=Uu(d,n);++l<r;)n(l);return g}function wR(r){return Ve(r)?mt(r,Xs):vs(r)?[r]:os(lg(it(r)))}function ER(r){var n=++PA;return it(r)+n}var CR=Va(function(r,n){return r+n},0),DR=cc("ceil"),xR=Va(function(r,n){return r/n},1),SR=cc("floor");function OR(r){return r&&r.length?Ia(r,as,Qu):s}function NR(r,n){return r&&r.length?Ia(r,xe(n,2),Qu):s}function IR(r){return Kp(r,as)}function AR(r,n){return Kp(r,xe(n,2))}function TR(r){return r&&r.length?Ia(r,as,Xu):s}function MR(r,n){return r&&r.length?Ia(r,xe(n,2),Xu):s}var PR=Va(function(r,n){return r*n},1),kR=cc("round"),RR=Va(function(r,n){return r-n},0);function VR(r){return r&&r.length?Fu(r,as):0}function FR(r,n){return r&&r.length?Fu(r,xe(n,2)):0}return _.after=nP,_.ary=yg,_.assign=WP,_.assignIn=Pg,_.assignInWith=Qa,_.assignWith=GP,_.at=KP,_.before=bg,_.bind=Ec,_.bindAll=Xk,_.bindKey=wg,_.castArray=vP,_.chain=gg,_.chunk=SM,_.compact=OM,_.concat=NM,_.cond=eR,_.conforms=tR,_.constant=Ic,_.countBy=V2,_.create=QP,_.curry=Eg,_.curryRight=Cg,_.debounce=Dg,_.defaults=YP,_.defaultsDeep=ZP,_.defer=iP,_.delay=aP,_.difference=IM,_.differenceBy=AM,_.differenceWith=TM,_.drop=MM,_.dropRight=PM,_.dropRightWhile=kM,_.dropWhile=RM,_.fill=VM,_.filter=U2,_.flatMap=$2,_.flatMapDeep=j2,_.flatMapDepth=H2,_.flatten=fg,_.flattenDeep=FM,_.flattenDepth=UM,_.flip=lP,_.flow=rR,_.flowRight=oR,_.fromPairs=LM,_.functions=ok,_.functionsIn=nk,_.groupBy=q2,_.initial=$M,_.intersection=jM,_.intersectionBy=HM,_.intersectionWith=qM,_.invert=ak,_.invertBy=lk,_.invokeMap=W2,_.iteratee=Ac,_.keyBy=G2,_.keys=Rt,_.keysIn=is,_.map=Ha,_.mapKeys=ck,_.mapValues=dk,_.matches=nR,_.matchesProperty=iR,_.memoize=za,_.merge=fk,_.mergeWith=kg,_.method=aR,_.methodOf=lR,_.mixin=Tc,_.negate=Wa,_.nthArg=cR,_.omit=hk,_.omitBy=pk,_.once=uP,_.orderBy=K2,_.over=dR,_.overArgs=cP,_.overEvery=fR,_.overSome=hR,_.partial=Cc,_.partialRight=xg,_.partition=Q2,_.pick=mk,_.pickBy=Rg,_.property=jg,_.propertyOf=pR,_.pull=KM,_.pullAll=pg,_.pullAllBy=QM,_.pullAllWith=YM,_.pullAt=ZM,_.range=mR,_.rangeRight=gR,_.rearg=dP,_.reject=J2,_.remove=JM,_.rest=fP,_.reverse=bc,_.sampleSize=eP,_.set=vk,_.setWith=_k,_.shuffle=tP,_.slice=XM,_.sortBy=oP,_.sortedUniq=i2,_.sortedUniqBy=a2,_.split=$k,_.spread=hP,_.tail=l2,_.take=u2,_.takeRight=c2,_.takeRightWhile=d2,_.takeWhile=f2,_.tap=O2,_.throttle=pP,_.thru=ja,_.toArray=Ag,_.toPairs=Vg,_.toPairsIn=Fg,_.toPath=wR,_.toPlainObject=Mg,_.transform=yk,_.unary=mP,_.union=h2,_.unionBy=p2,_.unionWith=m2,_.uniq=g2,_.uniqBy=v2,_.uniqWith=_2,_.unset=bk,_.unzip=wc,_.unzipWith=mg,_.update=wk,_.updateWith=Ek,_.values=tn,_.valuesIn=Ck,_.without=y2,_.words=Bg,_.wrap=gP,_.xor=b2,_.xorBy=w2,_.xorWith=E2,_.zip=C2,_.zipObject=D2,_.zipObjectDeep=x2,_.zipWith=S2,_.entries=Vg,_.entriesIn=Fg,_.extend=Pg,_.extendWith=Qa,Tc(_,_),_.add=CR,_.attempt=$g,_.camelCase=Ok,_.capitalize=Ug,_.ceil=DR,_.clamp=Dk,_.clone=_P,_.cloneDeep=bP,_.cloneDeepWith=wP,_.cloneWith=yP,_.conformsTo=EP,_.deburr=Lg,_.defaultTo=sR,_.divide=xR,_.endsWith=Nk,_.eq=$s,_.escape=Ik,_.escapeRegExp=Ak,_.every=F2,_.find=L2,_.findIndex=cg,_.findKey=JP,_.findLast=B2,_.findLastIndex=dg,_.findLastKey=XP,_.floor=SR,_.forEach=vg,_.forEachRight=_g,_.forIn=ek,_.forInRight=tk,_.forOwn=sk,_.forOwnRight=rk,_.get=Sc,_.gt=CP,_.gte=DP,_.has=ik,_.hasIn=Oc,_.head=hg,_.identity=as,_.includes=z2,_.indexOf=BM,_.inRange=xk,_.invoke=uk,_.isArguments=Do,_.isArray=Ve,_.isArrayBuffer=xP,_.isArrayLike=ns,_.isArrayLikeObject=Et,_.isBoolean=SP,_.isBuffer=Kr,_.isDate=OP,_.isElement=NP,_.isEmpty=IP,_.isEqual=AP,_.isEqualWith=TP,_.isError=Dc,_.isFinite=MP,_.isFunction=Cr,_.isInteger=Sg,_.isLength=Ga,_.isMap=Og,_.isMatch=PP,_.isMatchWith=kP,_.isNaN=RP,_.isNative=VP,_.isNil=UP,_.isNull=FP,_.isNumber=Ng,_.isObject=gt,_.isObjectLike=yt,_.isPlainObject=oi,_.isRegExp=xc,_.isSafeInteger=LP,_.isSet=Ig,_.isString=Ka,_.isSymbol=vs,_.isTypedArray=en,_.isUndefined=BP,_.isWeakMap=$P,_.isWeakSet=jP,_.join=zM,_.kebabCase=Tk,_.last=Ns,_.lastIndexOf=WM,_.lowerCase=Mk,_.lowerFirst=Pk,_.lt=HP,_.lte=qP,_.max=OR,_.maxBy=NR,_.mean=IR,_.meanBy=AR,_.min=TR,_.minBy=MR,_.stubArray=Pc,_.stubFalse=kc,_.stubObject=vR,_.stubString=_R,_.stubTrue=yR,_.multiply=PR,_.nth=GM,_.noConflict=uR,_.noop=Mc,_.now=qa,_.pad=kk,_.padEnd=Rk,_.padStart=Vk,_.parseInt=Fk,_.random=Sk,_.reduce=Y2,_.reduceRight=Z2,_.repeat=Uk,_.replace=Lk,_.result=gk,_.round=kR,_.runInContext=S,_.sample=X2,_.size=sP,_.snakeCase=Bk,_.some=rP,_.sortedIndex=e2,_.sortedIndexBy=t2,_.sortedIndexOf=s2,_.sortedLastIndex=r2,_.sortedLastIndexBy=o2,_.sortedLastIndexOf=n2,_.startCase=jk,_.startsWith=Hk,_.subtract=RR,_.sum=VR,_.sumBy=FR,_.template=qk,_.times=bR,_.toFinite=Dr,_.toInteger=Le,_.toLength=Tg,_.toLower=zk,_.toNumber=Is,_.toSafeInteger=zP,_.toString=it,_.toUpper=Wk,_.trim=Gk,_.trimEnd=Kk,_.trimStart=Qk,_.truncate=Yk,_.unescape=Zk,_.uniqueId=ER,_.upperCase=Jk,_.upperFirst=Nc,_.each=vg,_.eachRight=_g,_.first=hg,Tc(_,function(){var r={};return Zs(_,function(n,l){lt.call(_.prototype,l)||(r[l]=n)}),r}(),{chain:!1}),_.VERSION=i,Cs(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){_[r].placeholder=_}),Cs(["drop","take"],function(r,n){Qe.prototype[r]=function(l){l=l===s?1:Mt(Le(l),0);var d=this.__filtered__&&!n?new Qe(this):this.clone();return d.__filtered__?d.__takeCount__=Wt(l,d.__takeCount__):d.__views__.push({size:Wt(l,_t),type:r+(d.__dir__<0?"Right":"")}),d},Qe.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Cs(["filter","map","takeWhile"],function(r,n){var l=n+1,d=l==J||l==ut;Qe.prototype[r]=function(g){var y=this.clone();return y.__iteratees__.push({iteratee:xe(g,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Cs(["head","last"],function(r,n){var l="take"+(n?"Right":"");Qe.prototype[r]=function(){return this[l](1).value()[0]}}),Cs(["initial","tail"],function(r,n){var l="drop"+(n?"":"Right");Qe.prototype[r]=function(){return this.__filtered__?new Qe(this):this[l](1)}}),Qe.prototype.compact=function(){return this.filter(as)},Qe.prototype.find=function(r){return this.filter(r).head()},Qe.prototype.findLast=function(r){return this.reverse().find(r)},Qe.prototype.invokeMap=qe(function(r,n){return typeof r=="function"?new Qe(this):this.map(function(l){return Jn(l,r,n)})}),Qe.prototype.reject=function(r){return this.filter(Wa(xe(r)))},Qe.prototype.slice=function(r,n){r=Le(r);var l=this;return l.__filtered__&&(r>0||n<0)?new Qe(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),n!==s&&(n=Le(n),l=n<0?l.dropRight(-n):l.take(n-r)),l)},Qe.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Qe.prototype.toArray=function(){return this.take(_t)},Zs(Qe.prototype,function(r,n){var l=/^(?:filter|find|map|reject)|While$/.test(n),d=/^(?:head|last)$/.test(n),g=_[d?"take"+(n=="last"?"Right":""):n],y=d||/^find/.test(n);g&&(_.prototype[n]=function(){var E=this.__wrapped__,x=d?[1]:arguments,O=E instanceof Qe,L=x[0],B=O||Ve(E),H=function(Ge){var Ye=g.apply(_,$r([Ge],x));return d&&ue?Ye[0]:Ye};B&&l&&typeof L=="function"&&L.length!=1&&(O=B=!1);var ue=this.__chain__,ve=!!this.__actions__.length,Ne=y&&!ue,je=O&&!ve;if(!y&&B){E=je?E:new Qe(this);var Ie=r.apply(E,x);return Ie.__actions__.push({func:ja,args:[H],thisArg:s}),new xs(Ie,ue)}return Ne&&je?r.apply(this,x):(Ie=this.thru(H),Ne?d?Ie.value()[0]:Ie.value():Ie)})}),Cs(["pop","push","shift","sort","splice","unshift"],function(r){var n=pa[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);_.prototype[r]=function(){var g=arguments;if(d&&!this.__chain__){var y=this.value();return n.apply(Ve(y)?y:[],g)}return this[l](function(E){return n.apply(Ve(E)?E:[],g)})}}),Zs(Qe.prototype,function(r,n){var l=_[n];if(l){var d=l.name+"";lt.call(Qo,d)||(Qo[d]=[]),Qo[d].push({name:n,func:l})}}),Qo[Ra(s,T).name]=[{name:"wrapper",func:s}],Qe.prototype.clone=ZA,Qe.prototype.reverse=JA,Qe.prototype.value=XA,_.prototype.at=N2,_.prototype.chain=I2,_.prototype.commit=A2,_.prototype.next=T2,_.prototype.plant=P2,_.prototype.reverse=k2,_.prototype.toJSON=_.prototype.valueOf=_.prototype.value=R2,_.prototype.first=_.prototype.head,zn&&(_.prototype[zn]=M2),_},Wo=AA();go?((go.exports=Wo)._=Wo,Au._=Wo):$t._=Wo}).call(Fn)}(Xi,Xi.exports);var sp=Xi.exports;const Be=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,o=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=o.clone();try{return(await o.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function e1(e={}){try{return await Be("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function rp(e){try{return await Be("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function op(e){try{return await Be("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function t1(e){try{return await Be("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function pu(){try{return await Be("local_offermanager_get_type_options",{})}catch(e){throw new Error(e.message||"Erro ao buscar opções de tipos")}}async function s1(e,t){try{return await Be("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function r1(e,t){try{return await Be("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function o1(e,t,s){try{return await Be("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function np(e){var t;try{const s=await Be("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(o=>o.name.toLowerCase().includes(e.toLowerCase())).map(o=>({id:o.id,name:o.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function n1(e,t){try{return await Be("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function i1(e,t){try{return await Be("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Un(e="",t=0){try{return await Be("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function ip(e,t,s="",i=1,o=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${o}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),f=parseInt(o,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(f))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:f,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Be("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function ap(e,t=""){try{return await Be("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function mu(e,t){try{return await Be("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function a1(e,t){try{return await Be("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function ea(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Be("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function l1(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}}),"enrol_type"in s&&(delete s.enrol_type,console.log("Campo enrol_type removido do objeto enviado para a API"));const o=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(o.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",o),new Error(`Campos obrigatórios ausentes: ${o.join(", ")}`);return await Be("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function Lr(e){try{return await Be("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function u1(e){try{const t=await Be("local_offermanager_get_course",{offercourseid:e});return t.error?(console.log(t.exception.message),[]):t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function gu(e){try{const t=await Be("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function c1(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(o=>{if(o in e.optional_fields){const a=e.optional_fields[o];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(o)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[o]=a):typeof a=="boolean"?s.optional_fields[o]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[o]=a):a!=null&&a!==""&&(s.optional_fields[o]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol_type"in s&&(delete s.enrol_type,console.log("Campo enrol_type removido do objeto enviado para a API de atualização")),"enrol"in s&&(delete s.enrol,console.log("Campo enrol removido do objeto enviado para a API de atualização")),await Be("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function d1(e){try{return await Be("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function lp(e,t=0){try{return await Be("local_offermanager_get_potential_teachers",{offercourseid:e,offerclassid:t})}catch(s){throw new Error(s.message||"Erro ao buscar professores")}}async function f1(){try{return await Be("local_offermanager_get_situation_list",{})}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function h1(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Be("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function p1(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Be("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Be("local_offermanager_get_class",{id:parseInt(e,10)});let i,o;if(s&&s.data)i=s.data.offerid,o=s.data.offercourseid;else if(s)i=s.offerid,o=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Be("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=o).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function ta(e){try{return await Be("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function up(e=!0){try{return await Be("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function m1(e,t){try{return await Be("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const jR="",g1={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},v1={class:"table-responsive"},_1={class:"table"},y1=["data-value"],b1=["onClick"],w1=["data-column"];function E1(e,t,s,i,o,a){return N(),I("div",v1,[h("table",_1,[h("thead",null,[h("tr",null,[(N(!0),I(Fe,null,vt(s.headers,u=>(N(),I("th",{key:u.value,class:pe({"text-right":u.align==="right"}),style:ls(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Vt(e.$slots,"header-select",{key:0},()=>[nt(G(u.text),1)],!0):(N(),I(Fe,{key:1},[nt(G(u.text)+" ",1),u.sortable?(N(),I("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[h("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,b1)):ce("",!0)],64))],14,y1))),128))])]),h("tbody",null,[(N(!0),I(Fe,null,vt(s.items,u=>(N(),I("tr",{key:u.id},[(N(!0),I(Fe,null,vt(s.headers,c=>(N(),I("td",{key:c.value,class:pe({"text-right":c.align==="right"}),"data-column":c.value},[Vt(e.$slots,"item-"+c.value,{item:u},()=>[nt(G(u[c.value]),1)],!0)],10,w1))),128))]))),128))])])])}const po=ze(g1,[["render",E1],["__scopeId","data-v-c46cd2d8"]]),HR="",C1={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},D1={class:"select-wrapper"},x1=["value","disabled"],S1=["value"],O1={key:1,class:"error-message"};function N1(e,t,s,i,o,a){return N(),I("div",{ref:"selectContainer",class:"custom-select-container",style:ls(a.customWidth)},[s.label?(N(),I("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},G(s.label),3)):ce("",!0),h("div",D1,[h("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(N(!0),I(Fe,null,vt(s.options,u=>(N(),I("option",{key:u.value,value:u.value},G(u.label),9,S1))),128))],42,x1),h("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(N(),I("div",O1,G(s.errorMessage),1)):ce("",!0)],4)}const mr=ze(C1,[["render",N1],["__scopeId","data-v-bbc06e80"]]),qR="",I1={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},A1={key:0,class:"input-label"},T1=["type","placeholder","value","disabled","min","max"],M1={key:0,class:"search-icon"},P1={key:1,class:"error-message"};function k1(e,t,s,i,o,a){return N(),I("div",{class:"custom-input-container",style:ls(a.customWidth)},[s.label?(N(),I("div",A1,G(s.label),1)):ce("",!0),h("div",{class:pe(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[h("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:pe(["form-control",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,T1),s.hasSearchIcon?(N(),I("div",M1,t[2]||(t[2]=[h("i",{class:"fas fa-search"},null,-1)]))):ce("",!0),a.isDateType?(N(),I("div",{key:1,class:pe(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[h("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):ce("",!0)],2),s.hasError&&s.errorMessage?(N(),I("div",P1,G(s.errorMessage),1)):ce("",!0)],4)}const Ln=ze(I1,[["render",k1],["__scopeId","data-v-d5ce1e51"]]),zR="",R1={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},V1=["id","checked","disabled"],F1=["for"];function U1(e,t,s,i,o,a){return N(),I("div",{class:pe(["checkbox-container",{disabled:s.disabled}])},[h("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,V1),h("label",{for:s.id,class:pe(["checkbox-label",{disabled:s.disabled}])},[Vt(e.$slots,"default",{},()=>[nt(G(s.label),1)],!0)],10,F1)],2)}const sa=ze(R1,[["render",U1],["__scopeId","data-v-727d967e"]]),WR="",L1={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},B1=["disabled"],$1={key:1};function j1(e,t,s,i,o,a){return N(),I("button",{class:pe(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(N(),I("i",{key:0,class:pe(s.icon)},null,2)):ce("",!0),s.label?(N(),I("span",$1,G(s.label),1)):ce("",!0),Vt(e.$slots,"default",{},void 0,!0)],10,B1)}const Bo=ze(L1,[["render",j1],["__scopeId","data-v-36572ff9"]]),GR="",H1={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},q1={class:"filter-section"},z1={key:0},W1={class:"filter-content"},G1={key:1,class:"filter-tags"};function K1(e,t,s,i,o,a){return N(),I("div",q1,[s.title?(N(),I("h2",z1,G(s.title),1)):ce("",!0),h("div",W1,[Vt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(N(),I("div",G1,[Vt(e.$slots,"tags",{},void 0,!0)])):ce("",!0)])}const cp=ze(H1,[["render",K1],["__scopeId","data-v-ef6fc6cc"]]),KR="",Q1={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Y1(e,t,s,i,o,a){return N(),I("div",{class:pe(["filter-row",{"filter-row-inline":s.inline}])},[Vt(e.$slots,"default",{},void 0,!0)],2)}const ra=ze(Q1,[["render",Y1],["__scopeId","data-v-83bdb425"]]),QR="",Z1={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},J1={key:0,class:"filter-label"},X1={class:"filter-input"};function ew(e,t,s,i,o,a){return N(),I("div",{class:pe(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(N(),I("div",J1,G(s.label),1)):ce("",!0),h("div",X1,[Vt(e.$slots,"default",{},void 0,!0)])],2)}const oa=ze(Z1,[["render",ew],["__scopeId","data-v-5767b2e9"]]),YR="",tw={name:"FilterActions"},sw={class:"filter-actions"};function rw(e,t,s,i,o,a){return N(),I("div",sw,[Vt(e.$slots,"default",{},void 0,!0)])}const dp=ze(tw,[["render",rw],["__scopeId","data-v-68346c90"]]),ZR="",ow={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},nw={key:0};function iw(e,t,s,i,o,a){return N(),Pt(Lf,null,{default:ye(()=>[s.isLoading?(N(),I("div",nw,t[0]||(t[0]=[h("div",{class:"modal-overlay"},null,-1),h("div",{class:"loader-wrapper"},[h("span",{class:"loader",role:"status"},[h("span",{class:"sr-only"},"Carregando...")])],-1)]))):ce("",!0)]),_:1})}const vu=ze(ow,[["render",iw],["__scopeId","data-v-b3cb5b4c"]]),JR="",aw={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},lw={class:"toast-content"};function uw(e,t,s,i,o,a){return N(),Pt(s_,{to:"body"},[A(Lf,{name:"toast"},{default:ye(()=>[s.show?(N(),I("div",{key:0,class:pe(["toast",s.type])},[h("div",lw,[h("i",{class:pe(a.icon)},null,2),h("span",null,G(s.message),1)]),h("div",{class:"toast-progress",style:ls(a.progressStyle)},null,4)],2)):ce("",!0)]),_:1})])}const Bn=ze(aw,[["render",uw],["__scopeId","data-v-4440998c"]]),XR="",cw={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const o=[];for(let a=s;a<=i;a++)o.push(a);return o},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},dw={class:"pagination-container mt-3"},fw={class:"pagination-info"},hw=["value"],pw={class:"pagination-text"},mw={class:"pagination-controls"},gw=["disabled"],vw=["onClick"],_w=["disabled"];function yw(e,t,s,i,o,a){return N(),I("div",dw,[h("div",fw,[bt(h("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(N(!0),I(Fe,null,vt(s.perPageOptions,u=>(N(),I("option",{key:u,value:u},G(u),9,hw))),128))],544),[[Zl,a.perPageModel]]),h("span",pw," Mostrando de "+G(a.from)+" até "+G(a.to)+" de "+G(s.total)+" resultados ",1)]),h("div",mw,[h("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[h("i",{class:"fas fa-chevron-left"},null,-1)]),8,gw),(N(!0),I(Fe,null,vt(a.visiblePages,u=>(N(),I("button",{key:u,class:pe(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},G(u),11,vw))),128)),h("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[h("i",{class:"fas fa-chevron-right"},null,-1)]),8,_w)])])}const mo=ze(cw,[["render",yw],["__scopeId","data-v-8e75ed87"]]),eV="",bw={name:"PageHeader",props:{title:{type:String,required:!0}}},ww={class:"page-header"},Ew={class:"header-actions"};function Cw(e,t,s,i,o,a){return N(),I("div",ww,[h("h2",null,G(s.title),1),h("div",Ew,[Vt(e.$slots,"actions",{},void 0,!0)])])}const na=ze(bw,[["render",Cw],["__scopeId","data-v-70ecc472"]]),tV="",Dw={name:"Modal",components:{CustomButton:Bo},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},xw={class:"modal-body"},Sw={key:0,class:"modal-footer"},Ow={key:1,class:"modal-footer"};function Nw(e,t,s,i,o,a){const u=X("custom-button");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[h("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Ft(()=>{},["stop"]))},[h("div",xw,[Vt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(N(),I("div",Sw,[Vt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(N(),I("div",Ow,[A(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),A(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):ce("",!0)],2)])):ce("",!0)}const Iw=ze(Dw,[["render",Nw],["__scopeId","data-v-784205f2"]]),sV="",Aw={name:"ConfirmationModal",components:{Modal:Iw},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},Tw={key:0,class:"icon-container"},Mw={class:"modal-custom-title"},Pw={key:1,class:"message-list"},kw={key:0,class:"list-title"},Rw={key:2,class:"message"},Vw={class:"modal-custom-footer"},Fw=["disabled"];function Uw(e,t,s,i,o,a){const u=X("modal");return N(),Pt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:ye(()=>[h("div",{class:pe(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(N(),I("div",Tw,[h("i",{class:pe(a.iconClass)},null,2)])):ce("",!0),h("h3",Mw,G(s.title),1),a.hasListContent?(N(),I("div",Pw,[s.listTitle?(N(),I("p",kw,G(s.listTitle),1)):ce("",!0),h("ul",null,[(N(!0),I(Fe,null,vt(s.listItems,(c,f)=>(N(),I("li",{key:f},G(c),1))),128))])])):(N(),I("div",Rw,G(s.message),1)),h("div",Vw,[h("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},G(s.cancelButtonText),1),h("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},G(s.confirmButtonText),9,Fw)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const _u=ze(Aw,[["render",Uw],["__scopeId","data-v-ef02bc58"]]),rV="",oV="",Lw={name:"OfferManagerView",components:{CustomTable:po,CustomSelect:mr,CustomInput:Ln,CustomCheckbox:sa,CustomButton:Bo,FilterSection:cp,FilterRow:ra,FilterGroup:oa,FilterActions:dp,Pagination:mo,PageHeader:na,ConfirmationModal:_u,LFLoading:vu,Toast:Bn},setup(){return{router:Ji()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:tp},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=sp.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){var e;try{const t=await pu();(e=t==null?void 0:t.data)!=null&&e.types&&(this.typeOptions=t.data.types.map(s=>typeof s=="object"&&s.value&&s.label?s:{value:s,label:s.charAt(0).toUpperCase()+s.slice(1)}))}catch(t){this.error=t.message}},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await e1(e),s=Array.isArray(t)?t[0]:t;if(!s.error&&s.data)this.offers=s.data.offers||[],this.totalOffers=s.data.total_items||0;else throw new Error(s.message||"Erro ao carregar ofertas")}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await t1(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await i1(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Bw={id:"offer-manager-component",class:"offer-manager"},$w={class:"new-offer-container"},jw={key:0,class:"alert alert-danger"},Hw={class:"table-container"},qw=["title"],zw={class:"action-buttons"},Ww=["onClick"],Gw=["onClick","disabled","title"],Kw={key:0,class:"fas fa-eye"},Qw={key:1,class:"fas fa-eye-slash"},Yw=["onClick","disabled","title"];function Zw(e,t,s,i,o,a){var we,Y,he,be,Te,le;const u=X("CustomButton"),c=X("PageHeader"),f=X("CustomInput"),m=X("FilterGroup"),p=X("CustomSelect"),v=X("CustomCheckbox"),w=X("FilterActions"),D=X("FilterRow"),k=X("FilterSection"),U=X("CustomTable"),te=X("Pagination"),T=X("ConfirmationModal"),oe=X("LFLoading"),Q=X("Toast");return N(),I("div",Bw,[A(c,{title:"Gerenciar Ofertas"},{actions:ye(()=>[h("div",$w,[A(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),A(k,{title:"FILTRO"},{default:ye(()=>[A(D,{inline:!0},{default:ye(()=>[A(m,{label:"Oferta"},{default:ye(()=>[A(f,{modelValue:o.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=ie=>o.inputFilters.search=ie),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),A(m,{label:"Tipo"},{default:ye(()=>[A(p,{modelValue:o.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=ie=>o.inputFilters.type=ie),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),A(m,{"is-checkbox":!0},{default:ye(()=>[A(v,{modelValue:o.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=ie=>o.inputFilters.hideInactive=ie),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),A(w,null,{default:ye(()=>[A(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),o.error?(N(),I("div",jw,[t[7]||(t[7]=h("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+G(o.error),1)])):ce("",!0),h("div",Hw,[A(U,{headers:o.tableHeaders,items:o.offers,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-description":ye(({item:ie})=>[h("span",{title:ie.description},G(ie.description.length>50?ie.description.slice(0,50)+"...":ie.description),9,qw)]),"item-type":ye(({item:ie})=>[nt(G(ie.type.charAt(0).toUpperCase()+ie.type.slice(1)),1)]),"item-status":ye(({item:ie})=>[nt(G(ie.status===1?"Ativa":"Inativa"),1)]),"item-actions":ye(({item:ie})=>[h("div",zw,[h("button",{class:"btn-action btn-edit",onClick:Pe=>a.editOffer(ie),title:"Editar"},t[8]||(t[8]=[h("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[h("g",{"clip-path":"url(#clip0_9_197955)"},[h("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),h("defs",null,[h("clipPath",{id:"clip0_9_197955"},[h("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,Ww),h("button",{class:pe(["btn-action",ie.status===1?"btn-deactivate":"btn-activate"]),onClick:Pe=>a.toggleOfferStatus(ie),disabled:ie.status===0&&!ie.can_activate,title:a.getStatusButtonTitle(ie)},[ie.status===1?(N(),I("i",Kw)):(N(),I("i",Qw))],10,Gw),h("button",{class:"btn-action btn-delete",onClick:Pe=>a.deleteOffer(ie),disabled:!ie.can_delete,title:ie.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[h("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Yw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(te,{"current-page":o.currentPage,"onUpdate:currentPage":t[3]||(t[3]=ie=>o.currentPage=ie),"per-page":o.perPage,"onUpdate:perPage":t[4]||(t[4]=ie=>o.perPage=ie),total:o.totalOffers,loading:o.loading},null,8,["current-page","per-page","total","loading"]),A(T,{show:o.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=ie=>o.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),A(T,{show:o.showStatusModal,title:((we=o.selectedOffer)==null?void 0:we.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Y=o.selectedOffer)==null?void 0:Y.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((he=o.selectedOffer)==null?void 0:he.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((be=o.selectedOffer)==null?void 0:be.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Te=o.selectedOffer)==null?void 0:Te.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((le=o.selectedOffer)==null?void 0:le.status)===1?"warning":"question",onClose:t[6]||(t[6]=ie=>o.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),A(oe,{"is-loading":o.loading},null,8,["is-loading"]),A(Q,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const Jw=ze(Lw,[["render",Zw],["__scopeId","data-v-fff5242a"]]);async function yu(e={}){try{const t=await Be("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});return t?t.error?(console.error("Erro na resposta de fetchEnrolments:",t.error),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}):Array.isArray(t)?t.length>0?{data:t[0]}:(console.error("Array de resposta vazio"),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}):{data:t}:(console.error("Resposta vazia de fetchEnrolments"),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}})}catch(t){return console.error("Exceção em fetchEnrolments:",t),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}}}async function Xw(e={}){try{const t=await Be("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});return t?Array.isArray(t)||t&&Array.isArray(t.data)||t&&t.error===!1&&Array.isArray(t.data)?t:(console.error("Formato de resposta não reconhecido:",t),{error:!0,data:[],message:"Formato de resposta não reconhecido"}):(console.error("Resposta vazia de enrolUsers"),{error:!0,data:[]})}catch(t){return console.error("Erro ao matricular usuários:",t),{error:!0,data:[],message:t.message||"Erro ao matricular usuários"}}}async function bu(e={}){try{const t=await yu({offerclassid:e.offerclassid,userids:[],page:1,perpage:100});if(!t||!t.data)return console.error("Resposta vazia de getFilterOptions"),[];const s=t.data.data||t.data;return!s.enrolments||!Array.isArray(s.enrolments)?(console.error("Resposta não contém enrolments ou não é um array:",s),[]):s.enrolments.map(o=>{const a={id:o.userid};switch(e.filter_type){case"name":a.fullname=o.fullname;break;case"email":a.email=o.email;break;case"cpf":a.cpf=o.cpf;break}return a})}catch(t){return console.error("Erro ao buscar opções de filtro:",t),[]}}async function eE(e){try{const t=await Be("local_offermanager_get_potential_users_to_enrol",{offerclassid:e});return t?t.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",t.error),[]):Array.isArray(t)?t:t.data&&Array.isArray(t.data)?t.data:(console.warn("Resposta não é um array nem contém um array em .data:",t),[]):(console.error("Resposta vazia de getPotentialUsersToEnrol"),[])}catch(t){return console.error("Erro ao buscar usuários potenciais:",t),[]}}async function tE(e={}){try{const t=await Be("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function sE(e={}){try{const t=await Be("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function rE(e){try{const t=await Be("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function oE(e){try{const t=await Be("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function nE(e,t){try{const s=await Be("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const nV="",iE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},aE={class:"select-wrapper"},lE=["value","disabled"],uE=["label"],cE=["value"],dE={key:1,class:"error-message"};function fE(e,t,s,i,o,a){return N(),I("div",{ref:"selectContainer",class:"hierarchical-select-container",style:ls(a.customWidth)},[s.label?(N(),I("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},G(s.label),3)):ce("",!0),h("div",aE,[h("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(N(!0),I(Fe,null,vt(s.options,u=>(N(),I("optgroup",{key:u.value,label:u.label},[(N(!0),I(Fe,null,vt(u.children,c=>(N(),I("option",{key:c.value,value:c.value,class:"child-option"},G(c.label),9,cE))),128))],8,uE))),128))],42,lE),h("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(N(),I("div",dE,G(s.errorMessage),1)):ce("",!0)],4)}const hE=ze(iE,[["render",fE],["__scopeId","data-v-b5d38077"]]),iV="",pE={name:"FilterTag",emits:["remove"]};function mE(e,t,s,i,o,a){return N(),I("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=h("i",{class:"fas fa-times"},null,-1)),Vt(e.$slots,"default",{},void 0,!0)])}const $n=ze(pE,[["render",mE],["__scopeId","data-v-fe063554"]]),aV="",gE={name:"FilterTags"},vE={class:"filter-tags"};function _E(e,t,s,i,o,a){return N(),I("div",vE,[Vt(e.$slots,"default",{},void 0,!0)])}const ia=ze(gE,[["render",_E],["__scopeId","data-v-d8e54e5f"]]),lV="",yE={name:"Autocomplete",components:{FilterTag:$n,FilterTags:ia},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=sp.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},bE={class:"autocomplete-container"},wE=["id"],EE={class:"autocomplete-wrapper"},CE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],DE={key:0,class:"selected-item"},xE=["title"],SE=["id"],OE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],NE={class:"item-label"},IE={key:0,class:"fas fa-check"},AE={key:0,class:"dropdown-item loading-item"},TE={key:1,class:"dropdown-item no-results"},ME={key:0,class:"tags-container"};function PE(e,t,s,i,o,a){const u=X("FilterTag"),c=X("FilterTags");return N(),I("div",bE,[s.label?(N(),I("label",{key:0,class:pe(["filter-label",{required:s.required}]),id:`${o.uniqueId}-label`},G(s.label),11,wE)):ce("",!0),h("div",EE,[h("div",{class:"input-container",style:ls({maxWidth:a.inputMaxWidthStyle})},[h("div",{class:pe(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery}])},[bt(h("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=f=>o.searchQuery=f),disabled:s.disabled,"aria-expanded":o.isOpen,"aria-owns":`${o.uniqueId}-listbox`,"aria-labelledby":s.label?`${o.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${o.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...f)=>a.handleKeydown&&a.handleKeydown(...f)),onFocus:t[2]||(t[2]=f=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...f)=>a.handleInput&&a.handleInput(...f)),onClick:t[4]||(t[4]=f=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...f)=>a.handleBlur&&a.handleBlur(...f)),ref:"inputElement"},null,40,CE),[[Fs,o.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery?(N(),I("div",DE,[h("span",{class:"selected-text",title:a.getSelectedItemLabel},G(a.truncateLabel(a.getSelectedItemLabel)),9,xE),h("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Ft((...f)=>a.removeSelectedItem&&a.removeSelectedItem(...f),["stop"]))})])):ce("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery)?(N(),I("i",{key:1,class:pe(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):ce("",!0)],2),o.isOpen?(N(),I("div",{key:0,class:"dropdown-menu show",id:`${o.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...f)=>a.handleScroll&&a.handleScroll(...f))},[a.displayItems.length>0?(N(),I(Fe,{key:0},[(N(!0),I(Fe,null,vt(a.displayItems,(f,m)=>(N(),I("div",{key:f.value==="__ALL__"?"__ALL__":f.value,class:pe(["dropdown-item",{active:o.selectedIndex===m,selected:f.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===f.value):s.modelValue===f.value)}]),id:`${o.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":o.selectedIndex===m,tabindex:o.selectedIndex===m?0:-1,onClick:p=>a.selectItem(f),onKeydown:p=>a.handleOptionKeydown(p,f,m),ref_for:!0,ref:"optionElements",title:f.label},[h("span",NE,G(a.truncateLabel(f.label)),1),f.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===f.value)?(N(),I("i",IE)):ce("",!0)],42,OE))),128)),s.loading?(N(),I("div",AE,t[8]||(t[8]=[h("span",null,"Carregando mais itens...",-1)]))):ce("",!0)],64)):(N(),I("div",TE,G(s.noResultsText||"Nenhum item disponível"),1))],40,SE)):ce("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(N(),I("div",ME,[A(c,null,{default:ye(()=>[(N(!0),I(Fe,null,vt(s.modelValue,f=>(N(),Pt(u,{key:f.value,onRemove:m=>a.removeItem(f)},{default:ye(()=>[nt(G(f.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):ce("",!0)])])}const jn=ze(yE,[["render",PE],["__scopeId","data-v-aa96f7a3"]]),uV="",kE={name:"EnrolmentModalNew",components:{Toast:Bn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:[Number,String],required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",roleOptions:[],searchQuery:"",isOpen:!1,selectedIndex:-1,userOptions:[],selectedUsers:[],selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingRoles:!0,loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},computed:{isFormValid(){return this.roleOptions.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1},filteredUsers(){if(!this.searchQuery)return this.userOptions;const e=this.searchQuery.toLowerCase();return this.userOptions.filter(t=>t.label.toLowerCase().includes(e))}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=this.$el.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1,this.selectedIndex=-1)},async initializeForm(){if(this.offerclassid)try{await this.loadRoles(),await this.loadUsers(),this.resetForm()}catch(e){console.error("Erro ao inicializar formulário:",e)}},resetForm(){if(this.enrolmentMethod="manual",this.roleOptions.length>0){const e=this.roleOptions.find(t=>t.label.toLowerCase().includes("aluno")||t.label.toLowerCase().includes("estudante")||t.label.toLowerCase().includes("student"));this.selectedRoleId=e?e.value:this.roleOptions[0].value}else this.selectedRoleId="";this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8"},async loadRoles(){this.loadingRoles=!0;try{if(!this.offerclassid)return;const e=await Lr(parseInt(this.offerclassid));let t;if(e&&e.data?t=e.data.offercourseid:e&&(t=e.offercourseid),!t)throw new Error("Offercourseid não encontrado");const s=await ta(t);let i=[];if(s&&Array.isArray(s)?i=s:s&&s.data&&Array.isArray(s.data)&&(i=s.data),i.length>0){this.roleOptions=i.map(a=>({value:String(a.id),label:a.name}));const o=this.roleOptions.find(a=>a.value==="5");o?this.selectedRoleId=o.value:this.roleOptions.length>0&&(this.selectedRoleId=this.roleOptions[0].value)}else console.error("Nenhum papel disponível para este curso")}catch(e){console.error("Erro ao carregar papéis:",e),this.roleOptions=[],this.selectedRoleId=""}finally{this.loadingRoles=!1}},async loadUsers(){try{if(!this.offerclassid){console.error("EnrolmentModalNew - loadUsers: ID da turma não definido"),this.userOptions=[];return}this.loadingUsers=!0;const e=parseInt(this.offerclassid);if(isNaN(e)){this.userOptions=[];return}const t=await eE(e),i=(await this.getEnrolledUsers(e)).map(o=>o.id);if(t&&Array.isArray(t)){const o=t.filter(a=>!i.includes(a.id));this.userOptions=o.map(a=>({value:a.id,label:a.fullname}))}else this.userOptions=[]}catch(e){console.error("Erro ao carregar usuários:",e),this.userOptions=[]}finally{this.loadingUsers=!1}},async getEnrolledUsers(e){try{const t=await yu({offerclassid:e,page:1,perpage:1e3});if(console.log("EnrolmentModalNew - Resposta de fetchEnrolments:",t),t&&t.data){const s=t.data.data||t.data;if(s&&Array.isArray(s.enrolments))return s.enrolments.map(i=>({id:i.userid}))}return[]}catch(t){return console.error("Erro ao obter usuários matriculados:",t),[]}},handleFocus(){this.isOpen=!0,this.selectedIndex=-1},handleInput(){this.isOpen=!0},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=o=>{const a=o.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),o=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const v=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(v))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),f=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const v=a(p,t);if(v.length>Math.max(c,f)){const w=v[c].trim(),D=v[f].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}o.push({id:w,name:D})}}}if(o.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=o}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(a=>a.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(a=>parseInt(a.id)));const t=await Xw({offerclassid:parseInt(this.offerclassid),userids:e,roleid:parseInt(this.selectedRoleId)});console.log("Resposta da matrícula:",JSON.stringify(t,null,2));let s=[],i=!1;if(Array.isArray(t))s=t;else if(t&&Array.isArray(t.data))s=t.data;else if(Array.isArray(t)&&t.length>0&&t[0].data)s=t[0].data;else if(Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data))s=t[0].data;else{console.error("Formato de resposta não reconhecido:",t),this.showErrorMessage("Erro ao processar a resposta do servidor. Verifique o console para mais detalhes.");return}const o=s.filter(a=>a.success).length;if(i=o>0,i)this.showSuccessMessage(`${o} de ${e.length} usuário(s) matriculado(s) com sucesso.`),setTimeout(()=>{this.$emit("success",{count:o,total:e.length}),this.$emit("close")},1500);else{console.error("Nenhum usuário foi matriculado com sucesso");let a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.";s.length>0&&s[0].message&&(s[0].message.includes("já está matriculado")||s[0].message.includes("already enrolled")?a=s[0].message:s[0].message.includes("Error enrolling user")||s[0].message.includes("[[message:")||s[0].message.includes("enrolment_failed")?a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.":a=s[0].message),this.showErrorMessage(a)}}catch(e){console.error("Erro ao matricular usuários:",e),this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},RE={class:"modal-header"},VE={class:"modal-title"},FE={class:"modal-body"},UE={class:"enrolment-modal"},LE={class:"form-row"},BE={class:"form-group"},$E={class:"limited-width-input"},jE={class:"form-group"},HE={class:"limited-width-input"},qE={key:0,class:"error-message"},zE={key:0,class:"form-group"},WE={class:"user-select-container"},GE={class:"custom-autocomplete-wrapper"},KE={key:0,class:"dropdown-menu show"},QE=["onClick"],YE={key:0,class:"fas fa-check"},ZE={key:0,class:"selected-users-container"},JE={class:"filter-tags"},XE=["onClick"],eC={key:1,class:"form-group"},tC={class:"file-name"},sC={class:"file-size"},rC={key:0,class:"csv-users-preview"},oC={class:"preview-header"},nC={class:"selected-users-container"},iC={class:"filter-tags"},aC={key:0,class:"more-users"},lC={class:"csv-info"},uC={class:"csv-example"},cC=["href"],dC={class:"csv-options-row"},fC={class:"csv-option"},hC={class:"csv-option"},pC={class:"modal-footer"},mC=["disabled"];function gC(e,t,s,i,o,a){const u=X("CustomSelect"),c=X("Toast");return N(),I(Fe,null,[s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[16]||(t[16]=f=>s.closeOnBackdrop?e.$emit("close"):null)},[h("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[15]||(t[15]=Ft(()=>{},["stop"]))},[h("div",RE,[h("h3",VE,G(s.title),1),h("button",{class:"modal-close",onClick:t[0]||(t[0]=f=>e.$emit("close"))},t[17]||(t[17]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",FE,[h("div",UE,[t[32]||(t[32]=h("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),h("div",LE,[h("div",BE,[t[18]||(t[18]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Forma de matrícula"),h("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),h("div",$E,[A(u,{modelValue:o.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=f=>o.enrolmentMethod=f),options:o.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),h("div",jE,[t[19]||(t[19]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Papel para atribuir"),h("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),h("div",HE,[A(u,{modelValue:o.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=f=>o.selectedRoleId=f),options:o.roleOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"]),!o.loadingRoles&&o.roleOptions.length===0?(N(),I("div",qE," Não foi possível carregar os papéis disponíveis para esta turma. ")):ce("",!0)])])]),o.enrolmentMethod==="manual"?(N(),I("div",zE,[t[22]||(t[22]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Selecionar usuários"),h("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),h("div",WE,[h("div",GE,[bt(h("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=f=>o.searchQuery=f),onFocus:t[4]||(t[4]=(...f)=>a.handleFocus&&a.handleFocus(...f)),onInput:t[5]||(t[5]=(...f)=>a.handleInput&&a.handleInput(...f))},null,544),[[Fs,o.searchQuery]]),t[20]||(t[20]=h("div",{class:"select-arrow"},null,-1)),o.isOpen?(N(),I("div",KE,[(N(!0),I(Fe,null,vt(a.filteredUsers,(f,m)=>(N(),I("div",{key:f.value,class:pe(["dropdown-item",{active:o.selectedIndex===m,selected:o.selectedUsers.some(p=>p.value===f.value)}]),onClick:p=>a.selectUser(f)},[nt(G(f.label)+" ",1),o.selectedUsers.some(p=>p.value===f.value)?(N(),I("i",YE)):ce("",!0)],10,QE))),128))])):ce("",!0)])]),o.selectedUsers.length>0?(N(),I("div",ZE,[h("div",JE,[(N(!0),I(Fe,null,vt(o.selectedUsers,f=>(N(),I("div",{key:f.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(f)},[t[21]||(t[21]=h("i",{class:"fas fa-times"},null,-1)),nt(" "+G(f.label),1)],8,XE))),128))])])):ce("",!0)])):ce("",!0),o.enrolmentMethod==="batch"?(N(),I("div",eC,[t[31]||(t[31]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),h("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),h("div",{class:pe(["csv-upload-area",{"drag-over":o.isDragging}]),onDragover:t[7]||(t[7]=Ft((...f)=>a.onDragOver&&a.onDragOver(...f),["prevent"])),onDragleave:t[8]||(t[8]=Ft((...f)=>a.onDragLeave&&a.onDragLeave(...f),["prevent"])),onDrop:t[9]||(t[9]=Ft((...f)=>a.onDrop&&a.onDrop(...f),["prevent"])),onClick:t[10]||(t[10]=f=>e.$refs.fileInput.click())},[h("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[6]||(t[6]=(...f)=>a.handleFileSelect&&a.handleFileSelect(...f))},null,544),o.selectedFile?(N(),I(Fe,{key:1},[t[25]||(t[25]=h("div",{class:"file-icon"},[h("i",{class:"fas fa-file-alt"})],-1)),h("p",tC,G(o.selectedFile.name),1),h("p",sC," ("+G(a.formatFileSize(o.selectedFile.size))+") ",1),t[26]||(t[26]=h("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(N(),I(Fe,{key:0},[t[23]||(t[23]=h("div",{class:"upload-icon"},[h("i",{class:"fas fa-arrow-down"})],-1)),t[24]||(t[24]=h("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),o.csvUsers.length>0?(N(),I("div",rC,[h("div",oC,[h("span",null,"Usuários encontrados no arquivo ("+G(o.csvUsers.length)+"):",1)]),h("div",nC,[h("div",iC,[(N(!0),I(Fe,null,vt(o.csvUsers.slice(0,5),f=>(N(),I("div",{key:f.id,class:"tag badge badge-primary"},G(f.name),1))),128)),o.csvUsers.length>5?(N(),I("span",aC,"+"+G(o.csvUsers.length-5)+" mais",1)):ce("",!0)])])])):ce("",!0),h("div",lC,[t[30]||(t[30]=h("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),h("div",uC,[t[27]||(t[27]=h("span",{class:"example-label"},"Exemplo CSV",-1)),h("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,cC)]),h("div",dC,[h("div",fC,[t[28]||(t[28]=h("label",null,"Delimitador do CSV",-1)),A(u,{modelValue:o.csvDelimiter,"onUpdate:modelValue":t[11]||(t[11]=f=>o.csvDelimiter=f),options:o.delimiterOptions,width:160},null,8,["modelValue","options"])]),h("div",hC,[t[29]||(t[29]=h("label",null,"Codificação",-1)),A(u,{modelValue:o.csvEncoding,"onUpdate:modelValue":t[12]||(t[12]=f=>o.csvEncoding=f),options:o.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):ce("",!0),t[33]||(t[33]=h("div",{class:"form-info"},[h("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),h("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))])]),h("div",pC,[h("button",{class:"btn btn-primary",onClick:t[13]||(t[13]=(...f)=>a.handleSubmit&&a.handleSubmit(...f)),disabled:o.isSubmitting||!a.isFormValid},G(s.confirmButtonText),9,mC),h("button",{class:"btn btn-secondary",onClick:t[14]||(t[14]=f=>e.$emit("close"))},G(s.cancelButtonText),1)])],2)])):ce("",!0),A(c,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])],64)}const vC=ze(kE,[["render",gC],["__scopeId","data-v-da15b722"]]),cV="",_C={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},yC={class:"modal-header"},bC={key:0,class:"modal-body"},wC={class:"details-container"},EC={class:"detail-row"},CC={class:"detail-value"},DC={class:"detail-row"},xC={class:"detail-value"},SC={class:"detail-row"},OC={class:"detail-value"},NC={class:"detail-row"},IC={class:"detail-value"},AC={class:"detail-row"},TC={class:"detail-value"},MC={key:1,class:"modal-body no-data"},PC={class:"modal-footer"};function kC(e,t,s,i,o,a){return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[h("div",{class:"modal-container",onClick:t[2]||(t[2]=Ft(()=>{},["stop"]))},[h("div",yC,[t[5]||(t[5]=h("h3",{class:"modal-title"},"Informações da matrícula",-1)),h("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[h("i",{class:"fas fa-times"},null,-1)]))]),s.user?(N(),I("div",bC,[h("div",wC,[h("div",EC,[t[6]||(t[6]=h("div",{class:"detail-label"},"Nome completo",-1)),h("div",CC,G(s.user.fullName),1)]),h("div",DC,[t[7]||(t[7]=h("div",{class:"detail-label"},"Curso",-1)),h("div",xC,G(s.courseName),1)]),h("div",SC,[t[8]||(t[8]=h("div",{class:"detail-label"},"Método de inscrição",-1)),h("div",OC,G(a.getEnrolmentMethod(s.user.enrol)),1)]),h("div",NC,[t[9]||(t[9]=h("div",{class:"detail-label"},"Estado",-1)),h("div",IC,[h("span",{class:pe(["state-tag",s.user.state===0?"state-ativo":"state-inativo"])},G(s.user.stateName),3)])]),h("div",AC,[t[10]||(t[10]=h("div",{class:"detail-label"},"Matrícula criada",-1)),h("div",TC,G(s.user.createdDate),1)])])])):(N(),I("div",MC,"Nenhum dado disponível")),h("div",PC,[h("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):ce("",!0)}const RC=ze(_C,[["render",kC],["__scopeId","data-v-87efb59a"]]),dV="",VC={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.state;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(f=>parseInt(f.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await tE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,o]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,o,a,u,0,0)}}},FC={class:"modal-header"},UC={class:"modal-title"},LC={class:"modal-body"},BC={class:"enrollment-form"},$C={class:"form-row"},jC={class:"form-value"},HC={class:"form-row"},qC={class:"form-field"},zC={class:"select-wrapper"},WC={class:"form-row"},GC={class:"form-field date-time-field"},KC={class:"date-field"},QC={class:"time-field"},YC={class:"enable-checkbox"},ZC={class:"form-row"},JC={class:"form-field"},XC={class:"select-wrapper"},eD={class:"form-row"},tD={class:"date-field"},sD=["disabled"],rD={class:"time-field"},oD=["disabled"],nD={class:"enable-checkbox"},iD={class:"form-row"},aD={class:"form-value"},lD={class:"modal-footer"},uD={class:"footer-buttons"},cD=["disabled"];function dD(e,t,s,i,o,a){const u=X("CustomSelect");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[h("div",{class:"modal-container",onClick:t[14]||(t[14]=Ft(()=>{},["stop"]))},[h("div",FC,[h("h3",UC," Editar matrícula de "+G(s.user?s.user.fullName:""),1),h("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",LC,[h("div",BC,[h("div",$C,[t[17]||(t[17]=h("div",{class:"form-label"},"Método de inscrição",-1)),h("div",jC,G(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),h("div",HC,[t[18]||(t[18]=h("div",{class:"form-label"},"Estado",-1)),h("div",qC,[h("div",zC,[A(u,{modelValue:o.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>o.formData.status=c),options:o.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),h("div",WC,[t[20]||(t[20]=h("div",{class:"form-label"},"Matrícula começa",-1)),h("div",GC,[h("div",KC,[bt(h("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>o.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[Fs,o.formData.startDateStr]])]),h("div",QC,[bt(h("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>o.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[Fs,o.formData.startTimeStr]])]),h("div",YC,[bt(h("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>o.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[ji,o.formData.enableStartDate]]),t[19]||(t[19]=h("label",{for:"enable-start-date"},"Habilitar",-1))])])]),h("div",ZC,[t[21]||(t[21]=h("div",{class:"form-label"},"Período de validade da matrícula",-1)),h("div",JC,[h("div",XC,[A(u,{modelValue:o.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>o.formData.validityPeriod=c),options:o.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:o.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),h("div",eD,[t[23]||(t[23]=h("div",{class:"form-label"},"Matrícula termina",-1)),h("div",{class:pe(["form-field date-time-field",{"disabled-inputs-only":!o.formData.enableEndDate}])},[h("div",tD,[bt(h("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>o.formData.endDateStr=c),class:"form-control",disabled:!o.formData.enableEndDate},null,8,sD),[[Fs,o.formData.endDateStr]])]),h("div",rD,[bt(h("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>o.formData.endTimeStr=c),class:"form-control",disabled:!o.formData.enableEndDate},null,8,oD),[[Fs,o.formData.endTimeStr]])]),h("div",nD,[bt(h("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>o.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[ji,o.formData.enableEndDate]]),t[22]||(t[22]=h("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),h("div",iD,[t[24]||(t[24]=h("div",{class:"form-label"},"Matrícula criada",-1)),h("div",aD,G(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),h("div",lD,[t[25]||(t[25]=h("div",{class:"footer-spacer"},null,-1)),h("div",uD,[h("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:o.isSubmitting},G(o.isSubmitting?"Salvando...":"Salvar mudanças"),9,cD),h("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const fD=ze(VC,[["render",dD],["__scopeId","data-v-ee7a852d"]]),fV="",hV="",hD={name:"BulkEditEnrollmentModal",components:{Pagination:mo,CustomTable:po,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"stateName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[f,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,f,m,0,0);t=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;t+=v}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[f,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,f,m,0,0);s=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;s+=v}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const o=await sE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(o)&&o.length>0){const a=o.filter(f=>f.operation_status).length,u=o.length-a;let c="";if(a===o.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${o.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:o.length}),this.$emit("close")}else console.error("Resposta inválida da API:",o),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},pD={class:"modal-header"},mD={class:"modal-body"},gD={class:"enrollment-form"},vD={class:"table-container"},_D={class:"form-row"},yD={class:"form-field"},bD={class:"select-wrapper"},wD={class:"form-row"},ED={class:"form-field date-time-field"},CD={class:"date-field"},DD=["disabled"],xD={class:"time-field"},SD=["disabled"],OD={class:"enable-checkbox"},ND={class:"form-row"},ID={class:"form-field date-time-field"},AD={class:"date-field"},TD=["disabled"],MD={class:"time-field"},PD=["disabled"],kD={class:"enable-checkbox"},RD={class:"modal-footer"},VD={class:"footer-buttons"},FD=["disabled"];function UD(e,t,s,i,o,a){const u=X("CustomTable"),c=X("Pagination"),f=X("CustomSelect");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[h("div",{class:"modal-container",onClick:t[16]||(t[16]=Ft(()=>{},["stop"]))},[h("div",pD,[t[19]||(t[19]=h("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),h("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",mD,[h("div",gD,[h("div",null,[h("div",vD,[A(u,{headers:o.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?bt((N(),Pt(c,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>o.currentPage=m),"per-page":o.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>o.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>o.perPage]]):ce("",!0),t[20]||(t[20]=h("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),h("div",_D,[t[21]||(t[21]=h("div",{class:"form-label"},"Alterar o status",-1)),h("div",yD,[h("div",bD,[A(f,{modelValue:o.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>o.formData.status=m),options:o.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),h("div",wD,[t[23]||(t[23]=h("div",{class:"form-label"},"Alterar data de início",-1)),h("div",ED,[h("div",CD,[bt(h("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>o.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!o.formData.enableStartDate},null,40,DD),[[Fs,o.formData.startDateStr]])]),h("div",xD,[bt(h("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>o.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!o.formData.enableStartDate},null,40,SD),[[Fs,o.formData.startTimeStr]])]),h("div",OD,[bt(h("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>o.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[ji,o.formData.enableStartDate]]),t[22]||(t[22]=h("label",{for:"enable-start-date"},"Habilitar",-1))])])]),h("div",ND,[t[25]||(t[25]=h("div",{class:"form-label"},"Alterar data de fim",-1)),h("div",ID,[h("div",AD,[bt(h("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>o.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!o.formData.enableEndDate},null,40,TD),[[Fs,o.formData.endDateStr]])]),h("div",MD,[bt(h("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>o.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!o.formData.enableEndDate},null,40,PD),[[Fs,o.formData.endTimeStr]])]),h("div",kD,[bt(h("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>o.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[ji,o.formData.enableEndDate]]),t[24]||(t[24]=h("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),h("div",RD,[t[26]||(t[26]=h("div",{class:"footer-spacer"},null,-1)),h("div",VD,[h("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:o.isSubmitting},G(o.isSubmitting?"Salvando...":"Salvar mudanças"),9,FD),h("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const LD=ze(hD,[["render",UD],["__scopeId","data-v-0ff4495e"]]),pV="",BD={name:"BulkDeleteEnrollmentModal",components:{Pagination:mo,CustomSelect:mr,CustomTable:po},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"stateName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},$D={class:"modal-header"},jD={class:"modal-body"},HD={class:"enrollment-form"},qD={class:"table-container"},zD={class:"modal-footer"},WD={class:"footer-buttons"},GD=["disabled"];function KD(e,t,s,i,o,a){const u=X("CustomTable"),c=X("Pagination");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=f=>e.$emit("close"))},[h("div",{class:"modal-container",onClick:t[5]||(t[5]=Ft(()=>{},["stop"]))},[h("div",$D,[t[8]||(t[8]=h("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),h("button",{class:"modal-close",onClick:t[0]||(t[0]=f=>e.$emit("close"))},t[7]||(t[7]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",jD,[h("div",HD,[h("div",qD,[A(u,{headers:o.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?bt((N(),Pt(c,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[1]||(t[1]=f=>o.currentPage=f),"per-page":o.perPage,"onUpdate:perPage":t[2]||(t[2]=f=>o.perPage=f),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>o.perPage]]):ce("",!0)]),t[9]||(t[9]=h("div",{class:"text-center mt-5"},[h("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),h("div",zD,[h("div",WD,[h("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=f=>e.$emit("confirm")),disabled:o.isSubmitting},G(o.isSubmitting?"Removendo...":"Remover matrículas"),9,GD),h("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=f=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const QD=ze(BD,[["render",KD],["__scopeId","data-v-fd850db3"]]),mV="",YD={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function ZD(e,t,s,i,o,a){return N(),I("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=h("i",{class:"fas fa-angle-left"},null,-1)),nt(" "+G(s.label),1)])}const wu=ze(YD,[["render",ZD],["__scopeId","data-v-c577f103"]]),gV="",JD={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},XD=["src"];function ex(e,t,s,i,o,a){return N(),I("div",{class:"user-avatar",style:ls(a.avatarStyle)},[a.hasImage?(N(),I("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,XD)):(N(),I("div",{key:1,class:"avatar-initials",style:ls({backgroundColor:a.backgroundColor})},G(a.initials),5))],4)}const tx=ze(JD,[["render",ex],["__scopeId","data-v-eed19d8a"]]),vV="",sx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await Lr(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await ta(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const o=await oE(this.offeruserenrolid);if(Array.isArray(o)&&o.length)this.selectedRoles=o.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await nE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},rx={class:"role-selector"},ox={key:1,class:"role-edit-wrapper"},nx={class:"role-edit-container"},ix={class:"select-wrapper"},ax=["value"],lx={class:"role-actions"},ux={key:2,class:"loading-overlay"};function cx(e,t,s,i,o,a){return N(),I("div",rx,[o.isEditing?(N(),I("div",ox,[h("div",nx,[h("div",ix,[bt(h("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>o.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Ft(()=>{},["stop"])),style:ls({height:Math.max(4,o.roles.length)*25+"px"})},[(N(!0),I(Fe,null,vt(o.roles,u=>(N(),I("option",{key:u.id,value:u.id},G(u.name),9,ax))),128))],4),[[Zl,o.selectedRoles]])]),h("div",lx,[h("button",{class:"btn-save",onClick:t[3]||(t[3]=Ft((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[h("i",{class:"fas fa-check"},null,-1)])),h("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Ft((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[h("i",{class:"fas fa-times"},null,-1)]))])])])):(N(),I("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Ft((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[h("span",null,G(a.displayRoleNames),1),t[5]||(t[5]=h("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),o.loading&&o.isEditing?(N(),I("div",ux,t[8]||(t[8]=[h("div",{class:"spinner"},null,-1)]))):ce("",!0)])}const dx=ze(sx,[["render",cx],["__scopeId","data-v-217c6284"]]),_V="",fx={name:"RegisteredUsers",props:{offerclassid:{type:String,required:!0}},components:{CustomTable:po,CustomSelect:mr,HierarchicalSelect:hE,CustomInput:Ln,CustomCheckbox:sa,CustomButton:Bo,FilterSection:cp,FilterRow:ra,FilterGroup:oa,FilterActions:dp,FilterTag:$n,FilterTags:ia,Pagination:mo,PageHeader:na,ConfirmationModal:_u,Autocomplete:jn,EnrolmentModalNew:vC,EnrollmentDetailsModal:RC,Toast:Bn,EditEnrollmentModal:fD,BulkEditEnrollmentModal:LD,BulkDeleteEnrollmentModal:QD,BackButton:wu,UserAvatar:tx,RoleSelector:dx,LFLoading:vu},setup(){return{router:Ji()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:tp},offerId:null,inputFilters:{name:[],cpf:[],email:[]},appliedFilters:{name:[],cpf:[],email:[]},nameOptions:[],cpfOptions:[],emailOptions:[],selectedName:"",selectedCpf:"",selectedEmail:"",selectedUserIds:{name:[],cpf:[],email:[]},tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"status",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"state",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectAll:!1,selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},computed:{hasActiveFilters(){return this.appliedFilters.name&&this.appliedFilters.name.length>0||this.appliedFilters.cpf&&this.appliedFilters.cpf.length>0||this.appliedFilters.email&&this.appliedFilters.email.length>0},allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},filteredNameOptions(){const e=this.appliedFilters.name.map(t=>t.value);return this.nameOptions.filter(t=>!e.includes(t.value))},filteredCpfOptions(){const e=this.appliedFilters.cpf.map(t=>t.value);return this.cpfOptions.filter(t=>!e.includes(t.value))},filteredEmailOptions(){const e=this.appliedFilters.email.map(t=>t.value);return this.emailOptions.filter(t=>!e.includes(t.value))}},watch:{perPage(e,t){if(e!==t){this.currentPage=1,this.selectedUsers=[];const s=this.offerclassid||this.$route.params.offerclassid;s&&this.loadRegisteredUsers(s)}},currentPage(e,t){if(e!==t){this.selectedUsers=[];const s=this.offerclassid||this.$route.params.offerclassid;s&&this.loadRegisteredUsers(s)}}},async created(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(e){try{const t=await Lr(parseInt(e));if(this.classDetails=(t==null?void 0:t.data)||{},t&&t.error===!1&&Array.isArray(t.data)){const s=t.data.find(i=>i.id===parseInt(e));s&&s.offerid?this.offerId=s.offerid:t.data.length>0&&t.data[0].offerid&&(this.offerId=t.data[0].offerid)}else t&&t.data&&t.data.offercourseid?e==="3"&&(this.offerId=2):t&&t.offercourseid&&e==="3"&&(this.offerId=2);e==="3"&&!this.offerId&&(this.offerId=2)}catch{e==="3"&&(this.offerId=2)}await this.loadRoles(),await this.loadNameOptions(),await this.loadCpfOptions(),await this.loadEmailOptions(),await this.loadRegisteredUsers(e)}else this.error="ID da turma não encontrado"}catch(e){this.error="Erro ao carregar dados iniciais: "+e.message}},methods:{async loadRegisteredUsers(e){try{if(this.loading=!0,this.error=null,!e){this.error="ID da turma não fornecido",this.loading=!1;return}let t=[];if(this.selectedUserIds.name.length>0||this.selectedUserIds.cpf.length>0||this.selectedUserIds.email.length>0){const a=[...this.selectedUserIds.name,...this.selectedUserIds.cpf,...this.selectedUserIds.email];t=[...new Set(a)]}else if(this.hasActiveFilters)try{const a=[];this.appliedFilters.name&&this.appliedFilters.name.length>0&&this.appliedFilters.name.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),this.appliedFilters.cpf&&this.appliedFilters.cpf.length>0&&this.appliedFilters.cpf.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),this.appliedFilters.email&&this.appliedFilters.email.length>0&&this.appliedFilters.email.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),a.length>0&&(t=a)}catch{}const i={offerclassid:parseInt(e),userids:t,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},o=await yu(i);if(o&&o.data){const a=o.data.data||o.data;if(Array.isArray(a.enrolments)){const u=await this.calculateDeadline();this.enrolments=a.enrolments.map(c=>({id:c.userid,offeruserenrolid:c.offeruserenrolid,fullName:c.fullname,email:c.email,cpf:c.cpf,enrol:c.enrol,roles:this.formatRoles(c.roles),groups:c.groups,timecreated:c.timecreated,createdDate:this.formatDateTime(c.timecreated),timestart:c.timestart,timeend:c.timeend,startDate:this.formatDate(c.timestart),endDate:this.formatDate(c.timeend),deadline:u==null?"Imilitado":u===1?"1 dia":`${u} dias`,progress:this.formatProgress(c.progress),status:c.situation,statusName:c.situation_name,grade:c.grade||"-",state:c.ue_status,stateName:c.ue_status!==void 0?c.ue_status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=a.total||this.enrolments.length}}else this.enrolments=[],this.totalEnrolments=0}catch(t){this.error="Erro ao carregar usuários matriculados: "+t.message,this.enrolments=[],this.totalEnrolments=0}finally{this.loading=!1}},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},async calculateDeadline(){const e=this.offerclassid||this.$route.params.offerclassid,t=await Lr(parseInt(e));if(t&&t.data){const s=t.data.optional_fields,i=s.enrolperiod,o=s.enableenrolperiod,a=s.enableenddate,u=t.data.startdate,c=s.enddate;let f;if(!a&&!o)return null;if(i===0&&o===!1)if(u&&c){const m=new Date(u),v=new Date(c)-m;f=Math.ceil(v/(1e3*60*60*24))}else f=null;else f=i;return f}return null},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").map(t=>this.translateAndCapitalizeRole(t.trim())).join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.map(t=>this.translateAndCapitalizeRole(t)).join(", "):"-"},translateAndCapitalizeRole(e){if(!e)return"";if(typeof e!="string")try{e=String(e)}catch{return"Papel desconhecido"}return e.charAt(0).toUpperCase()+e.slice(1)},async loadNameOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"name"});Array.isArray(t)?this.nameOptions=t.map(s=>({value:s.id,label:s.fullname})):this.nameOptions=[]}catch{this.nameOptions=[]}},async loadCpfOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"cpf"});Array.isArray(t)?this.cpfOptions=t.map(s=>({value:s.id,label:s.cpf})):this.cpfOptions=[]}catch{this.cpfOptions=[]}},async loadEmailOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"email"});Array.isArray(t)?this.emailOptions=t.map(s=>({value:s.id,label:s.email})):this.emailOptions=[]}catch{this.emailOptions=[]}},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},handleNameSelectNoFilter(e){if(this.inputFilters.name.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.name.push(s),this.appliedFilters.name.push(s)}this.selectedName="",this.selectedUserIds.name.includes(e.value)||this.selectedUserIds.name.push(e.value)},handleCpfSelectNoFilter(e){if(this.inputFilters.cpf.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.cpf.push(s),this.appliedFilters.cpf.push(s)}this.selectedCpf="",this.selectedUserIds.cpf.includes(e.value)||this.selectedUserIds.cpf.push(e.value)},handleEmailSelectNoFilter(e){if(this.inputFilters.email.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.email.push(s),this.appliedFilters.email.push(s)}this.selectedEmail="",this.selectedUserIds.email.includes(e.value)||this.selectedUserIds.email.push(e.value)},handleNameSelect(e){this.handleNameSelectNoFilter(e),this.applyFilters()},handleCpfSelect(e){this.handleCpfSelectNoFilter(e),this.applyFilters()},handleEmailSelect(e){this.handleEmailSelectNoFilter(e),this.applyFilters()},async handlePageChange(e){if(e!==this.currentPage){this.currentPage=e;const t=this.offerclassid||this.$route.params.offerclassid;t?await this.loadRegisteredUsers(t):this.error="ID da turma não encontrado"}},async handlePerPageChange(e){if(e!==this.perPage){this.perPage=e,this.currentPage=1;const t=this.offerclassid||this.$route.params.offerclassid;t?await this.loadRegisteredUsers(t):this.error="ID da turma não encontrado"}},async applyFilters(){try{this.loading=!0,this.currentPage=1,this.selectedUsers=[],this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));const e=this.offerclassid||this.$route.params.offerclassid;e?await this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"}catch(e){this.error=e.message}finally{this.loading=!1}},async clearFilters(){this.inputFilters={name:[],cpf:[],email:[]},this.appliedFilters={name:[],cpf:[],email:[]},this.selectedName="",this.selectedCpf="",this.selectedEmail="",this.selectedUserIds={name:[],cpf:[],email:[]},this.selectedUsers=[],this.currentPage=1;const e=this.offerclassid||this.$route.params.offerclassid;e?await this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"},async removeFilter(e,t){t!==void 0?(this.appliedFilters[e].splice(t,1),this.inputFilters[e].splice(t,1),this.selectedUserIds[e][t]&&this.selectedUserIds[e].splice(t,1)):(this.inputFilters[e]=[],this.appliedFilters[e]=[],this.selectedUserIds[e]=[]),e==="name"?this.selectedName="":e==="cpf"?this.selectedCpf="":e==="email"&&(this.selectedEmail=""),this.selectedUsers=[],this.currentPage=1;const s=this.offerclassid||this.$route.params.offerclassid;s?await this.loadRegisteredUsers(s):this.error="ID da turma não encontrado"},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t;const s=this.offerclassid||this.$route.params.offerclassid;s&&await this.loadRegisteredUsers(s)},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",status:"situation",state:"ue_status"}[e]||"fullname"},addNewUser(){var t;if(!(this.offerclassid||this.$route.params.offerclassid)){this.error="ID da turma não encontrado. Não é possível matricular usuários.";return}if(this.classDetails&&((t=this.classDetails)==null?void 0:t.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e){this.router.push({name:"offer-manager"});return}const t=await Lr(parseInt(e));if(!t||t.error){this.router.push({name:"offer-manager"});return}let s=null;if(t.data&&t.data.offercourseid)s=t.data.offercourseid;else if(t.offercourseid)s=t.offercourseid;else if(t.data&&Array.isArray(t.data)){const a=t.data.find(u=>u.id===parseInt(e));a&&a.offercourseid&&(s=a.offercourseid)}if(!s){this.router.push({name:"offer-manager"});return}const i=await gu(s);if(!i){this.router.push({name:"offer-manager"});return}let o=null;if(i.data[0].offerid&&(o=i.data[0].offerid),!o){this.router.push({name:"offer-manager"});return}this.router.push({name:"editar-oferta",params:{id:o.toString()}})}catch{this.router.push({name:"offer-manager"})}},viewUserProfile(e){var i;if(!e)return;const t=(i=this.classDetails)==null?void 0:i.courseid,s=`/user/view.php?id=${e}&course=${t}`;window.location.href=s},async handlePageViewChange(e){const t=this.offerclassid||this.$route.params.offerclassid;if(t)try{const s=await Lr(parseInt(t));if(!s||!s.data||!s.data.offercourseid){this.selectedPageView="usuarios_matriculados";return}const i=s.data.offercourseid;try{const o=await gu(i);if(!o||Array.isArray(o)&&o.length===0){this.selectedPageView="usuarios_matriculados";return}let a=null;s.data&&s.data.courseid?a=s.data.courseid:Array.isArray(o)&&o.length>0&&o[0]&&o[0].error===!1&&o[0].data&&o[0].data.courses&&o[0].data.courses.length>0&&(a=o[0].data.courses[0].courseid),a||(a=i);let u=null;s.data&&s.data.course_context_id?u=s.data.course_context_id:u=a;const c={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${a}`,agrupamentos:`/group/groupings.php?id=${a}`,visao_geral:`/user/index.php?id=${a}`,permissoes:`/admin/roles/permissions.php?contextid=${u}`,outros_usuarios:`/enrol/otherusers.php?id=${a}`,verificar_permissoes:`/admin/roles/check.php?contextid=${u}`};c[e]?window.location.href=c[e]:this.selectedPageView="usuarios_matriculados"}catch{this.selectedPageView="usuarios_matriculados"}}catch{this.selectedPageView="usuarios_matriculados"}},async handleEnrolmentSuccess(){const e=this.offerclassid||this.$route.params.offerclassid;e&&await this.loadRegisteredUsers(e)},async loadRoles(){try{const e=this.offerclassid||this.$route.params.offerclassid;try{const t=await Lr(parseInt(e));if(t&&t.data&&t.data.offercourseid){const s=t.data.offercourseid;try{const i=await ta(s);i&&Array.isArray(i)&&(this.roleOptions=i.map(o=>({id:String(o.id),name:o.name})))}catch(i){console.error("Erro ao buscar papéis do curso:",i)}}}catch(t){console.error("Erro ao obter detalhes da turma:",t)}}catch(e){console.error("Erro geral em loadRoles:",e)}},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",state:e.state||0,stateName:e.stateName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(o=>o.id===String(e.roleid));i&&(t=i.name)}if(!t){const i=this.offerclassid||this.$route.params.offerclassid;if(i){await this.loadRegisteredUsers(i),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].state=e.status,e.status===1?this.enrolments[s].stateName="Ativo":e.status===0&&(this.enrolments[s].stateName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else{const i=this.offerclassid||this.$route.params.offerclassid;i&&await this.loadRegisteredUsers(i)}}else{const t=this.offerclassid||this.$route.params.offerclassid;t&&await this.loadRegisteredUsers(t)}this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){const e=this.offerclassid||this.$route.params.offerclassid;e?this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,state:e.state,stateName:e.stateName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},generateCertificate(e){e.status},async confirmeBulkDeleteEnrollment(){try{this.loading=!0;const e=[];for(const i of this.selectedUsers){const o=this.enrolments.find(a=>a.id===i);o&&o.offeruserenrolid?e.push(o.offeruserenrolid):console.error(`Não foi possível encontrar o ID da matrícula para o usuário ID ${i}`)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await rE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,o=s.length-i;if(i>0){this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${o>0?` ${o} matrícula(s) não puderam ser canceladas.`:""}`);const a=this.offerclassid||this.$route.params.offerclassid;a&&await this.loadRegisteredUsers(a),this.selectedUsers=[]}else this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else{this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`);const i=this.offerclassid||this.$route.params.offerclassid;i&&await this.loadRegisteredUsers(i),this.selectedUsers=[]}this.showBulkDeleteEnrollmentModal=!1}catch(e){this.error=e.message,this.showErrorMessage("Ocorreu um erro ao cancelar a matrícula. Por favor, tente novamente.")}finally{this.loading=!1}},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}let e=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(t=>typeof t=="number"))e=this.selectedUsers;else if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(t=>t&&typeof t=="object"))e=this.selectedUsers.filter(t=>t&&(t.id||t.userId)).map(t=>t.id||t.userId),console.log("IDs extraídos de objetos de usuário:",e);else try{e=this.selectedUsers.filter(t=>t!=null).map(t=>typeof t=="number"?t:typeof t=="object"&&t!==null?t.id||t.userId:null).filter(t=>t!=null),console.log("IDs extraídos com método alternativo:",e)}catch(t){console.error("Erro ao extrair IDs:",t)}if(e.length===0){this.showErrorMessage("Não foi possível enviar mensagem. Nenhum usuário válido selecionado.");return}this.showSendMessageModal(e)},showSendMessageModal(e){if(typeof window.require!="function"){console.error("Função require não disponível"),this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{console.error("Erro ao carregar o módulo core_message/message_send_bulk:",t),this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}console.log("Escrevendo anotação para usuários:",this.selectedUsers);let e=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))e=this.selectedUsers,console.log("selectedUsers é um array de IDs:",e);else if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>s&&typeof s=="object"))e=this.selectedUsers.filter(s=>s&&(s.id||s.userId)).map(s=>s.id||s.userId),console.log("IDs extraídos de objetos de usuário:",e);else try{e=this.selectedUsers.filter(s=>s!=null).map(s=>typeof s=="number"?s:typeof s=="object"&&s!==null?s.id||s.userId:null).filter(s=>s!=null)}catch(s){console.error("Erro ao extrair IDs:",s)}if(e.length===0){this.showErrorMessage("Não foi possível escrever anotação. Nenhum usuário válido selecionado.");return}const t=this.classDetails.courseid;if(!t){this.showErrorMessage("Não foi possível escrever anotação. ID do curso não disponível.");return}this.showAddNoteModal(t,e)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(o=>(o.getRoot().on("hidden.bs.modal",()=>{console.log("Modal de anotações fechado"),this.selectedBulkAction=""}),o)).catch(o=>{console.error("Erro ao mostrar o modal de anotações:",o),this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{console.error(s),this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(o=>o.id===s);if(i){const o={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.statusName||i.status||"",Nota:i.grade||"",Estado:i.stateName||""};t.push(o)}}else for(const s of this.selectedUsers){if(!s){console.warn("Usuário inválido encontrado:",s);continue}if(typeof s=="number"){const i=this.enrolments.find(o=>o.id===s);if(i){const o={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.statusName||i.status||"",Nota:i.grade||"",Estado:i.stateName||""};t.push(o)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.statusName||s.status||"",Nota:s.grade||"",Estado:s.stateName||""};t.push(i)}}if(t.length===0){if(console.error("Nenhum dado disponível para download após processamento"),this.selectedUsers.length>0&&this.enrolments&&this.enrolments.length>0){const s=this.selectedUsers.map((i,o)=>({ID:i,Índice:o+1,Selecionado:"Sim"}));if(s.length>0){switch(e){case"csv":this.downloadCSV(s);break;case"xlsx":this.downloadXLSX(s);break;case"html":this.downloadHTML(s);break;case"json":this.downloadJSON(s);break;default:this.showErrorMessage("Formato de download não suportado.");break}return}}this.showErrorMessage("Nenhum dado disponível para download.");return}try{switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}}catch(s){console.error("Erro ao fazer download:",s),this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(f=>f.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),o=t+[i.join(","),...e.map(f=>s.map(m=>{const p=f[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([o],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(f=>{const m=c[f]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),o=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(o),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length!==0)try{const t=Object.keys(e[0]),s=[];for(let T=0;T<t.length;T++){const oe=t[T].replace(/([A-Z])/g," $1").replace(/^./,Q=>Q.toUpperCase()).trim();s.push(oe)}let i="";for(let T=0;T<s.length;T++)i+="<th>"+s[T]+"</th>";let o="";for(let T=0;T<e.length;T++){let oe="<tr>";for(let Q=0;Q<t.length;Q++)oe+="<td>"+(e[T][t[Q]]||"")+"</td>";oe+="</tr>",o+=oe}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",f="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",v='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+f+i+m+o+p+v+w,k=new Blob([D],{type:"text/html;charset=utf-8;"}),U=URL.createObjectURL(k),te=document.createElement("a");te.setAttribute("href",U),te.setAttribute("download","usuarios_matriculados.html"),te.style.visibility="hidden",document.body.appendChild(te),te.click(),document.body.removeChild(te),URL.revokeObjectURL(U),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),o=document.createElement("a");o.setAttribute("href",i),o.setAttribute("download","usuarios_matriculados.json"),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(i)},downloadODS(e){if(e.length!==0)try{const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(f=>{const m=s.map(p=>{const v=f[p]||"";return'"'+String(v).replace(/"/g,'""')+'"'});i.push(m.join(","))});const o=t+i.join(`
`),a=new Blob([o],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadPDF(e){if(e.length!==0)try{this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(console.log("Editando matrículas dos usuários:",this.selectedUsers),this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){console.log("Matrículas editadas em lote com sucesso:",e),this.showSuccessMessage(e.message||"Matrículas editadas com sucesso.");const t=this.offerclassid||this.$route.params.offerclassid;t&&await this.loadRegisteredUsers(t),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},hx={id:"offer-manager-component",class:"offer-manager"},px={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},mx={style:{width:"240px"}},gx={class:"filter-buttons"},vx={key:0,class:"alert alert-danger"},_x={class:"table-container"},yx={class:"checkbox-container"},bx=["checked","indeterminate"],wx={class:"checkbox-container"},Ex=["checked","onChange"],Cx=["href","title"],Dx={class:"user-name-link"},xx={class:"progress-container"},Sx={class:"progress-text"},Ox={class:"state-container"},Nx={class:"state-actions"},Ix=["onClick"],Ax=["onClick"],Tx={class:"selected-users-actions"},Mx={class:"bulk-actions-container"},Px={key:1,class:"bottom-enroll-button"};function kx(e,t,s,i,o,a){var Pe,ae,We;const u=X("BackButton"),c=X("PageHeader"),f=X("HierarchicalSelect"),m=X("CustomButton"),p=X("Autocomplete"),v=X("FilterGroup"),w=X("FilterRow"),D=X("FilterTag"),k=X("FilterTags"),U=X("FilterSection"),te=X("UserAvatar"),T=X("RoleSelector"),oe=X("CustomTable"),Q=X("Pagination"),we=X("EnrollmentDetailsModal"),Y=X("EnrolmentModalNew"),he=X("EditEnrollmentModal"),be=X("BulkEditEnrollmentModal"),Te=X("BulkDeleteEnrollmentModal"),le=X("LFLoading"),ie=X("Toast");return N(),I("div",hx,[A(c,{title:"Usuários matriculados"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),h("div",px,[h("div",mx,[A(f,{modelValue:o.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=J=>o.selectedPageView=J),options:o.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!o.classDetails||((Pe=o.classDetails)==null?void 0:Pe.operational_cycle)!==2?(N(),Pt(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):ce("",!0)]),A(U,{"has-active-tags":a.hasActiveFilters,title:""},{tags:ye(()=>[A(k,null,{default:ye(()=>[(N(!0),I(Fe,null,vt(o.appliedFilters.name,(J,$e)=>(N(),Pt(D,{key:"name-"+$e,onRemove:ut=>a.removeFilter("name",$e)},{default:ye(()=>[nt(" Nome: "+G(J.label),1)]),_:2},1032,["onRemove"]))),128)),(N(!0),I(Fe,null,vt(o.appliedFilters.cpf,(J,$e)=>(N(),Pt(D,{key:"cpf-"+$e,onRemove:ut=>a.removeFilter("cpf",$e)},{default:ye(()=>[nt(" CPF: "+G(J.label),1)]),_:2},1032,["onRemove"]))),128)),(N(!0),I(Fe,null,vt(o.appliedFilters.email,(J,$e)=>(N(),Pt(D,{key:"email-"+$e,onRemove:ut=>a.removeFilter("email",$e)},{default:ye(()=>[nt(" E-mail: "+G(J.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})]),default:ye(()=>[A(w,{inline:!0},{default:ye(()=>[A(v,{label:"Filtrar por nome"},{default:ye(()=>[A(p,{modelValue:o.selectedName,"onUpdate:modelValue":t[1]||(t[1]=J=>o.selectedName=J),items:a.filteredNameOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleNameSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,{label:"Filtrar por CPF"},{default:ye(()=>[A(p,{modelValue:o.selectedCpf,"onUpdate:modelValue":t[2]||(t[2]=J=>o.selectedCpf=J),items:a.filteredCpfOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleCpfSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,{label:"Filtrar por E-mail"},{default:ye(()=>[A(p,{modelValue:o.selectedEmail,"onUpdate:modelValue":t[3]||(t[3]=J=>o.selectedEmail=J),items:a.filteredEmailOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleEmailSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,null,{default:ye(()=>[h("div",gx,[A(m,{variant:"primary",label:"Filtrar",onClick:a.applyFilters},null,8,["onClick"]),A(m,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])])]),_:1})]),_:1})]),_:1},8,["has-active-tags"]),o.error?(N(),I("div",vx,[t[11]||(t[11]=h("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+G(o.error),1)])):ce("",!0),h("div",_x,[A(oe,{headers:o.tableHeaders,items:o.enrolments,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"header-select":ye(()=>[h("div",yx,[h("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[4]||(t[4]=(...J)=>a.toggleSelectAll&&a.toggleSelectAll(...J)),class:"custom-checkbox"},null,40,bx)])]),"item-select":ye(({item:J})=>[h("div",wx,[h("input",{type:"checkbox",checked:a.isSelected(J.id),onChange:$e=>a.toggleSelectUser(J.id),class:"custom-checkbox"},null,40,Ex)])]),"item-fullName":ye(({item:J})=>[h("a",{class:"user-name-container",href:`/user/view.php?id=${J.id}`,title:"Ver perfil de "+J.fullName},[A(te,{"full-name":J.fullName,size:36},null,8,["full-name"]),h("span",Dx,G(J.fullName),1)],8,Cx)]),"item-email":ye(({item:J})=>[nt(G(J.email),1)]),"item-cpf":ye(({item:J})=>[nt(G(J.cpf),1)]),"item-roles":ye(({item:J})=>[A(T,{userId:J.id,offeruserenrolid:J.offeruserenrolid,currentRole:J.roles,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":ye(({item:J})=>[nt(G(J.groups),1)]),"item-startDate":ye(({item:J})=>[nt(G(J.startDate),1)]),"item-endDate":ye(({item:J})=>[nt(G(J.endDate),1)]),"item-deadline":ye(({item:J})=>[nt(G(J.deadline),1)]),"item-progress":ye(({item:J})=>[h("div",xx,[h("div",{class:"progress-bar",style:ls({width:J.progress})},null,4),h("span",Sx,G(J.progress),1)])]),"item-status":ye(({item:J})=>[nt(G(J.statusName),1)]),"item-grade":ye(({item:J})=>[nt(G(J.grade),1)]),"item-state":ye(({item:J})=>[h("div",Ox,[h("span",{class:pe(["state-tag badge",J.state===0?"badge-success":"badge-danger"])},G(J.stateName),3),h("div",Nx,[h("button",{class:"btn-information",onClick:$e=>a.showEnrollmentDetails(J),title:"Informações da matrícula"},t[12]||(t[12]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[h("circle",{cx:"12",cy:"12",r:"10"}),h("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),h("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,Ix),h("button",{class:"btn-settings",onClick:$e=>a.editUser(J),title:"Editar matrícula"},t[13]||(t[13]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[h("circle",{cx:"12",cy:"12",r:"3"}),h("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,Ax)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(Q,{"current-page":o.currentPage,"onUpdate:currentPage":t[5]||(t[5]=J=>o.currentPage=J),"per-page":o.perPage,"onUpdate:perPage":t[6]||(t[6]=J=>o.perPage=J),total:o.totalEnrolments,loading:o.loading},null,8,["current-page","per-page","total","loading"]),h("div",Tx,[h("div",Mx,[t[15]||(t[15]=h("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),bt(h("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[7]||(t[7]=J=>o.selectedBulkAction=J),onChange:t[8]||(t[8]=(...J)=>a.handleBulkAction&&a.handleBulkAction(...J))},t[14]||(t[14]=[uy('<option value="" data-v-1b27cded>Escolher...</option><optgroup label="Comunicação" data-v-1b27cded><option value="message" data-v-1b27cded>Enviar uma mensagem</option><option value="note" data-v-1b27cded>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-1b27cded><option value="download_csv" data-v-1b27cded> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-1b27cded>Microsoft excel (.xlsx)</option><option value="download_html" data-v-1b27cded>Tabela HTML</option><option value="download_json" data-v-1b27cded> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-1b27cded>OpenDocument (.ods)</option><option value="download_pdf" data-v-1b27cded> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições manuais" data-v-1b27cded><option value="edit_enrolment" data-v-1b27cded> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-1b27cded> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Zl,o.selectedBulkAction]])])]),!o.classDetails||((ae=o.classDetails)==null?void 0:ae.operational_cycle)!==2?(N(),I("div",Px,[A(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):ce("",!0),A(we,{show:o.showEnrollmentModal,user:o.selectedUser,"course-name":((We=o.classDetails)==null?void 0:We.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),A(Y,{show:o.showEnrolmentModal,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),roles:o.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),A(he,{show:o.showEditEnrollmentModal,user:o.selectedUser,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),A(be,{show:o.showBulkEditEnrollmentModal,users:o.selectedUsers.map(J=>o.enrolments.find($e=>$e.id===J)).filter(Boolean),offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:t[9]||(t[9]=J=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),A(Te,{show:o.showBulkDeleteEnrollmentModal,users:o.selectedUsers.map(J=>o.enrolments.find($e=>$e.id===J)).filter(Boolean),offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:t[10]||(t[10]=J=>o.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),A(le,{"is-loading":o.loading},null,8,["is-loading"]),A(ie,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const Rx=ze(fx,[["render",kx],["__scopeId","data-v-1b27cded"]]),yV="",Vx={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},Fx={class:"table-responsive"},Ux={class:"table"},Lx={key:0,class:"expand-column"},Bx=["onClick","data-value"],$x={key:0,class:"sort-icon"},jx={key:0},Hx={key:0,class:"expand-column"},qx=["onClick","title"],zx=["colspan"],Wx={class:"expanded-content"},Gx={key:1},Kx=["colspan"];function Qx(e,t,s,i,o,a){return N(),I("div",Fx,[h("table",Ux,[h("thead",null,[h("tr",null,[s.expandable?(N(),I("th",Lx)):ce("",!0),(N(!0),I(Fe,null,vt(s.headers,u=>(N(),I("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:pe({sortable:u.sortable}),"data-value":u.value},[nt(G(u.text)+" ",1),u.sortable?(N(),I("span",$x,[h("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):ce("",!0)],10,Bx))),128))])]),s.items.length>0?(N(),I("tbody",jx,[(N(!0),I(Fe,null,vt(s.items,(u,c)=>(N(),I(Fe,{key:u.id},[h("tr",{class:pe({expanded:o.expandedRows.includes(u.id)})},[s.expandable?(N(),I("td",Hx,[h("button",{class:"btn-expand",onClick:f=>a.toggleExpand(u.id),title:o.expandedRows.includes(u.id)?"Recolher":"Expandir"},[h("div",{class:pe(["icon-container",{"is-expanded":o.expandedRows.includes(u.id)}])},t[0]||(t[0]=[h("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[h("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),h("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,qx)])):ce("",!0),(N(!0),I(Fe,null,vt(s.headers,f=>(N(),I("td",{key:`${u.id}-${f.value}`},[Vt(e.$slots,"item-"+f.value,{item:u},()=>[nt(G(u[f.value]),1)],!0)]))),128))],2),s.expandable?(N(),I("tr",{key:0,class:pe(["expanded-row",{"is-visible":o.expandedRows.includes(u.id)}])},[h("td",{colspan:s.headers.length+1},[h("div",Wx,[Vt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,zx)],2)):ce("",!0)],64))),128))])):(N(),I("tbody",Gx,[h("tr",null,[h("td",{colspan:s.headers.length+(s.expandable?1:0)},[Vt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=h("div",{class:"empty-state"},[h("span",null,"Não existem registros")],-1))],!0)],8,Kx)])]))])])}const Yx=ze(Vx,[["render",Qx],["__scopeId","data-v-05038124"]]),bV="",Zx={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},Jx={class:"text-editor-container"},Xx={class:"editor-toolbar"},eS={class:"toolbar-group"},tS=["disabled"],sS=["disabled"],rS=["disabled"],oS=["disabled"],nS={class:"toolbar-group"},iS=["disabled"],aS=["disabled"],lS=["contenteditable"],uS=["rows","placeholder","disabled"];function cS(e,t,s,i,o,a){return N(),I("div",Jx,[s.label?(N(),I("label",{key:0,class:pe(["filter-label",{disabled:s.disabled}])},G(s.label),3)):ce("",!0),h("div",{class:pe(["editor-container",{disabled:s.disabled}])},[h("div",Xx,[h("div",eS,[h("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[h("i",{class:"fas fa-bold"},null,-1)]),8,tS),h("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[h("i",{class:"fas fa-italic"},null,-1)]),8,sS),h("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[h("i",{class:"fas fa-underline"},null,-1)]),8,rS),h("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[h("i",{class:"fas fa-strikethrough"},null,-1)]),8,oS)]),t[16]||(t[16]=h("div",{class:"toolbar-divider"},null,-1)),h("div",nS,[h("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[h("i",{class:"fas fa-list-ul"},null,-1)]),8,iS),h("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[h("i",{class:"fas fa-list-ol"},null,-1)]),8,aS)])]),o.showHtmlSource?bt((N(),I("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>o.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,uS)),[[Fs,o.htmlContent]]):(N(),I("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,lS))],2)])}const fp=ze(Zx,[["render",cS],["__scopeId","data-v-57cfe760"]]),wV="",EV="",dS={name:"AddCourseModal",components:{CustomInput:Ln,CustomButton:Bo,CustomTable:po,Pagination:mo,Autocomplete:jn,FilterTag:$n},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await a1(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await ea(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Un("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const o=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await ip(this.offerId,o,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const f in a)if(Array.isArray(a[f])){c=a[f],u={page:1,total_pages:1};break}}}catch(f){console.error("Erro ao processar resposta:",f)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(v=>v.id===p.id)&&!this.selectedCoursesPreview.some(v=>v.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?ip(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let o=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?o=i[0].data.courses||[]:Array.isArray(i[0].data)?o=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(o=i[0].data.data):o=i:i&&typeof i=="object"&&(i.data&&i.data.courses?o=i.data.courses:i.courses?o=i.courses:i.data&&Array.isArray(i.data)&&(o=i.data)),o.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){o=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}o&&o.length>0&&o.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},fS={class:"modal-header"},hS={class:"modal-body"},pS={class:"search-section"},mS={class:"search-group"},gS={class:"search-group"},vS={class:"table-container"},_S={key:0,class:"empty-preview-message"},yS={class:"action-buttons"},bS=["onClick"],wS={class:"modal-footer"};function ES(e,t,s,i,o,a){const u=X("Autocomplete"),c=X("CustomTable"),f=X("Pagination"),m=X("CustomButton");return s.modelValue?(N(),I("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[h("div",{class:"modal-content",onClick:t[5]||(t[5]=Ft(()=>{},["stop"]))},[h("div",fS,[t[8]||(t[8]=h("h2",null,"Adicionar curso",-1)),h("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",hS,[t[11]||(t[11]=h("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),h("div",pS,[h("div",mS,[A(u,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>o.selectedCategory=p),items:o.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:o.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),h("div",gS,[A(u,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>o.selectedCourse=p),items:o.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!o.selectedCategory,loading:o.loadingCourses||o.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),h("div",vS,[o.selectedCoursesPreview.length===0?(N(),I("div",_S,t[9]||(t[9]=[h("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(N(),Pt(c,{key:1,headers:o.tableHeaders,items:a.filteredCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-actions":ye(({item:p})=>[h("div",yS,[h("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[h("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,bS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),o.selectedCoursesPreview.length>0?(N(),Pt(f,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>o.currentPage=p),a.handlePageChange],"per-page":o.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>o.perPage=p),a.handlePerPageChange],total:o.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):ce("",!0)]),h("div",wS,[A(m,{variant:"primary",label:"Confirmar",disabled:o.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),A(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):ce("",!0)}const CS=ze(dS,[["render",ES],["__scopeId","data-v-bc4b8014"]]),CV="",DV="",DS={name:"DuplicateClassModal",components:{Autocomplete:jn,CustomTable:po,Pagination:mo},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,o)=>{const a=i[this.sortBy],u=o[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Un("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const o=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await p1(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,v=String(p)===String(o),w=m.offercourseid||m.id,D=String(w)!==String(c),k=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return v&&D&&k}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(v=>String(v.value)===String(p))});const f=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...f]:this.targetCourseOptions=f,this.hasMoreCourses=f.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const o=parseInt(i.value,10);if(isNaN(o)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await h1(t,o);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:o,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},xS={class:"modal-header"},SS={class:"modal-title"},OS={class:"modal-body"},NS={class:"search-section"},IS={class:"search-group"},AS={class:"search-group"},TS={class:"table-container"},MS={key:0,class:"empty-preview-message"},PS={class:"action-buttons"},kS=["onClick"],RS={class:"modal-footer"},VS=["disabled"];function FS(e,t,s,i,o,a){var m;const u=X("Autocomplete"),c=X("CustomTable"),f=X("Pagination");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[h("div",{class:"modal-container",onClick:t[7]||(t[7]=Ft(()=>{},["stop"]))},[h("div",xS,[h("h3",SS,'Duplicar Turma "'+G((m=s.turma)==null?void 0:m.nome)+'"',1),h("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",OS,[t[12]||(t[12]=h("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),h("div",NS,[h("div",IS,[A(u,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>o.selectedCategory=p),items:o.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:o.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),h("div",AS,[A(u,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>o.selectedCourse=p),items:o.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!o.selectedCategory,loading:o.loadingCourses||o.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),h("div",TS,[o.selectedCoursesPreview.length===0?(N(),I("div",MS,t[10]||(t[10]=[h("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(N(),Pt(c,{key:1,headers:o.tableHeaders,items:a.filteredCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-actions":ye(({item:p})=>[h("div",PS,[h("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[h("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,kS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),o.selectedCoursesPreview.length>0?(N(),Pt(f,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>o.currentPage=p),"per-page":o.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>o.perPage=p),total:o.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):ce("",!0)]),h("div",RS,[h("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:o.selectedCoursesPreview.length===0}," Duplicar ",8,VS),h("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):ce("",!0)}const US=ze(DS,[["render",FS],["__scopeId","data-v-15c7cc65"]]),xV="",LS={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},BS=["data-content","aria-label"],$S=["title","aria-label"];function jS(e,t,s,i,o,a){return N(),I("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[h("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,$S)],8,BS)}const Eu=ze(LS,[["render",jS]]),SV="",HS={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:Eu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){try{this.loading=!0;const e=await up(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name})))}catch(e){console.error("Erro ao carregar métodos de inscrição:",e)}finally{this.loading=!1}},handleConfirm(){this.selectedEnrolType&&(console.log("Tipo de inscrição selecionado:",this.selectedEnrolType),this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid}))},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},qS={class:"modal-header"},zS={class:"modal-title"},WS={class:"modal-body"},GS={class:"enrol-type-modal"},KS={class:"form-group mb-3"},QS={class:"label-with-help"},YS={class:"limited-width-input",style:{"max-width":"280px"}},ZS={class:"modal-footer"},JS={class:"footer-buttons"},XS=["disabled"];function eO(e,t,s,i,o,a){const u=X("HelpIcon"),c=X("CustomSelect");return s.show?(N(),I("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=f=>s.closeOnBackdrop?e.$emit("close"):null)},[h("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Ft(()=>{},["stop"]))},[h("div",qS,[h("h3",zS,G(s.title),1),h("button",{class:"modal-close",onClick:t[0]||(t[0]=f=>e.$emit("close"))},t[6]||(t[6]=[h("i",{class:"fas fa-times"},null,-1)]))]),h("div",WS,[h("div",GS,[t[9]||(t[9]=h("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),h("div",KS,[h("div",QS,[t[7]||(t[7]=h("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.\r
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.\r
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),h("div",YS,[A(c,{modelValue:o.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=f=>o.selectedEnrolType=f),options:[{value:"",label:"Selecione um método..."},...o.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),h("div",ZS,[t[10]||(t[10]=h("div",{class:"form-info"},[h("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),h("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),h("div",JS,[h("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...f)=>a.handleConfirm&&a.handleConfirm(...f)),disabled:!o.selectedEnrolType},G(s.confirmButtonText),9,XS),h("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=f=>e.$emit("close"))},G(s.cancelButtonText),1)])])],2)])):ce("",!0)}const tO=ze(HS,[["render",eO],["__scopeId","data-v-1a0e1cb5"]]),sO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBkPSJNNyAxNHMtMSAwLTEtMSAxLTQgNS00IDUgMyA1IDQtMSAxLTEgMUg3em00LTZhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2eiIgZmlsbD0iI2ZmZiIvPg0KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNS4yMTYgMTRBMi4yMzggMi4yMzggMCAwIDEgNSAxM2MwLTEuMzU1LjY4LTIuNzUgMS45MzYtMy43MkE2LjMyNSA2LjMyNSAwIDAgMCA1IDljLTQgMC01IDMtNSA0czEgMSAxIDFoNC4yMTZ6IiBmaWxsPSIjZmZmIi8+DQogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPg0KPC9zdmc+DQo=",rO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTggMmEuNS41IDAgMCAxIC41LjV2NWg1YS41LjUgMCAwIDEgMCAxaC01djVhLjUuNSAwIDAgMS0xIDB2LTVoLTVhLjUuNSAwIDEgMSAwLTFoNXYtNUEuNS41IDAgMCAxIDggMnoiIGZpbGw9IiNmZmYiLz4NCjwvc3ZnPg0K",OV="",oO={name:"NewOfferView",components:{CustomTable:po,CustomSelect:mr,CustomInput:Ln,CustomButton:Bo,Pagination:mo,CollapsibleTable:Yx,PageHeader:na,BackButton:wu,Autocomplete:jn,TextEditor:fp,CustomCheckbox:sa,FilterRow:ra,FilterGroup:oa,FilterTag:$n,FilterTags:ia,AddCourseModal:CS,ConfirmationModal:_u,Toast:Bn,HelpIcon:Eu,DuplicateClassModal:US,EnrolTypeModal:tO,LFLoading:vu},setup(){const e=Ji(),t=ep();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:sO,plus:rO},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,o]of Object.entries(s))if(i.toLowerCase()===t)return o;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,o]of Object.entries(s))if(t.includes(i.toLowerCase()))return o;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await rp(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await pu();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await np("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,this.appliedFilters.course&&this.appliedFilters.category&&this.appliedFilters.onlyActive?await this.loadCourses():this.appliedFilters.course?await this.loadCourses():this.appliedFilters.category&&await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Un("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await ea(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:o,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=o||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.id||c.courseid,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(o=>({value:o.id||o.courseid,label:o.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{if(this.selectedCategory){const t=this.selectedCategory,s=await mu(this.offerId,t);if(s&&s.data&&Array.isArray(s.data)){const i=s.data.filter(o=>o.fullname.toLowerCase().includes(e.toLowerCase()));this.courseOptions=i.map(o=>({value:o.id||o.courseid,label:o.fullname}))}}else{const t=await ap(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.id||s.courseid,label:s.fullname})))}this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const o=await Un(e.label,this.offerId);if(o&&o.data&&o.data.length>0){const a=o.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await mu(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const o=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=o,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...o],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e.label,this.currentPage=1,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCourses(){if(this.offerId)try{this.loading=!0;const e=await ap(this.offerId,this.appliedFilters.course);if(e&&e.data&&e.data.length>0){const t=e.data.map(i=>i.id),s=await ea(this.offerId,{courseIds:t});s&&s.data.courses?this.selectedCourses=s.data.courses.map(i=>({id:i.id||i.courseid,offerCourseId:i.id,name:i.fullname,category:i.category_name||"-",turmasCount:Array.isArray(i.turmas)?i.turmas.length:0,status:i.status===1||i.status==="1"?"Ativo":"Inativo",can_delete:i.can_delete!==void 0?i.can_delete:!0,can_activate:i.can_activate!==void 0?i.can_activate:!0,turmas:Array.isArray(i.turmas)?i.turmas.map(o=>({id:o.id,nome:o.name,enrol_type:o.enrol_type||"-",vagas:o.max_users?o.max_users:"Ilimitado",inscritos:o.enrolled_users||0,dataInicio:o.start_date||"-",dataFim:o.end_date||"-"})):[]})):this.selectedCourses=[]}else this.selectedCourses=[]}catch(e){console.log(e),this.showErrorMessage("Erro ao buscar cursos. Por favor, tente novamente.")}finally{this.loading=!1}},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Un(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const o=await mu(this.offerId,i);if(o&&o.data){const a=o.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await s1(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id,offerid:this.offerId||"0"}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await m1(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(o=>o.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],o=i.turmas.findIndex(a=>a.id===this.selectedClass.id);o!==-1&&(i.turmas[o].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:e.id}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await d1(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseSearch=this.appliedFilters.course),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await ea(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,o=1,a=0;t.data.courses&&({page:i,total_pages:o,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const f=await gu(c.id);let m=[];f&&typeof f=="object"&&f.error===!1&&Array.isArray(f.data)&&f.data.length>0&&(m=f.data.map(p=>{let v=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:v,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:o>0?this.totalItems=o*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([rp(e),np("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const o=i.data;if(this.offer={name:o.name,offerType:o.type,description:o.description,id:o.id,status:o.status},s&&Array.isArray(s.items)){const a=o.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await op(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await op(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const o=`/edit-offer/${this.offerId}`;this.router.replace(o)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await n1(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await o1(this.offerId,s,e);const i=this.selectedCourses.findIndex(o=>o.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await r1(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await pu();if(t&&t.data){const{enabled:s,types:i,default:o}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),o&&!this.isEditing&&(this.offer.offerType=o))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course&&(this.appliedFilters.course="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},nO={id:"new-offer-component",class:"new-offer"},iO={key:0,class:"alert alert-warning"},aO={class:"section-container"},lO={class:"form-row mb-3"},uO={class:"form-group"},cO={class:"input-container"},dO={key:0,class:"form-group"},fO={class:"input-container"},hO={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},pO={class:"form-group"},mO={class:"label-container"},gO={class:"label-with-help"},vO={class:"input-container"},_O={class:"form-group text-editor-container"},yO={class:"limited-width-editor"},bO={key:0,class:"section-title"},wO={key:1,class:"message-container"},EO={key:2},CO={class:"filters-left-group"},DO={class:"filters-right-group"},xO={class:"empty-state"},SO={class:"no-results"},OO=["title"],NO={class:"action-buttons"},IO=["onClick"],AO=["src"],TO=["onClick","disabled","title"],MO=["onClick","disabled","title"],PO={class:"turmas-container"},kO={class:"turmas-content"},RO={key:0},VO={class:"turma-col"},FO=["title"],UO={class:"turma-col"},LO={class:"turma-col"},BO={class:"turma-col"},$O={class:"turma-col"},jO={class:"turma-col"},HO={class:"turma-col"},qO={class:"action-buttons"},zO=["onClick"],WO=["src"],GO=["onClick"],KO=["onClick"],QO=["title","onClick"],YO=["onClick","disabled","title"],ZO={key:1,class:"empty-turmas"},JO={class:"d-flex justify-content-between align-items-center"},XO={class:"actions-container offer-actions"};function eN(e,t,s,i,o,a){var Pe,ae,We,J,$e,ut,Oe,Ee,Ut,Xt,_t;const u=X("BackButton"),c=X("PageHeader"),f=X("CustomInput"),m=X("CustomSelect"),p=X("HelpIcon"),v=X("Autocomplete"),w=X("TextEditor"),D=X("FilterGroup"),k=X("CustomCheckbox"),U=X("FilterTag"),te=X("FilterTags"),T=X("FilterRow"),oe=X("CollapsibleTable"),Q=X("Pagination"),we=X("CustomButton"),Y=X("AddCourseModal"),he=X("ConfirmationModal"),be=X("DuplicateClassModal"),Te=X("EnrolTypeModal"),le=X("LFLoading"),ie=X("Toast");return N(),I("div",nO,[A(c,{title:o.isEditing?`Editar oferta: ${o.offer.name}`:"Nova oferta"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),o.showWarning?(N(),I("div",iO,t[21]||(t[21]=[h("i",{class:"fas fa-exclamation-triangle"},null,-1),nt(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):ce("",!0),h("div",aO,[t[27]||(t[27]=h("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),h("div",lO,[h("div",uO,[t[22]||(t[22]=h("div",{class:"label-container"},[h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Nome da Oferta"),h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),h("div",cO,[A(f,{modelValue:o.offer.name,"onUpdate:modelValue":t[0]||(t[0]=de=>o.offer.name=de),placeholder:"Oferta 0001",width:280,required:"","has-error":o.formErrors.name.hasError,"error-message":o.formErrors.name.message,onValidate:t[1]||(t[1]=de=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),o.typeOptionsEnabled?(N(),I("div",dO,[t[23]||(t[23]=h("div",{class:"label-container"},[h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Tipo da oferta")])],-1)),h("div",fO,[A(m,{modelValue:o.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=de=>o.offer.offerType=de),options:o.offerTypeOptions,width:280},null,8,["modelValue","options"])])])):ce("",!0)]),h("div",hO,[h("div",pO,[h("div",mO,[h("div",gO,[t[24]||(t[24]=h("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),h("div",vO,[A(v,{class:"autocomplete-audiences",modelValue:o.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=de=>o.selectedAudiences=de),t[4]||(t[4]=de=>a.validateField("audiences"))],items:o.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":o.formErrors.audiences.hasError,"error-message":o.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),h("div",_O,[t[26]||(t[26]=h("div",{class:"label-container"},[h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Descrição da oferta")])],-1)),h("div",yO,[A(w,{modelValue:o.offer.description,"onUpdate:modelValue":t[5]||(t[5]=de=>o.offer.description=de),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),h("div",{class:pe(["section-container",{"no-title-section":!o.isEditing}])},[o.isEditing?(N(),I("h2",bO,"CURSOS")):ce("",!0),!a.canManageCourses||!o.isEditing?(N(),I("div",wO,t[28]||(t[28]=[h("div",{class:"lock-message"},[h("i",{class:"fas fa-lock lock-icon"}),h("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):ce("",!0),o.isEditing&&a.canManageCourses?(N(),I("div",EO,[A(T,{inline:"",class:"courses-filter-row"},{default:ye(()=>[h("div",CO,[A(D,null,{default:ye(()=>[A(v,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=de=>o.selectedCategory=de),items:o.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),A(D,null,{default:ye(()=>[A(v,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=de=>o.selectedCourse=de),items:o.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:o.loadingCourses||o.loadingMoreCourses,"no-results-text":o.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),A(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:ye(()=>[A(k,{modelValue:o.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=de=>o.inputFilters.onlyActive=de),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),o.appliedFilters.course?(N(),Pt(te,{key:0,class:"mt-3"},{default:ye(()=>[A(U,{onRemove:t[9]||(t[9]=de=>a.removeFilter("course"))},{default:ye(()=>[nt(" Curso: "+G(o.appliedFilters.course),1)]),_:1})]),_:1})):ce("",!0)]),h("div",DO,[h("button",{class:"btn btn-primary",onClick:t[10]||(t[10]=(...de)=>a.showAddCourseModal&&a.showAddCourseModal(...de))}," Adicionar curso ")])]),_:1}),A(oe,{headers:o.courseTableHeaders,items:o.selectedCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":ye(()=>[h("div",xO,[h("span",SO,G(o.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":ye(({item:de})=>[h("span",{title:de.name},G(de.name.length>50?de.name.slice(0,50)+"...":de.name),9,OO)]),"item-actions":ye(({item:de})=>[h("div",NO,[h("button",{class:"btn-action btn-add",onClick:Ke=>a.addTurma(de),title:"Adicionar turma"},[h("img",{src:o.icons.plus,alt:"Adicionar turma"},null,8,AO)],8,IO),h("button",{class:pe(["btn-action",de.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:Ke=>a.toggleCourseStatus(de),disabled:de.status==="Inativo"&&!de.can_activate||!de.can_activate,title:a.getStatusButtonTitle(de)},[h("i",{class:pe(de.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,TO),h("button",{class:"btn-action btn-delete",onClick:Ke=>a.deleteCourse(de),disabled:!de.can_delete,title:de.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[h("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,MO)])]),"expanded-content":ye(({item:de})=>[h("div",PO,[t[34]||(t[34]=h("div",{class:"turmas-header"},[h("div",{class:"turma-col"},"NOME DA TURMA"),h("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),h("div",{class:"turma-col"},"QTD. DE VAGAS"),h("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),h("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),h("div",{class:"turma-col"},"DATA FIM DA TURMA"),h("div",{class:"turma-col"},"AÇÕES")],-1)),h("div",kO,[de.turmas&&de.turmas.length>0?(N(),I("div",RO,[(N(!0),I(Fe,null,vt(de.turmas,(Ke,fs)=>(N(),I("div",{class:"turmas-row",key:fs},[h("div",VO,[h("span",{title:Ke.nome},G(Ke.nome.length>20?Ke.nome.slice(0,20)+"...":Ke.nome),9,FO)]),h("div",UO,G(a.getEnrolTypeLabel(Ke.enrol_type)),1),h("div",LO,G(Ke.vagas),1),h("div",BO,G(Ke.inscritos),1),h("div",$O,G(Ke.dataInicio),1),h("div",jO,G(Ke.dataFim),1),h("div",HO,[h("div",qO,[h("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(Ke),title:"Usuários Matriculados"},[h("img",{src:o.icons.users,alt:"Usuários Matriculados"},null,8,WO)],8,zO),h("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(Ke),title:"Editar"},t[30]||(t[30]=[h("i",{class:"fas fa-pencil-alt"},null,-1)]),8,GO),h("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(Ke,de),title:"Duplicar Turma"},t[31]||(t[31]=[h("i",{class:"fas fa-copy"},null,-1)]),8,KO),h("button",{class:pe(["btn-action",Ke.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:Ke.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(Ke)},[h("i",{class:pe(Ke.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,QO),h("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(de,fs),disabled:!Ke.can_delete,title:Ke.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[h("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,YO)])])]))),128))])):(N(),I("div",ZO,t[33]||(t[33]=[h("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),A(Q,{ref:"pagination","current-page":o.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=de=>o.currentPage=de),a.handlePageChange],"per-page":o.perPage,"onUpdate:perPage":t[12]||(t[12]=de=>o.perPage=de),total:o.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):ce("",!0)],2),t[36]||(t[36]=h("hr",null,null,-1)),h("div",JO,[t[35]||(t[35]=h("div",{class:"required-fields-message"},[h("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),h("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),h("div",XO,[A(we,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),A(we,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),A(Y,{modelValue:o.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=de=>o.showAddCourseModalVisible=de),"offer-id":o.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),A(he,{show:o.showCourseStatusModal,title:((Pe=o.selectedCourse)==null?void 0:Pe.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((ae=o.selectedCourse)==null?void 0:ae.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((We=o.selectedCourse)==null?void 0:We.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((J=o.selectedCourse)==null?void 0:J.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(($e=o.selectedCourse)==null?void 0:$e.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=de=>o.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),A(he,{show:o.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=de=>o.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),A(he,{show:o.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=de=>o.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),A(he,{show:o.showClassStatusModal,title:((ut=o.selectedClass)==null?void 0:ut.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((Oe=o.selectedClass)==null?void 0:Oe.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((Ee=o.selectedClass)==null?void 0:Ee.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Ut=o.selectedClass)==null?void 0:Ut.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((Xt=o.selectedClass)==null?void 0:Xt.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=de=>o.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),A(be,{show:o.showDuplicateClassModal,turma:o.classToDuplicate,parentCourse:o.classToDuplicateParentCourse,offerId:o.offerId,onClose:t[18]||(t[18]=de=>o.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=de=>o.loading=de),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),A(Te,{show:o.showEnrolTypeModal,offercourseid:(_t=o.selectedCourseForClass)==null?void 0:_t.offerCourseId,offerid:o.offerId||"0",onClose:t[20]||(t[20]=de=>o.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),A(le,{"is-loading":o.loading},null,8,["is-loading"]),A(ie,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const hp=ze(oO,[["render",eN],["__scopeId","data-v-3dc26373"]]),NV="",tN={name:"NewClassView",components:{CustomInput:Ln,CustomSelect:mr,CustomButton:Bo,PageHeader:na,BackButton:wu,Autocomplete:jn,TextEditor:fp,CustomCheckbox:sa,FilterRow:ra,FilterGroup:oa,Toast:Bn,HelpIcon:Eu,FilterTag:$n,FilterTags:ia},setup(){const e=Ji(),t=ep();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:[Number,String],required:!0},classid:{type:[Number,String],required:!1,default:null},offerid:{type:[Number,String],required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,classId:null,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherOptions:[],extensionSituations:[],reenrolSituations:[],roleOptions:[],situationOptions:[],extensionSituationOptions:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(this.offercourseid?(this.classData.offercourseid=parseInt(this.offercourseid),console.log("offercourseid definido a partir da prop:",this.classData.offercourseid)):this.route.params.offercourseid?(this.classData.offercourseid=parseInt(this.route.params.offercourseid),console.log("offercourseid definido a partir dos parâmetros da rota:",this.classData.offercourseid)):this.route.query.offercourseid&&(this.classData.offercourseid=parseInt(this.route.query.offercourseid),console.log("offercourseid definido a partir da query string:",this.classData.offercourseid)),this.classData.offercourseid||console.error("offercourseid não foi definido. Isso causará problemas ao salvar a turma."),this.classid)this.isEditing=!0,this.classId=parseInt(this.classid),console.log("Modo de edição ativado para a turma ID (via props):",this.classId);else if(this.route.query.classid&&this.route.query.edit==="true"){this.isEditing=!0,this.classId=parseInt(this.route.query.classid),console.log("Modo de edição ativado para a turma ID (via query):",this.classId);const e=this.route.query.offerid||this.offerid;this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classId,offerid:e||"0"}})})}try{console.log("Carregando dados iniciais..."),await this.loadInitialData(),this.isEditing&&this.classId?(console.log("Carregando dados específicos da turma..."),await this.loadClassData(),console.log("Sincronizando situações após carregar dados da turma..."),this.syncSituations(),this.$nextTick(()=>{console.log("Reiniciando o componente após carregar todos os dados"),this.restartComponent()})):this.syncSituations()}catch(e){console.error("Erro durante a inicialização do componente:",e),this.showErrorMessage("Erro ao inicializar o componente. Alguns dados podem estar incompletos.")}},mounted(){window.scrollTo(0,0)},computed:{maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>0?e:null}return null}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"classData.startdate":function(){this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod")},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod")},"classData.optional_fields.enableenddate":function(e){!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.startdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates()},"classData.optional_fields.enableenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;try{const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;const o=Math.abs(i-s);return Math.ceil(o/(1e3*60*60*24))}catch(s){return console.error("Erro ao calcular diferença entre datas:",s),0}},syncSituations(){this.extensionSituations=this.classData.optional_fields.extensionallowedsituations.map(e=>{const t=parseInt(e);return t===0?{value:t,label:"Inscrito"}:this.extensionSituationOptions.find(s=>s.value===t)||{value:t,label:`Situação ${e}`}}),this.reenrolSituations=this.classData.optional_fields.reenrolmentsituations.map(e=>this.situationOptions.find(t=>t.value===parseInt(e))||{value:parseInt(e),label:`Situação ${e}`})},async loadInitialData(){try{this.loading=!0,await this.loadOfferCourse(),await this.loadTeachers(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch(e){console.error("Erro ao carregar dados iniciais:",e),this.showErrorMessage("Alguns dados não puderam ser carregados. Valores padrão serão usados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await u1(this.offercourseid);!e.error&&e.data&&(this.offerCourse=e.data)}catch{this.offerCourse={id:this.offercourseid,offerid:null}}},async loadTeachers(){var e;try{const t=await lp(this.offercourseid,this.classId);!t.error&&t.data?this.teacherOptions=t.data.map(s=>({value:s.id,label:s.fullname})):this.showErrorMessage(((e=t.exception)==null?void 0:e.message)||"Erro desconhecido ao carregar professores")}catch{this.showErrorMessage("Não foi possível carregar a lista de professores."),this.teacherOptions=[]}},async loadRoles(){try{const e=await ta(this.offercourseid);let t=e==null?void 0:e.data;if(!e.error&&t){if(this.roleOptions=t.map(s=>({value:s.id,label:s.name})),!this.classData.optional_fields.roleid){const s=this.roleOptions.find(i=>i.value===5);this.classData.optional_fields.roleid=(s==null?void 0:s.value)??this.roleOptions[0].value}return}}catch(e){console.error("Erro ao carregar papéis:",e)}},async loadSituations(){try{try{const e=await f1();if(e&&Array.isArray(e)){const t=[6,7,8,4,5],s=e.filter(o=>t.includes(o.id));this.situationOptions=s.map(o=>({value:o.id,label:o.name.charAt(0).toUpperCase()+o.name.slice(1).toLowerCase()})),this.situationOptions.length===0&&(this.situationOptions=[{value:6,label:"Reprovado"},{value:7,label:"Não concluído"},{value:8,label:"Abandono"},{value:4,label:"Cancelado pelo usuário"},{value:5,label:"Cancelado pelo administrador"}]);const i=[0,1];if(Array.isArray(e)){const o=e.filter(a=>i.includes(a.id));this.extensionSituationOptions=o.map(a=>({value:a.id,label:a.id===0?"Inscrito":a.name.charAt(0).toUpperCase()+a.name.slice(1).toLowerCase()}))}this.extensionSituationOptions.length===0&&(this.extensionSituationOptions=[{value:0,label:"Inscrito"},{value:1,label:"Em andamento"}]);return}}catch(e){console.warn("Não foi possível carregar situações de matrícula:",e)}this.situationOptions=[{value:6,label:"Reprovado"},{value:7,label:"Não concluído"},{value:8,label:"Abandono"},{value:4,label:"Cancelado pelo usuário"},{value:5,label:"Cancelado pelo administrador"}],this.extensionSituationOptions=[{value:0,label:"Inscrito"},{value:1,label:"Em andamento"}]}catch(e){console.error("Erro ao carregar situações de matrícula:",e),this.situationOptions=[{value:6,label:"Reprovado"},{value:7,label:"Não concluído"},{value:8,label:"Abandono"},{value:4,label:"Cancelado pelo usuário"},{value:5,label:"Cancelado pelo administrador"}],this.extensionSituationOptions=[{value:0,label:"Inscrito"},{value:1,label:"Em andamento"}]}},async loadEnrolmentMethods(){try{const e=this.route.query.enrol_type;console.log("Parâmetros da URL:",this.route.query),console.log("Tipo de inscrição encontrado na URL:",e),!this.isEditing&&e?(console.log("Usando tipo de inscrição da URL:",e),this.classData.enrol=e):this.isEditing||(this.classData.enrol="offer_manual",console.log("Usando tipo de inscrição padrão: offer_manual"));try{const t=await up(!0);if(t&&Array.isArray(t)){if(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})),!this.classData.enrol&&this.enrolmentMethods.length>0){const s=this.enrolmentMethods.find(i=>i.value==="offer_manual");s?this.classData.enrol=s.value:this.classData.enrol=this.enrolmentMethods[0].value}return}}catch(t){console.warn("Não foi possível carregar métodos de inscrição:",t)}this.enrolmentMethods=[{value:"offer_manual",label:"Manual"},{value:"offer_self",label:"Autoinscrição"}],this.classData.enrol||(this.classData.enrol="offer_manual")}catch(e){console.error("Erro ao carregar métodos de inscrição:",e),this.enrolmentMethods=[{value:"offer_manual",label:"Manual"},{value:"offer_self",label:"Autoinscrição"}],this.classData.enrol||(this.classData.enrol="offer_manual")}},validate(){Object.keys(this.formErrors).forEach(s=>{this.formErrors[s].hasError=!1}),this.validationAlert.show=!1;let e=!1;return this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate||(this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&!this.classData.optional_fields.enddate&&(this.formErrors.enddate.hasError=!0,e=!0),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0)),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":this.formErrors.startdate.hasError=!this.classData.startdate;break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":this.formErrors.enddate.hasError=this.classData.optional_fields.enableenddate&&!this.classData.optional_fields.enddate;break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const s=this.classData.optional_fields.enableenrolperiod,i=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",o=this.maxEnrolPeriod!==null&&i&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;s&&!i?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):s&&o?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,o=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),o||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(o)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(o)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(o)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},async loadClassData(){try{this.loading=!0;const e=await Lr(this.classId);if(!e)throw new Error("Erro ao carregar dados da turma: resposta vazia");await this.processClassData(e),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`}catch{this.showErrorMessage("Erro ao carregar dados da turma. Alguns campos podem estar incompletos.")}finally{this.loading=!1}},async processClassData(e){const t=this.debugApiResponse(e);this.processBasicFields(t),t.optional_fields&&this.processOptionalFields(t.optional_fields),t.teachers&&Array.isArray(t.teachers)?await this.processTeachers(t.teachers):console.log("Nenhum professor encontrado na resposta ou formato inválido")},logFieldCheck(e){console.log("Verificando campos na resposta após extração:"),console.log("- classname:",e.classname),console.log("- name:",e.name),console.log("- enrol:",e.enrol),console.log("- enrol_type:",e.enrol_type),console.log("- offercourseid:",e.offercourseid),console.log("- startdate:",e.startdate)},processBasicFields(e){this.classData.classname=e.classname||e.name||"",this.classData.enrol=e.enrol||e.enrol_type||"",this.classData.offercourseid=e.offercourseid||this.offercourseid,this.classData.startdate=e.startdate},processOptionalFields(e){Object.keys(e).forEach(t=>{t in this.classData.optional_fields&&(this.classData.optional_fields[t]=e[t])}),this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationOptions.find(i=>i.value===parseInt(t))||{value:parseInt(t),label:`Situação ${t}`}))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>{const s=parseInt(t);return s===0?{value:s,label:"Inscrito"}:s===1?this.extensionSituationOptions.find(o=>o.value===s)||{value:s,label:"Em andamento"}:{value:s,label:`Situação ${t}`}}))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]},async processTeachers(e){this.classData.teachers=[...e];try{const t=await lp(this.offercourseid,this.classId);!t.error&&t.data&&(this.selectedTeachers=e.map(s=>{const i=t.data.find(o=>o.id===s);return{value:s,label:i?i.fullname:`Professor ${s}`}}))}catch{this.selectedTeachers=e.map(s=>({value:s,label:`Professor ${s}`}))}},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&console.warn("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(this.validate())try{this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));if(e.teachers=this.selectedTeachers.map(u=>u.value),!e.enrol){const u=this.route.query.enrol_type;u?(e.enrol=u,console.log("Usando tipo de inscrição da URL:",u)):(e.enrol="offer_manual",console.log("Usando tipo de inscrição padrão: offer_manual"))}console.log("Tipo de inscrição que será enviado:",e.enrol),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(u=>u.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(u=>u.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(u=>{const c=e.optional_fields[u];(c===0||c===null||c===""||c===void 0)&&(e.optional_fields[u]=void 0)}),"enrol_type"in e&&delete e.enrol_type,this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(u=>!e[u]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}e.offercourseid&&(e.offercourseid=parseInt(e.offercourseid));const o=e;let a;if(this.isEditing&&this.classId){o.offerclassid=this.classId;let u=await c1(o);!u.error&&u.data?(this.showSuccessMessage(u.data.message),this.loadClassData()):this.showErrorMessage(u.exception.message)}else if(a=await l1(o),!a.error&&a.data){this.showSuccessMessage(a.data.message);let u=a.data.offerclassid;this.isEditing=!0,this.classId=u,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:u,offerid:this.offerid||this.route.query.offerid||"0"}})}else this.showErrorMessage(a.exception.message)}catch(e){this.showErrorMessage(e.message||"Erro ao salvar turma")}finally{this.loading=!1}},goBack(){if(this.offerid)this.router.push({name:"editar-oferta",params:{id:this.offerid}});else if(this.offerCourse&&this.offerCourse.offerid)this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}});else{const e=this.route.query.offerid;e?this.router.push({name:"editar-oferta",params:{id:e}}):this.router.push({name:"listar-ofertas"})}},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},debugFields(){return{isEditing:this.isEditing,classId:this.classId,classname:this.classData.classname,enrol:this.classData.enrol,startdate:this.classData.startdate,teachers:this.classData.teachers,optional_fields:this.classData.optional_fields}},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){this.extensionSituationOptions.every(t=>this.extensionSituations.some(s=>s.value===t.value))?this.extensionSituations=[]:this.extensionSituations=[...this.extensionSituationOptions],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){this.situationOptions.every(t=>this.reenrolSituations.some(s=>s.value===t.value))?this.reenrolSituations=[]:this.reenrolSituations=[...this.situationOptions],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)},debugApiResponse(e){return e?e.error===!1&&e.data?e.data:e:null},tryAlternativeDataLoading(e){try{e.data&&typeof e.data=="object"&&e.data.id&&(e.data.classname&&(this.classData.classname=e.data.classname),e.data.enrol&&(this.classData.enrol=e.data.enrol),e.data.startdate&&(this.classData.startdate=e.data.startdate),e.data.optional_fields&&Object.keys(e.data.optional_fields).forEach(t=>{t in this.classData.optional_fields&&(this.classData.optional_fields[t]=e.data.optional_fields[t])}),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.classData.teachers=[...e.data.teachers]))}catch{}}}},sN={class:"new-class",ref:"classView"},rN={class:"page-header-container"},oN={key:0,class:"validation-alert"},nN={class:"section-container"},iN={class:"form-group mb-3"},aN={class:"label-with-help"},lN={class:"limited-width-input",style:{"max-width":"280px"}},uN={class:"form-row mb-3"},cN={class:"form-group"},dN={class:"label-with-help"},fN={class:"limited-width-input"},hN={class:"label-with-help"},pN={class:"input-with-checkbox"},mN={class:"limited-width-input"},gN={class:"form-row mb-3"},vN={class:"label-with-help"},_N={class:"label-with-help"},yN={key:2,class:"form-group"},bN={class:"form-group mb-3"},wN={class:"label-with-help"},EN={class:"limited-width-editor"},CN={class:"form-row mb-3"},DN={key:0,class:"form-group"},xN={class:"label-with-help"},SN={class:"limited-width-input"},ON={key:1,class:"form-group"},NN={class:"label-with-help"},IN={class:"limited-width-input"},AN={class:"form-group"},TN={class:"label-with-help"},MN={class:"limited-width-input"},PN={class:"form-row mb-3"},kN={class:"label-with-help"},RN={class:"input-with-checkbox"},VN={class:"limited-width-input"},FN={class:"section-container"},UN={class:"form-row mb-3"},LN={class:"label-with-help"},BN={class:"form-row mb-3"},$N={class:"limited-width-input"},jN={class:"form-row mb-3"},HN={class:"limited-width-input"},qN={class:"form-row mb-3"},zN={class:"limited-width-input"},WN={class:"limited-width-select"},GN={key:1,class:"section-container"},KN={class:"form-row mb-3"},QN={class:"form-group"},YN={class:"label-with-help"},ZN={class:"limited-width-select"},JN={class:"section-container"},XN={class:"form-group mb-3"},eI={class:"label-with-help"},tI={class:"limited-width-select"},sI={class:"actions-container"},rI={key:2,class:"loading"};function oI(e,t,s,i,o,a){const u=X("BackButton"),c=X("PageHeader"),f=X("HelpIcon"),m=X("CustomInput"),p=X("CustomCheckbox"),v=X("TextEditor"),w=X("CustomSelect"),D=X("Autocomplete"),k=X("CustomButton"),U=X("Toast"),te=y_("tooltip");return N(),I("div",sN,[h("div",rN,[A(c,{title:o.isEditing?"Editar turma":"Nova turma"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),o.validationAlert.show?(N(),I("div",oN,[t[31]||(t[31]=h("i",{class:"fas fa-exclamation-triangle"},null,-1)),h("span",null,G(o.validationAlert.message),1)])):ce("",!0),h("div",nN,[t[48]||(t[48]=h("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),h("div",iN,[h("div",aN,[t[32]||(t[32]=h("label",{class:"form-label"},"Nome da turma",-1)),t[33]||(t[33]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(f,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),h("div",lN,[A(m,{modelValue:o.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=T=>o.classData.classname=T),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":o.formErrors.classname.hasError,"error-message":o.formErrors.classname.message,onValidate:t[1]||(t[1]=T=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),h("div",uN,[h("div",cN,[h("div",dN,[t[34]||(t[34]=h("label",{class:"form-label"},"Data início da turma",-1)),t[35]||(t[35]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(f,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),h("div",fN,[A(m,{modelValue:o.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=T=>o.classData.startdate=T),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":o.formErrors.startdate.hasError,"error-message":o.formErrors.startdate.message,onValidate:t[3]||(t[3]=T=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableenddate}])},[h("div",hN,[t[36]||(t[36]=h("label",{class:"form-label"},"Data fim da turma",-1)),t[37]||(t[37]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(f,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),h("div",pN,[h("div",mN,[A(m,{modelValue:o.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=T=>o.classData.optional_fields.enddate=T),type:"date",width:180,disabled:!o.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":o.formErrors.enddate.hasError,"error-message":o.formErrors.enddate.message,onValidate:t[5]||(t[5]=T=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),A(p,{modelValue:o.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=T=>o.classData.optional_fields.enableenddate=T),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),h("div",gN,[o.classData.enrol=="offer_self"?(N(),I("div",{key:0,class:pe(["form-group",{disabled:!o.classData.optional_fields.enablepreenrolment}])},[h("div",vN,[t[38]||(t[38]=h("label",{class:"form-label"},"Data início pré-inscrição",-1)),A(f,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),A(m,{modelValue:o.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=T=>o.classData.optional_fields.preenrolmentstartdate=T),type:"date",width:180,disabled:!o.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":o.formErrors.preenrolmentstartdate.hasError,"error-message":o.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=T=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ce("",!0),o.classData.enrol=="offer_self"?(N(),I("div",{key:1,class:pe(["form-group",{disabled:!o.classData.optional_fields.enablepreenrolment}])},[h("div",_N,[t[39]||(t[39]=h("label",{class:"form-label"},"Data fim pré-inscrição",-1)),A(f,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),A(m,{modelValue:o.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=T=>o.classData.optional_fields.preenrolmentenddate=T),type:"date",width:180,disabled:!o.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":o.formErrors.preenrolmentenddate.hasError,"error-message":o.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=T=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ce("",!0),o.classData.enrol=="offer_self"?(N(),I("div",yN,[t[40]||(t[40]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"}," ")],-1)),A(p,{modelValue:o.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=T=>o.classData.optional_fields.enablepreenrolment=T),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):ce("",!0)]),h("div",bN,[h("div",wN,[t[41]||(t[41]=h("label",{class:"form-label"},"Descrição da turma",-1)),A(f,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),h("div",EN,[A(v,{modelValue:o.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=T=>o.classData.optional_fields.description=T),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),h("div",CN,[o.classData.enrol=="offer_self"?(N(),I("div",DN,[h("div",xN,[t[42]||(t[42]=h("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),A(f,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),h("div",SN,[A(m,{modelValue:o.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=T=>o.classData.optional_fields.minusers=T),type:"number",width:180},null,8,["modelValue"])])])):ce("",!0),o.classData.enrol=="offer_self"?(N(),I("div",ON,[h("div",NN,[t[43]||(t[43]=h("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),A(f,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),h("div",IN,[A(m,{modelValue:o.classData.optional_fields.maxusers,"onUpdate:modelValue":t[14]||(t[14]=T=>o.classData.optional_fields.maxusers=T),type:"number",width:180},null,8,["modelValue"])])])):ce("",!0),h("div",AN,[h("div",TN,[t[44]||(t[44]=h("label",{class:"form-label"},"Papel atribuído por padrão",-1)),t[45]||(t[45]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(f,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),h("div",MN,[A(w,{modelValue:o.classData.optional_fields.roleid,"onUpdate:modelValue":t[15]||(t[15]=T=>o.classData.optional_fields.roleid=T),options:o.roleOptions,width:180,required:"","has-error":o.formErrors.roleid.hasError,"error-message":o.formErrors.roleid.message,onValidate:t[16]||(t[16]=T=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),h("div",PN,[h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableenrolperiod}])},[h("div",kN,[t[46]||(t[46]=h("label",{class:"form-label"},"Prazo de conclusão da turma",-1)),t[47]||(t[47]=h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(f,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),h("div",RN,[h("div",VN,[A(m,{modelValue:o.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[17]||(t[17]=T=>o.classData.optional_fields.enrolperiod=T),type:"number",width:180,disabled:!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.enrolperiod.hasError,"error-message":o.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[18]||(t[18]=T=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),A(p,{modelValue:o.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[19]||(t[19]=T=>o.classData.optional_fields.enableenrolperiod=T),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)])]),h("div",FN,[h("div",UN,[h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[h("div",LN,[t[49]||(t[49]=h("label",{class:"form-label"},"Prorrogação de matrícula",-1)),A(f,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),bt(A(p,{modelValue:o.classData.optional_fields.enableextension,"onUpdate:modelValue":t[20]||(t[20]=T=>o.classData.optional_fields.enableextension=T),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!o.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,o.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),h("div",BN,[h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[50]||(t[50]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),h("div",$N,[A(m,{modelValue:o.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[21]||(t[21]=T=>o.classData.optional_fields.extensionperiod=T),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensionperiod.hasError,"error-message":o.formErrors.extensionperiod.message,onValidate:t[22]||(t[22]=T=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),h("div",jN,[h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[51]||(t[51]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),h("div",HN,[A(m,{modelValue:o.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[23]||(t[23]=T=>o.classData.optional_fields.extensiondaysavailable=T),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensiondaysavailable.hasError,"error-message":o.formErrors.extensiondaysavailable.message,onValidate:t[24]||(t[24]=T=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),h("div",qN,[h("div",{class:pe(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[52]||(t[52]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),h("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),h("div",zN,[A(m,{modelValue:o.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[25]||(t[25]=T=>o.classData.optional_fields.extensionmaxrequests=T),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensionmaxrequests.hasError,"error-message":o.formErrors.extensionmaxrequests.message,onValidate:t[26]||(t[26]=T=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),h("div",{class:pe(["form-group mb-3",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[53]||(t[53]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),h("div",WN,[A(D,{modelValue:o.extensionSituations,"onUpdate:modelValue":t[27]||(t[27]=T=>o.extensionSituations=T),items:o.extensionSituationOptions,placeholder:"Selecione as situações...",disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)]),o.classData.enrol=="offer_self"?(N(),I("div",GN,[h("div",KN,[h("div",QN,[h("div",YN,[t[54]||(t[54]=h("label",{class:"form-label"},"Habilitar rematrícula",-1)),A(f,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),A(p,{modelValue:o.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[28]||(t[28]=T=>o.classData.optional_fields.enablereenrol=T),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),h("div",{class:pe(["form-group mb-3",{disabled:!o.classData.optional_fields.enablereenrol}])},[t[55]||(t[55]=h("div",{class:"label-with-help"},[h("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),h("div",ZN,[A(D,{modelValue:o.reenrolSituations,"onUpdate:modelValue":t[29]||(t[29]=T=>o.reenrolSituations=T),items:o.situationOptions,placeholder:"Selecione as situações...",disabled:!o.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):ce("",!0),h("div",JN,[h("div",XN,[h("div",eI,[t[56]||(t[56]=h("label",{class:"form-label"},"Atribuir corpo docente",-1)),A(f,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),h("div",tI,[A(D,{class:"autocomplete-teachers",modelValue:o.selectedTeachers,"onUpdate:modelValue":t[30]||(t[30]=T=>o.selectedTeachers=T),items:o.teacherOptions,placeholder:"Pesquisar...",width:280,"show-all-option":!1,"auto-open":!1},null,8,["modelValue","items"])])])]),t[58]||(t[58]=h("div",{class:"required-fields-message"},[h("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),h("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),h("div",sI,[A(k,{variant:"primary",label:"Salvar",loading:o.loading,onClick:a.saveClass},null,8,["loading","onClick"]),A(k,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),o.loading?(N(),I("div",rI,t[57]||(t[57]=[h("div",{class:"spinner-border",role:"status"},[h("span",{class:"sr-only"},"Carregando...")],-1)]))):ce("",!0),A(U,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])],512)}const pp=ze(tN,[["render",oI],["__scopeId","data-v-6f3d422c"]]),nI=[{path:"/",name:"listar-ofertas",component:Jw,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:hp,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:hp,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid/:offerid",name:"NewClass",component:pp,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid/:offerid",name:"EditClass",component:pp,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:Rx,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],Hn=J0({history:u0("/local/offermanager/"),routes:nI,scrollBehavior(){return{top:0}}});Hn.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),Hn.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&Hn.push("/")});const IV="",iI=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{iI();const s=Zy(Pb);if(s.use(Ib()),s.use(Hn),t&&t.route){let o={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(o=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(o=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(t.offerid?o=`/new-class/${t.offercourseid}/${t.offerid}`:o=`/new-class/${t.offercourseid}/0`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(t.offerid?o=`/edit-class/${t.offercourseid}/${t.classid}/${t.offerid}`:o=`/edit-class/${t.offercourseid}/${t.classid}/0`),Hn.replace(o)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
