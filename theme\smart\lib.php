<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Theme functions.
 *
 * @package    theme_smart
 * @copyright  2023 Revvo - FMCorz.net
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use \tool_lfxp\helpers\user\role;

defined('MOODLE_INTERNAL') || die();

global $PAGE;

function theme_smart_reroutes($rerouter)
{
	global $COURSE, $PAGE;

	$importid = optional_param("importid", 0, PARAM_INT);
	$course_is_learningflix = $importid ? theme_smart_is_learningflix_course($importid) : $COURSE->is_learningflix;

	if (!is_siteadmin()) {
		// Check if a not admin user is trying to edit a learningflix course
		// redirect it to the referer page
		if ($course_is_learningflix) {
			$rerouter::register("/course/modedit.php", "/course/view.php?id={$COURSE->id}", false)
				->set_message(get_string("nopermissiontoviewpage", "core_error"), 0, \core\output\notification::NOTIFY_ERROR);

			$rerouter::register("/course/edit.php", "/course/view.php?id={$COURSE->id}", false)
				->set_message(get_string("nopermissiontoviewpage", "core_error"), 0, \core\output\notification::NOTIFY_ERROR);

			$rerouter::register("/course/completion.php", "/course/view.php?id={$COURSE->id}", false)
				->set_message(get_string("nopermissiontoviewpage", "core_error"), 0, \core\output\notification::NOTIFY_ERROR);

			$rerouter::register("/backup/import.php", "/course/view.php?id={$COURSE->id}", false)
				->set_message(get_string("nopermissiontoviewpage", "core_error"), 0, \core\output\notification::NOTIFY_ERROR);

			$rerouter::register("/question/*", "/course/view.php?id={$COURSE->id}", false)
				->set_message(get_string("nopermissiontoviewpage", "core_error"), 0, \core\output\notification::NOTIFY_ERROR);
		}

		// If user role is 'client' and tries to access the native course and category manager
		// redirects to the client's course management
		if (theme_smart_is_client()) {
			$rerouter::register("/course/management.php", "/admin/tool/coursemanagement/index.php", false);
		}
	}
}


/**
 * Post process the CSS tree.
 *
 * @param string $tree The CSS tree.
 * @param theme_config $theme The theme config object.
 */
function theme_smart_css_tree_post_processor($tree, $theme)
{
	error_log('theme_smart_css_tree_post_processor() is deprecated. Required' .
		'prefixes for Bootstrap are now in theme/smart/scss/moodle/prefixes.scss');
	$prefixer = new theme_smart\autoprefixer($tree);
	$prefixer->prefix();
}

/**
 * Inject additional SCSS.
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_smart_get_extra_scss($theme)
{
	$content = '';
	$imageurl = $theme->setting_file_url('backgroundimage', 'backgroundimage');

	// Sets the background image, and its settings.
	if (!empty($imageurl)) {
		$content .= '@media (min-width: 768px) {';
		$content .= 'body { ';
		$content .= "background-image: url('$imageurl'); background-size: cover;";
		$content .= ' } }';
	}

	// Sets the login background image.
	$loginbackgroundimageurl = $theme->setting_file_url('loginbackgroundimage', 'loginbackgroundimage');
	if (!empty($loginbackgroundimageurl)) {
		$content .= 'body.pagelayout-login #page { ';
		$content .= "background-image: url('$loginbackgroundimageurl'); background-size: cover;";
		$content .= ' }';
	}

	// Get scss from course formats (deprecated)
	//$content .= theme_smart_get_courseformats_scss_content();

	// Always return the background image with the scss when we have it.
	return !empty($theme->settings->scss) ? $theme->settings->scss . ' ' . $content : $content;
}

/**
 * Serves any files associated with the theme settings.
 *
 * @param stdClass $course
 * @param stdClass $cm
 * @param context $context
 * @param string $filearea
 * @param array $args
 * @param bool $forcedownload
 * @param array $options
 * @return bool
 */
function theme_smart_pluginfile($course, $cm, $context, $filearea, $args, $forcedownload, array $options = array())
{
	if ($context->contextlevel == CONTEXT_SYSTEM && ($filearea === 'logo' || $filearea === 'backgroundimage' ||
		$filearea === 'loginbackgroundimage' || $filearea === 'footerlogo' || $filearea === 'loginlogo' || $filearea === 'policy')) {
		$theme = theme_config::load('smart');
		// By default, theme files must be cache-able by both browsers and proxies.
		if (!array_key_exists('cacheability', $options)) {
			$options['cacheability'] = 'public';
		}
		return $theme->setting_file_serve($filearea, $args, $forcedownload, $options);
	} else {
		send_file_not_found();
	}
}

/**
 * Returns the main SCSS content.
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_smart_get_main_scss_content($theme)
{
	global $CFG;

	$scss = '';
	$filename = !empty($theme->settings->preset) ? $theme->settings->preset : null;
	$fs = get_file_storage();

	$context = context_system::instance();
	if ($filename == 'default.scss') {
		$scss .= file_get_contents($CFG->dirroot . '/theme/smart/scss/preset/default.scss');
	} else if ($filename == 'plain.scss') {
		$scss .= file_get_contents($CFG->dirroot . '/theme/smart/scss/preset/plain.scss');
	} else if ($filename && ($presetfile = $fs->get_file($context->id, 'theme_smart', 'preset', 0, '/', $filename))) {
		$scss .= $presetfile->get_content();
	} else {
		// Safety fallback - maybe new installs etc.
		$scss .= file_get_contents($CFG->dirroot . '/theme/smart/scss/preset/default.scss');
	}


	return $scss;
}

/**
 * CUSTOM
 * Get scss from course formats when exists.
 *
 * @return string
 */
function theme_smart_get_courseformats_scss_content()
{
	global $CFG;

	// DEPRECATED
	return null;

	require_once($CFG->dirroot . '/course/lib.php');

	$scss = "";

	foreach (get_sorted_course_formats() as $format) {
		$scssFile = $CFG->dirroot . "/course/format/" . $format . "/scss/theme.scss";

		if (file_exists($scssFile)) {
			$scss .= file_get_contents($scssFile);
		}
	}

	return $scss;
}

/**
 * Get compiled css.
 *
 * @return string compiled css
 */
function theme_smart_get_precompiled_css()
{
	global $CFG;
	return file_get_contents($CFG->dirroot . '/theme/smart/style/moodle.css');
}

/**
 * Get SCSS to prepend.
 *
 * @param theme_config $theme The theme config object.
 * @return array
 */
function theme_smart_get_pre_scss($theme)
{
	global $CFG;

	$scss = '';
	$configurable = [
		// Config key => [variableName, ...].
		'brandcolor' => ['primary'],
		'btn_primary_bg_color' => ['btn_primary_bg_color'],
		'btn_primary_text_color' => ['btn_primary_text_color'],
	];

	// Prepend variables first.
	foreach ($configurable as $configkey => $targets) {
		$value = isset($theme->settings->{$configkey}) ? $theme->settings->{$configkey} : null;
		if (empty($value)) {
			continue;
		}
		array_map(function ($target) use (&$scss, $value) {
			$scss .= '$' . $target . ': ' . $value . ";\n";
		}, (array) $targets);
	}

	// Prepend pre-scss.
	if (!empty($theme->settings->scsspre)) {
		$scss .= $theme->settings->scsspre;
	}

	return $scss;
}

/**
 * Get course format section name.
 *
 * @param $course The course object.
 * @param $plural
 * @return array
 */
function theme_smart_get_course_format_section_name($course, $plural = false)
{
	$courseformat = course_get_format($course);

	if (method_exists($courseformat, 'get_custom_section_name')) {
		return $courseformat->get_custom_section_name($plural);
	}

	if (get_string_manager()->string_exists('sectionname', 'format_' . $courseformat->get_format())) {
		if (!$plural) {
			return get_string("sectionname", "format_{$course->format}");
		}

		return get_string("sectionname", "format_{$course->format}") . 's';
	}

	return get_string("sections");
}

function theme_smart_add_custom_langstring(string $lang, string $componentname, string $stringid, string $stringoriginal, string $stringmaster)
{
	global $DB, $CFG;

	$componentid = $DB->get_field('tool_customlang_components', "id", ["name" => $componentname]);

	if (!$componentid) {
		return false;
	}

	$stringexists = $DB->record_exists('tool_customlang', ["stringid" => $stringid, "componentid" => $componentid]);

	if ($stringexists) {
		return false;
	}

	$now = time();

	$record               	= new stdclass();
	$record->lang         	= $lang;
	$record->componentid  	= $componentid;
	$record->stringid     	= $stringid;
	$record->original     	= $stringoriginal;
	$record->master       	= $stringmaster;
	$record->timemodified 	= $now;
	$record->outdated     	= 0;
	$record->local          = null;
	$record->timecustomized = null;
	$record->modified 		= 1;

	$DB->insert_record('tool_customlang', $record);
	theme_smart_customlang_checkin($lang, $componentname);

	return true;
}

function theme_smart_customlang_checkin($lang, $componentname)
{
	global $DB, $USER, $CFG;
	require_once($CFG->libdir . '/filelib.php');

	if ($lang !== clean_param($lang, PARAM_LANG)) {
		return false;
	}

	list($insql, $inparams) = $DB->get_in_or_equal([$componentname]);

	// Get all customized strings from updated valid components.
	$sql = "SELECT s.*, c.name AS component
			  FROM {tool_customlang} s
			  JOIN {tool_customlang_components} c ON s.componentid = c.id
			 WHERE s.lang = ?
				   AND (s.local IS NOT NULL OR s.modified = 1)
				   AND c.name $insql
		  ORDER BY componentid, stringid";
	array_unshift($inparams, $lang);
	$strings = $DB->get_records_sql($sql, $inparams);

	$files = array();
	foreach ($strings as $string) {
		if (!is_null($string->local)) {
			$files[$string->component][$string->stringid] = $string->local;
		}
	}

	fulldelete($CFG->langlocalroot . '/' . $lang . '_local');

	foreach ($files as $component => $strings) {
		theme_smart_dump_strings($lang, $component, $strings);
	}

	$DB->set_field_select('tool_customlang', 'modified', 0, 'lang = ?', array($lang));
	$sm = get_string_manager();
	$sm->reset_caches();
}

function theme_smart_dump_strings($lang, $component, $strings)
{
	global $CFG;

	if ($lang !== clean_param($lang, PARAM_LANG)) {
		throw new moodle_exception('Unable to dump local strings for non-installed language pack .' . s($lang));
	}
	if ($component !== clean_param($component, PARAM_COMPONENT)) {
		throw new coding_exception('Incorrect component name');
	}
	if (!$filename = $component . ".php") {
		throw new moodle_exception('Unable to find the filename for the component ' . s($component));
	}
	if ($filename !== clean_param($filename, PARAM_FILE)) {
		throw new coding_exception('Incorrect file name ' . s($filename));
	}
	list($package, $subpackage) = core_component::normalize_component($component);
	$packageinfo = " * @package    $package";
	if (!is_null($subpackage)) {
		$packageinfo .= "\n * @subpackage $subpackage";
	}
	$filepath = $CFG->langlocalroot . '/' . $lang . '_local';
	$filepath = $filepath . '/' . $filename;
	if (!is_dir(dirname($filepath))) {
		check_dir_exists(dirname($filepath));
	}

	if (!$f = fopen($filepath, 'w')) {
		throw new moodle_exception('Unable to write ' . s($filepath));
	}
	fwrite(
		$f,
		<<<EOF
<?php

// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
* Local language pack from $CFG->wwwroot
*
$packageinfo
* @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
*/

defined('MOODLE_INTERNAL') || die();


EOF
	);

	foreach ($strings as $stringid => $text) {
		if ($stringid !== clean_param($stringid, PARAM_STRINGID)) {
			debugging('Invalid string identifier ' . s($stringid));
			continue;
		}
		fwrite($f, '$string[\'' . $stringid . '\'] = ');
		fwrite($f, var_export($text, true));
		fwrite($f, ";\n");
	}
	fclose($f);
	@chmod($filepath, $CFG->filepermissions);
}

/**
 * Checks if a user is a client admin.
 *
 * Do not use this. Its just here to keep compatibility
 * @see tool_lfxp_is_client()
 *
 * @param integer $userid
 * @return bool
 */
function theme_smart_is_client(int $userid = 0)
{
	global $CFG;
	require_once($CFG->dirroot . '/admin/tool/lfxp/lib/userlib.php');
	return tool_lfxp_is_client($userid);
}

/**
 * Checks if a given user has a specific role.
 *
 * @param string $roleshortname The shortname of the role to check
 * @param integer $userid The user ID to check, defaults to current user
 * @return bool True if the user has the role, false otherwise
 */
function theme_smart_has_role(string $roleshortname, int $userid = 0)
{
	global $USER;
	$userid = $userid ?: $USER->id;
	$roles = get_user_roles(\context_system::instance(), $userid, false);
	$shortnames = array_column($roles, 'shortname');
	return array_search($roleshortname, $shortnames) !== false;
}

function theme_smart_add_client_role()
{
	global $CFG;

	$xml_path = $CFG->dirroot . "/theme/smart/db/role_client.xml";

	$roleid = role::create_role_from_xml($xml_path);

	if ($roleid) {
		mtrace('Papel Cliente criado com id ' . $roleid);
	} else {
		mtrace('Não foi possível criar o Papel Cliente');
	}
}

function theme_smart_update_client_role($options, $roleid)
{
	global $CFG;

	$resettype = "client";
	$systemcontext = context_system::instance();
	$xml = file_get_contents($CFG->dirroot . "/theme/smart/db/role_client.xml");

	$definitiontable = new core_role_define_role_table_advanced($systemcontext, $roleid);

	if (is_number($resettype)) {
		// Duplicate the role.
		$definitiontable->force_duplicate($resettype, $options);
	} else {
		// Must be an archetype.
		$definitiontable->force_archetype($resettype, $options);
	}

	$definitiontable->force_preset($xml, $options);
	$definitiontable->read_submitted_permissions();
	$definitiontable->save_changes();
}

function theme_smart_is_learningflix_course(int $courseid)
{
	global $DB;

	return $DB->get_field_sql("
		SELECT cfd.value
		FROM {customfield_data} cfd
		JOIN {customfield_field} cff ON (cff.id = cfd.fieldid)
		WHERE cff.shortname = 'learningflix_course'
		AND cfd.instanceid = {$courseid}
		AND cfd.value = 1
	");
}

/**
 * Returns the default homepage for the current user.
 *
 * @return string The URL of the default homepage.
 */
function theme_smart_get_home_page()
{
	global $CFG;

	switch ($CFG->defaulthomepage) {
		case HOMEPAGE_MY:
			return $CFG->wwwroot . '/my/';
			break;

		case HOMEPAGE_MYCOURSES && !isguestuser():
			return $CFG->wwwroot . '/my/courses.php';
			break;

		case HOMEPAGE_SITE:
		case HOMEPAGE_USER:
		default:
			return $CFG->wwwroot . '/';
	}
}

function theme_smart_add_menubar_icon(){

	global $PAGE, $DB;
	$gallery = $DB->get_records_menu("course", ["format" => "gallery", "visible" => 1], "",  "id, fullname", 0, 1);

		if(empty($gallery)){
			return false;
		}

	$galleryID = array_keys($gallery)[0];

	return (object)[

		"name" => current($gallery),
		"icon" => 'fa fa-play',
		"url" => new \moodle_url("/course/view.php", ["id"=>$galleryID]),
		"active" => $PAGE->pagetype == "local-catalog" ? 'active' : '',
		"order" => 2
	];
}

/**
 * Adiciona o script de chat do Tawk.to apenas para usuários logados
 *
 * @return string HTML do script de chat ou string vazia
 */
function theme_smart_before_footer() {
	if (isloggedin() && !isguestuser()) {
		return '
					<!--Start of Tawk.to Script-->
					<script type="text/javascript">
					var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
					(function(){
					var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
					s1.async=true;
					s1.src=\'https://embed.tawk.to/64d67d65cc26a871b02eb866/1h7itjlms\';
					s1.charset=\'UTF-8\';
					s1.setAttribute(\'crossorigin\',\'*\');
					s0.parentNode.insertBefore(s1,s0);
					})();
					</script>
					<!--End of Tawk.to Script-->
		';
	}

	return '';
}
