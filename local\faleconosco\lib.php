<?php

/**
 * @package local_faleconosco
 * @copyright 2025 REVVO <www.somosrevvo.com.br>
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Adiciona ícone "Fale Conosco" na barra de menu
 *
 * @return object|false
 */
function local_faleconosco_add_menubar_icon()
{
    global $PAGE;
    
    // Verifica se o plugin está habilitado
    if (!get_config("local_faleconosco", "enabled")) {
        return false;
    }
    
    // Não exibe para usuários convidados
    if (isguestuser()) {
        return false;
    }
    
    return (object)[
        "name" => get_string('pluginname', 'local_faleconosco'),
        "icon" => 'fas fa-headset',
        "url" => "https://epicbrasil.atlassian.net/servicedesk/customer/portal/122",
        "active" => "",
        "order" => 9,
        "target" => "_blank"
    ];
}
