{{!
    @template theme_smart/menubar
	
	This template is called by theme_smart/default
}}

<section id="menubar" class="d-none d-lg-block">
	<div class="aria-live-polite" aria-live="polite" style="position: absolute; left: -9999px;"></div>
	<div class="menubar-inner d-flex flex-column align-items-center justify-content-between h-100 py-3 position-fixed">
		{{>theme_smart/logo}}
		
		{{#output.menubar}}
			<div class="menubar-menu d-flex flex-column align-items-center w-100 mb-auto">
				{{#menuitems}}
					{{#divider}}
						<div class="divider"></div>
					{{/divider}}

					{{^has_submenu}}
					<a class="menu-item btn btn-link w-100 px-3 {{class}} {{active}}" href="{{{url}}}" {{#index}}data-index="{{index}}"{{/index}}
					{{#target}}target="{{target}}"{{/target}} aria-label="{{name}}">
							{{#icon}}<i class="menu-icon {{.}}"></i>{{/icon}}
							<div class="sidebar-hover">{{name}}</div>
					</a>
					{{/has_submenu}}
					
					{{#has_submenu}}
						<div class="dropleft menu-submenu {{active}}">
							<a class="menu-item btn btn-link w-100 px-3" href="#" data-placement="right" data-toggle="dropdown">
								{{#icon}}<i class="menu-icon {{.}}"></i>{{/icon}}
								<div class="sidebar-hover">{{name}}</div>
							</a>
							
							<div class="dropdown-menu">
								{{#submenu}}
									<a class="dropdown-item {{active}}" href="{{{url}}}">{{name}}</a>
								{{/submenu}}
							</div>
						</div>				
					{{/has_submenu}}

				{{/menuitems}}
				
				{{#output.show_edit_switch}}
					{{{ output.edit_switch }}}
				{{/output.show_edit_switch}}

				<div class="dropleft menu-submenu menu-moreitems d-none">
					<a class="menu-item btn btn-link w-100 px-3" href="{{{url}}}" data-placement="right" data-toggle="dropdown">
						<i class="menu-icon fa fa-ellipsis"></i>
						<div class="sidebar-hover text-capitalize" style="margin-left:-0.5rem">{{#str}}more{{/str}}</div>
					</a>
					
					<div class="dropdown-menu">
						{{#menuitems}}
							{{#index}}
								<a class="dropdown-item {{active}}" href="{{{url}}}">{{name}}</a>
							{{/index}}
						{{/menuitems}}
					</div>
				</div>				
			</div>
		{{/output.menubar}}
		
		<div class="menubar-base">
			{{{ output.navbar_plugin_output }}}
			
			<div class="d-flex align-items-stretch usermenu-container" data-region="usermenu">
				{{#usermenu}}
					{{> core/user_menu }}
				{{/usermenu}}
			</div>
		</div>
	</div>
</section>

<section id="menubar-mobile" class="d-lg-none bg-glassy-dark w-100 position-fixed">
	<div class="d-flex flex-column align-items-center justify-content-between w-100 h-100">		
		{{#output.menubar}}
			<div class="menubar-menu align-items-center w-100 h-100 custom-link">
				{{#menuitems}}
					{{#divider}}
						<div class="divider"></div>
					{{/divider}}

					<a class="menu-item text-center py-2 px-3 {{class}} {{active}}"
					   {{^has_submenu}}href="{{{url}}}" {{#target}}target="{{target}}"{{/target}}{{/has_submenu}}
					   {{#has_submenu}}
					   data-toggle="collapse"
					   data-target=".submenu-{{index}}"
					   {{/has_submenu}}
					>
						{{#icon}}<i class="menu-icon {{.}}"></i>{{/icon}}
						{{#pix_icon}} {{{pix_icon}}} {{/pix_icon}}
						<span class="live">{{name}}</span>
					</a>
					
					{{#has_submenu}}
						<div class="collapse submenu-{{index}} pl-4 mb-2">
						{{#submenu}}
							<a class="menu-item text-center small {{class}} {{active}}" href="{{{url}}}" style="padding:0.3rem !important">
								{{#icon}}<i class="menu-icon {{.}}"></i>{{/icon}}
								<span class="live">{{name}}</span>
							</a>
						{{/submenu}}
						</div>				
					{{/has_submenu}}

				{{/menuitems}}

				<a class="menu-item expand-menu text-center py-2 px-3 {active}}" href="#">
					<i class="menu-icon fa-solid fa-bars"></i>
					<span class="live text-capitalize">{{#str}}more{{/str}}</span>
				</a>

				{{{ output.edit_switch }}}
			</div>
		{{/output.menubar}}
		
		<a href="#" class="retract-menu btn btn-lg btn-light rounded-circle mb-4"><i class="fa fa-xmark"></i></a>
	</div>
</section>

{{#js}}
	require(['jquery'], function($) {
		
		$(document).on("click", ".expand-menu", function(e){
			e.preventDefault();
			$("#menubar-mobile").addClass("expanded");
			$("html").css("overflow", "hidden");
		}).on("click", ".retract-menu", function(e){
			e.preventDefault();
			$("#menubar-mobile").removeClass("expanded");
			$("#menubar-mobile .collapse.show").removeClass("show");
			$("html").css("overflow", "auto");
		});
		
		var resizeEvent;
		$("#menubar .menu-moreitems").insertAfter($("#menubar .menu-item[data-index]").last());
		
		function adjustMenuItems(){
			$("#menubar .menubar-menu").addClass("resizing");
			$("#menubar .menu-moreitems").addClass("d-none");
			$("#menubar .menubar-menu .menu-item").removeClass("d-none");
			$("#menubar .menu-moreitems .dropdown-item").removeClass("d-none");
			
			let screenHeight = $(window).height();
			let menuHeight = $("#menubar .menubar-menu")[0].scrollHeight + $("#menubar .menubar-inner").outerHeight() - $("#menubar .menubar-menu").outerHeight() - 10;
			let itemHeight = $("#menubar .menu-item").outerHeight();
			let totalItems = $("#menubar .menu-item[data-index]").length;
			
			if(menuHeight > screenHeight){
				let diffHeight = menuHeight - screenHeight;
				let lastItems = Math.ceil(diffHeight/itemHeight)+1;
				let firstItems = totalItems - lastItems;
				
				$("#menubar .menu-item[data-index]").slice(-lastItems).addClass("d-none");
				$("#menubar .menu-moreitems .dropdown-item").slice(0, firstItems).addClass("d-none")	;
				$("#menubar .menu-moreitems").removeClass("d-none");
			}
			
			clearTimeout(resizeEvent);
			
			resizeEvent = setTimeout(function(){
				$("#menubar .menubar-menu").removeClass("resizing");
			}, 500);
		}

		adjustMenuItems();
		
		$(window).on("resize", adjustMenuItems);
	});	
{{/js}}